import { useState, useEffect, useCallback } from 'react'
import {
  Fi<PERSON>ser,
  FiCalendar,
  FiShoppingBag,
  FiHeart,
  FiSettings,
  FiClock,
  FiMapPin,
  FiPhone,
  FiMail,
  FiEdit3,
  FiTrash2,
  FiStar,
  FiX,
  FiAlertCircle
} from 'react-icons/fi'
import {
  userService,
  appointmentService,
  orderService,
  cartService,
  notificationService
} from '../../services'

import { useBranding } from '../../contexts/BrandingContext'
import { useToast } from '../../contexts/ToastContext'
import { useCart } from '../../contexts/CartContext'
import { useFavorites } from '../../contexts/FavoritesContext'
import { useRouter } from '../../hooks/useRouter'
import Loading from '../../components/Loading'
import AppointmentModal from '../../components/Modals/AppointmentModal'
import AppointmentDetailModal from '../../components/Modals/AppointmentDetailModal'
import UserPaymentConfirmations from './User/UserPaymentConfirmations'
import UserAppointments from './User/UserAppointments'
import UserOrders from './User/UserOrders'
import UserFavorites from './User/UserFavorites'
import ReviewForm from '../../components/Reviews/ReviewForm'
import DashboardLayout from './Shared/DashboardLayout'

const UserDashboard = ({ onNavigate, onLogout }) => {
  const { branding } = useBranding()
  const { showSuccess, showError } = useToast()
  const { addToCart: addToCartContext } = useCart()
  const { favorites: userFavorites, removeFromFavorites } = useFavorites()
  const { parseCurrentRoute, navigateToSubRoute } = useRouter()

  // Initialize activeTab from URL if available
  const [activeTab, setActiveTab] = useState(() => {
    const { mainRoute, subRoute } = parseCurrentRoute()
    return (mainRoute === 'user-dashboard' && subRoute) ? subRoute : 'overview'
  })

  const [userData, setUserData] = useState(null)
  const [appointments, setAppointments] = useState([])
  const [orders, setOrders] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [isUpdating, setIsUpdating] = useState(false)

  // Loading states for different sections
  const [sectionLoading, setSectionLoading] = useState({
    appointments: false,
    orders: false,
    favorites: false,
    notifications: false
  })

  // Data loading tracking
  const [loadedData, setLoadedData] = useState({
    appointments: false,
    orders: false,
    favorites: false,
    notifications: false
  })

  // Modal states for appointment booking
  const [showAppointmentModal, setShowAppointmentModal] = useState(false)
  const [showAppointmentDetailModal, setShowAppointmentDetailModal] = useState(false)
  const [appointmentDetailItem, setAppointmentDetailItem] = useState(null)
  const [modalType, setModalType] = useState('add')
  const [editingAppointment, setEditingAppointment] = useState(null)
  const [viewingAppointment, setViewingAppointment] = useState(null)
  const [confirmDialog, setConfirmDialog] = useState(null)

  // Review modal states
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [reviewingService, setReviewingService] = useState(null)
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: ''
  })

  // Load user data on component mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const response = await userService.getProfile()
        if (response.success) {
          setUserData(response.data)
          setProfileData({
            firstName: response.data.firstName || '',
            lastName: response.data.lastName || '',
            email: response.data.email || '',
            phone: response.data.phone || '',
            address: response.data.address || '',
            city: response.data.city || '',
            state: response.data.state || '',
            zipCode: response.data.zipCode || ''
          })
        }
      } catch (error) {
        console.error('Error loading user data:', error)
        setError('Failed to load user data')
      } finally {
        setIsLoading(false)
      }
    }

    loadUserData()
  }, [])

  // Load appointments data
  const loadAppointments = useCallback(async () => {
    if (loadedData.appointments) return

    try {
      setSectionLoading(prev => ({ ...prev, appointments: true }))
      const response = await appointmentService.getUserAppointments()

      if (response.success) {
        setAppointments(Array.isArray(response.data) ? response.data : [])
        setLoadedData(prev => ({ ...prev, appointments: true }))
      }
    } catch (error) {
      console.error('Error loading appointments:', error)
      if (activeTab === 'appointments') {
        showError(`Failed to load appointments: ${error.response?.data?.message || error.message || 'Unknown error'}`)
      }
    } finally {
      setSectionLoading(prev => ({ ...prev, appointments: false }))
    }
  }, [loadedData.appointments, activeTab])

  // Load orders data
  const loadOrders = useCallback(async () => {
    if (loadedData.orders) return

    try {
      setSectionLoading(prev => ({ ...prev, orders: true }))
      // For now, set empty array - you can implement actual API call later
      setOrders([])
      setLoadedData(prev => ({ ...prev, orders: true }))
    } catch (error) {
      console.error('Error loading orders:', error)
      if (activeTab === 'orders') {
        showError(`Failed to load orders: ${error.response?.data?.message || error.message || 'Unknown error'}`)
      }
    } finally {
      setSectionLoading(prev => ({ ...prev, orders: false }))
    }
  }, [loadedData.orders, activeTab])

  // Load favorites data (using context, so just mark as loaded)
  const loadFavorites = useCallback(async () => {
    if (loadedData.favorites) return

    try {
      setSectionLoading(prev => ({ ...prev, favorites: true }))
      // Favorites are loaded from context, so just mark as loaded
      setLoadedData(prev => ({ ...prev, favorites: true }))
    } catch (error) {
      console.error('Error loading favorites:', error)
      if (activeTab === 'favorites') {
        showError(`Failed to load favorites: ${error.response?.data?.message || error.message || 'Unknown error'}`)
      }
    } finally {
      setSectionLoading(prev => ({ ...prev, favorites: false }))
    }
  }, [loadedData.favorites, activeTab])

  // Load all essential data on dashboard mount
  useEffect(() => {
    const loadAllData = async () => {
      try {
        setIsLoading(true)

        // Load appointments first (needed for overview)
        await loadAppointments()

        // Load other data in parallel
        await Promise.all([
          loadOrders(),
          loadFavorites()
        ])

      } catch (error) {
        console.error('Error loading dashboard data:', error)
        showError('Failed to load dashboard data')
      } finally {
        setIsLoading(false)
      }
    }

    loadAllData()
  }, [loadAppointments, loadOrders, loadFavorites]) // Include dependencies

  // Load additional data when switching tabs (if not already loaded)
  useEffect(() => {
    if (activeTab === 'appointments' && !loadedData.appointments) {
      loadAppointments()
    } else if (activeTab === 'orders' && !loadedData.orders) {
      loadOrders()
    } else if (activeTab === 'favorites' && !loadedData.favorites) {
      loadFavorites()
    }
  }, [activeTab, loadedData, loadAppointments, loadOrders, loadFavorites])

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab)
    navigateToSubRoute('user-dashboard', tab)
  }

  // Handle appointment cancellation
  const handleCancelAppointment = async (appointmentId) => {
    try {
      const response = await appointmentService.cancelAppointment(appointmentId)

      if (response.success) {
        // Update local state
        setAppointments(prev => prev.filter(apt => (apt._id || apt.id) !== appointmentId))

        // Show success message
        showSuccess('Appointment cancelled successfully!')
      } else {
        showError('Failed to cancel appointment: ' + (response.message || 'Unknown error'))
      }
    } catch (error) {
      console.error('Error cancelling appointment:', error)
      showError(`Failed to cancel appointment: ${error.response?.data?.message || error.message || 'Unknown error'}`)
    }
  }

  // Handle review service
  const handleReviewService = (appointment) => {
    setReviewingService({
      serviceId: appointment.service?._id || appointment.service,
      serviceName: appointment.service?.name || appointment.serviceName || 'Service',
      appointmentId: appointment._id || appointment.id
    })
    setShowReviewModal(true)
  }

  const handleDeleteFromDetail = async (appointmentId) => {
    await handleCancelAppointment(appointmentId)
    setShowAppointmentDetailModal(false)
  }

  // Handle appointment save
  const handleSaveAppointment = async (appointmentData) => {
    try {
      setIsUpdating(true)
      let response

      // Prepare data for API
      const apiData = {
        ...appointmentData,
        customerInfo: {
          name: `${appointmentData.firstName} ${appointmentData.lastName}`,
          phone: appointmentData.phone,
          email: appointmentData.email
        }
      }

      if (editingAppointment) {
        // Update existing appointment
        response = await appointmentService.updateAppointment(
          editingAppointment._id || editingAppointment.id,
          appointmentData
        )

        if (response.success) {
          // Update local state
          setAppointments(prev => prev.map(apt =>
            (apt._id || apt.id) === (editingAppointment._id || editingAppointment.id)
              ? { ...apt, ...response.data }
              : apt
          ))

          showSuccess('Appointment updated successfully!')
        }
      } else {
        // Create new appointment
        response = await appointmentService.createAppointment(apiData)

        if (response.success) {
          // Add to local state
          setAppointments(prev => [response.data, ...prev])
          showSuccess('Appointment booked successfully!')
        }
      }

      if (response.success) {
        setShowAppointmentModal(false)
        setEditingAppointment(null)
        setModalType('add')
      } else {
        throw new Error(response.message || 'Failed to save appointment')
      }
    } catch (error) {
      console.error('Error saving appointment:', error)
      showError(`Failed to save appointment: ${error.response?.data?.message || error.message || 'Unknown error'}`)
    } finally {
      setIsUpdating(false)
    }
  }

  // Handle profile update
  const handleProfileChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleProfileSave = async () => {
    try {
      setIsUpdating(true)
      const response = await userService.updateProfile(profileData)

      if (response.success) {
        setUserData(response.data)
        showSuccess('Profile updated successfully!')
      } else {
        throw new Error(response.message || 'Failed to update profile')
      }
    } catch (error) {
      console.error('Error updating profile:', error)
      showError(`Failed to update profile: ${error.response?.data?.message || error.message || 'Unknown error'}`)
    } finally {
      setIsUpdating(false)
    }
  }

  // Helper functions


  const formatTime = (timeString) => {
    if (!timeString) return 'Time not set'

    try {
      // Handle different time formats
      let time
      if (timeString.includes(':')) {
        // Format: "HH:MM" or "HH:MM:SS"
        const [hours, minutes] = timeString.split(':')
        time = new Date()
        time.setHours(parseInt(hours), parseInt(minutes), 0, 0)
      } else {
        // Try to parse as a full date/time string
        time = new Date(timeString)
      }

      // Format to 12-hour format
      return time.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch (error) {
      console.error('Error formatting time:', error)
      return timeString // Return original if formatting fails
    }
  }

  const formatFullDate = (dateString) => {
    if (!dateString) return 'Date not set'

    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch (error) {
      console.error('Error formatting date:', error)
      return dateString // Return original if formatting fails
    }
  }

  const getNextAppointment = () => {
    if (!appointments || !Array.isArray(appointments)) {
      return null
    }
    
    const upcoming = appointments.filter(apt =>
      new Date(apt.date) > new Date() &&
      (apt.status === 'confirmed' || apt.status === 'scheduled')
    ).sort((a, b) => new Date(a.date) - new Date(b.date))

    return upcoming[0]
  }

  const addToCart = (product) => {
    addToCartContext(product)
  }

  // Sidebar items configuration
  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: FiUser },
    { id: 'appointments', label: 'Appointments', icon: FiCalendar },
    { id: 'orders', label: 'Orders', icon: FiShoppingBag },
    { id: 'favorites', label: 'Favorites', icon: FiHeart },
    { id: 'payment-confirmations', label: 'Payment History', icon: FiClock },
    { id: 'profile', label: 'Profile', icon: FiSettings }
  ].map(tab => ({
    ...tab,
    description: tab.id === 'overview' ? 'Dashboard overview' :
                 tab.id === 'appointments' ? 'Manage appointments' :
                 tab.id === 'orders' ? 'Order history' :
                 tab.id === 'favorites' ? 'Favorite products' :
                 tab.id === 'payment-confirmations' ? 'Payment history' :
                 tab.id === 'profile' ? 'Edit profile' : ''
  }))

  const renderOverview = () => {
    const nextAppointment = getNextAppointment()

    return (
      <div className="space-y-4 lg:space-y-6">
        {/* Welcome Section */}
        <div className="relative rounded-2xl p-6 text-white overflow-hidden transform hover:scale-[1.01] transition-all duration-300 shadow-xl border border-white/20"
             style={{ background: `linear-gradient(135deg, ${branding.colors.primary}, ${branding.colors.secondary})` }}>
          <div className="relative z-10">
            <h1 className="text-2xl lg:text-3xl font-bold mb-2">
              Welcome back, {userData?.firstName || 'User'}! 👋
            </h1>
            <p className="text-white/90 text-base lg:text-lg">
              Ready to take care of your hair today?
            </p>
          </div>
          <div className="absolute top-0 right-0 w-32 h-32 lg:w-40 lg:h-40 opacity-20">
            <div className="w-full h-full rounded-full bg-white/20 transform translate-x-8 -translate-y-8"></div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {/* Next Appointment */}
          <div className="bg-gradient-to-br from-white via-blue-50/30 to-white rounded-2xl p-4 lg:p-6 shadow-xl border border-blue-100/50 backdrop-blur-sm transform hover:scale-[1.02] hover:shadow-2xl transition-all duration-300">
            <div className="flex items-center space-x-3 mb-3 lg:mb-4">
              <div className="p-2 lg:p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                <FiCalendar className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
              </div>
              <h3 className="text-lg lg:text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">Next Appointment</h3>
            </div>
            {sectionLoading.appointments ? (
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
                <div className="h-6 bg-gray-200 rounded w-20"></div>
              </div>
            ) : nextAppointment ? (
              <div className="space-y-2">
                <p className="font-bold text-gray-800 text-sm lg:text-base">{nextAppointment.service?.name || 'Service'}</p>
                <p className="text-gray-600 text-xs lg:text-sm">
                  {formatFullDate(nextAppointment.date)} at {formatTime(nextAppointment.time)}
                </p>
                <span className="inline-block px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 rounded-full text-xs font-medium shadow-sm">
                  {nextAppointment.status}
                </span>
              </div>
            ) : (
              <div className="text-center py-4 lg:py-6">
                <p className="text-gray-500 text-sm lg:text-base font-medium">No upcoming appointments</p>
                <button
                  onClick={() => {
                    setShowAppointmentModal(true)
                    setModalType('add')
                    setEditingAppointment(null)
                  }}
                  className="mt-2 text-blue-600 hover:text-blue-700 text-xs lg:text-sm font-medium transition-colors duration-200"
                >
                  Book now
                </button>
              </div>
            )}
          </div>

          {/* Total Appointments */}
          <div className="bg-gradient-to-br from-white via-green-50/30 to-white rounded-2xl p-4 lg:p-6 shadow-xl border border-green-100/50 backdrop-blur-sm transform hover:scale-[1.02] hover:shadow-2xl transition-all duration-300">
            <div className="flex items-center space-x-3 mb-3 lg:mb-4">
              <div className="p-2 lg:p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                <FiCalendar className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
              </div>
              <h3 className="text-lg lg:text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">Total Appointments</h3>
            </div>
            <div className="text-center">
              {sectionLoading.appointments ? (
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-200 rounded w-12 mx-auto mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-16 mx-auto"></div>
                </div>
              ) : (
                <>
                  <p className="text-2xl lg:text-3xl font-bold text-gray-800">{appointments.length}</p>
                  <p className="text-gray-600 text-xs lg:text-sm mt-1">All time</p>
                </>
              )}
            </div>
          </div>

          {/* Orders */}
          <div className="bg-gradient-to-br from-white via-purple-50/30 to-white rounded-2xl p-4 lg:p-6 shadow-xl border border-purple-100/50 backdrop-blur-sm transform hover:scale-[1.02] hover:shadow-2xl transition-all duration-300">
            <div className="flex items-center space-x-3 mb-3 lg:mb-4">
              <div className="p-2 lg:p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                <FiShoppingBag className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
              </div>
              <h3 className="text-lg lg:text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">Orders</h3>
            </div>
            <div className="text-center">
              {sectionLoading.orders ? (
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-200 rounded w-12 mx-auto mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-20 mx-auto"></div>
                </div>
              ) : (
                <>
                  <p className="text-2xl lg:text-3xl font-bold text-gray-800">{orders.length}</p>
                  <p className="text-gray-600 text-xs lg:text-sm mt-1">Total orders</p>
                </>
              )}
            </div>
          </div>

          {/* Favorites */}
          <div className="bg-gradient-to-br from-white via-pink-50/30 to-white rounded-2xl p-4 lg:p-6 shadow-xl border border-pink-100/50 backdrop-blur-sm transform hover:scale-[1.02] hover:shadow-2xl transition-all duration-300">
            <div className="flex items-center space-x-3 mb-3 lg:mb-4">
              <div className="p-2 lg:p-3 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl shadow-lg">
                <FiHeart className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
              </div>
              <h3 className="text-lg lg:text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">Favorites</h3>
            </div>
            <div className="text-center">
              {sectionLoading.favorites ? (
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-200 rounded w-12 mx-auto mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-20 mx-auto"></div>
                </div>
              ) : (
                <>
                  <p className="text-2xl lg:text-3xl font-bold text-gray-800">{userFavorites.length}</p>
                  <p className="text-gray-600 text-xs lg:text-sm mt-1">Saved items</p>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-2xl p-4 lg:p-6 shadow-xl border border-gray-100/50 backdrop-blur-sm">
          <div className="flex items-center space-x-3 mb-4 lg:mb-6">
            <div className="p-2 lg:p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
              <FiClock className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
            </div>
            <h3 className="text-lg lg:text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">Recent Activity</h3>
          </div>
          <div className="space-y-3">
            {sectionLoading.appointments ? (
              // Loading skeleton for recent activity
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="animate-pulse flex items-center justify-between p-4 lg:p-5 bg-gray-50 rounded-xl">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-32"></div>
                      <div className="h-3 bg-gray-200 rounded w-48"></div>
                    </div>
                  </div>
                  <div className="w-16 h-6 bg-gray-200 rounded"></div>
                </div>
              ))
            ) : appointments && appointments.length > 0 ? (
              [...appointments]
                .sort((a, b) => {
                  // Sort by creation date first, then by appointment date
                  const createdA = new Date(a.createdAt || a.date)
                  const createdB = new Date(b.createdAt || b.date)
                  return createdB - createdA // Most recently created first
                })
                .slice(0, 3) // Show 3 most recent appointments
                .map((appointment, index) => (
                <div key={appointment._id || appointment.id || index} className="group flex items-center justify-between p-4 lg:p-5 bg-gradient-to-r from-white to-gray-50/50 rounded-xl shadow-md border border-gray-100/50 hover:shadow-lg transform hover:scale-[1.01] transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 lg:p-3 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-lg group-hover:shadow-md transition-all duration-200">
                      <FiCalendar className="w-4 h-4 lg:w-5 lg:h-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-bold text-gray-800 text-sm lg:text-base">{appointment.service?.name || appointment.serviceName || 'Service'}</p>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                          appointment.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                          appointment.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {appointment.status}
                        </span>
                      </div>
                      <p className="text-gray-600 text-xs lg:text-sm">
                        {appointment.date ? formatFullDate(appointment.date) : 'Date not set'}
                        {appointment.time && ` at ${formatTime(appointment.time)}`}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setAppointmentDetailItem(appointment)
                      setShowAppointmentDetailModal(true)
                    }}
                    className="text-blue-600 hover:text-blue-700 transition-colors duration-200 text-xs lg:text-sm font-medium"
                  >
                    View Details
                  </button>
                </div>
              ))
            ) : (
              <div className="text-center py-8 lg:py-12">
                <div className="p-3 lg:p-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl w-12 h-12 lg:w-16 lg:h-16 mx-auto mb-3 lg:mb-4 flex items-center justify-center">
                  <FiCalendar className="w-6 h-6 lg:w-8 lg:h-8 text-gray-400" />
                </div>
                <p className="text-gray-500 text-base lg:text-lg font-medium">No recent appointments</p>
                <p className="text-gray-400 text-xs lg:text-sm mt-1">Your activity will appear here</p>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderProfile = () => {
    // Show loading state if userData is not available
    if (!userData) {
      return (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Profile Settings</h2>
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            <span className="ml-3 text-gray-600">Loading profile...</span>
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Profile Settings</h2>

        <div className="bg-white rounded-xl p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">Personal Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
              <input
                type="text"
                value={profileData.firstName}
                onChange={(e) => handleProfileChange('firstName', e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
                onFocus={(e) => {
                  e.target.style.outline = 'none'
                  e.target.style.borderColor = branding.colors.secondary
                  e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db'
                  e.target.style.boxShadow = 'none'
                }}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
              <input
                type="text"
                value={profileData.lastName}
                onChange={(e) => handleProfileChange('lastName', e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
                onFocus={(e) => {
                  e.target.style.outline = 'none'
                  e.target.style.borderColor = branding.colors.secondary
                  e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db'
                  e.target.style.boxShadow = 'none'
                }}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
              <input
                type="email"
                value={profileData.email}
                onChange={(e) => handleProfileChange('email', e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
                onFocus={(e) => {
                  e.target.style.outline = 'none'
                  e.target.style.borderColor = branding.colors.secondary
                  e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db'
                  e.target.style.boxShadow = 'none'
                }}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
              <input
                type="tel"
                value={profileData.phone}
                onChange={(e) => handleProfileChange('phone', e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
                onFocus={(e) => {
                  e.target.style.outline = 'none'
                  e.target.style.borderColor = branding.colors.secondary
                  e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db'
                  e.target.style.boxShadow = 'none'
                }}
              />
            </div>
          </div>

          <div className="mt-6">
            <button
              onClick={handleProfileSave}
              disabled={isUpdating}
              className="text-white px-6 py-3 rounded-lg transition-colors duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ backgroundColor: branding.colors.secondary }}
              onMouseEnter={(e) => !isUpdating && (e.target.style.backgroundColor = branding.colors.accent)}
              onMouseLeave={(e) => !isUpdating && (e.target.style.backgroundColor = branding.colors.secondary)}
            >
              {isUpdating ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>
    )
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview()
      case 'appointments':
        return (
          <UserAppointments
            appointments={appointments}
            sectionLoading={sectionLoading}
            setShowAddModal={setShowAppointmentModal}
            setModalType={setModalType}
            setEditingItem={setEditingAppointment}
            setViewingItem={setViewingAppointment}
            setConfirmDialog={setConfirmDialog}
            handleDeleteAppointment={handleCancelAppointment}
            onReviewService={handleReviewService}
          />
        )
      case 'orders':
        return (
          <UserOrders
            orders={orders}
            sectionLoading={sectionLoading}
            setViewingItem={setViewingAppointment}
            setModalType={setModalType}
            setEditingItem={setEditingAppointment}
          />
        )
      case 'favorites':
        return (
          <UserFavorites
            favoriteProducts={userFavorites}
            sectionLoading={sectionLoading}
            setViewingItem={setViewingAppointment}
            setModalType={setModalType}
            setEditingItem={setEditingAppointment}
            handleRemoveFromFavorites={removeFromFavorites}
            handleAddToCart={addToCart}
          />
        )
      case 'payment-confirmations':
        return <UserPaymentConfirmations />
      case 'profile':
        return renderProfile()
      default:
        return renderOverview()
    }
  }

  // Show loading state for initial load
  if (isLoading && !loadedData.appointments) {
    return <Loading message="Loading your dashboard..." />
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <FiAlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Dashboard</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="text-white px-6 py-3 rounded-lg transition-colors duration-200 cursor-pointer"
              style={{ backgroundColor: branding.colors.secondary }}
              onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
              onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <DashboardLayout
        userType="user"
        activeTab={activeTab}
        setActiveTab={handleTabChange}
        userData={userData}
        onLogout={onLogout}
        sidebarItems={sidebarItems}
      >
        {renderContent()}
      </DashboardLayout>

      {/* Appointment Modal */}
      <AppointmentModal
        isOpen={showAppointmentModal}
        onClose={() => {
          setShowAppointmentModal(false)
          setEditingAppointment(null)
          setModalType('add')
        }}
        onSave={handleSaveAppointment}
        editingItem={editingAppointment}
        modalType={modalType}
        branding={branding}
        currentUser={userData}
        isUserMode={true}
      />

      {/* Appointment Detail Modal */}
      <AppointmentDetailModal
        isOpen={showAppointmentDetailModal}
        onClose={() => setShowAppointmentDetailModal(false)}
        appointment={appointmentDetailItem}
        onEdit={(appointment) => {
          setEditingAppointment(appointment)
          setModalType('edit')
          setShowAppointmentModal(true)
          setShowAppointmentDetailModal(false)
        }}
        onDelete={handleDeleteFromDetail}
        branding={branding}
      />

      {/* Review Modal */}
      {showReviewModal && reviewingService && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Write a Review
            </h3>
            <p className="text-gray-600 mb-6">
              Share your experience with {reviewingService.serviceName}
            </p>

            <ReviewForm
              serviceId={reviewingService.serviceId}
              branding={branding}
              onSuccess={() => {
                setShowReviewModal(false)
                setReviewingService(null)
                showSuccess('Thank you for your review! It has been submitted for approval.')
              }}
              onCancel={() => {
                setShowReviewModal(false)
                setReviewingService(null)
              }}
            />
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      {confirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {confirmDialog.title}
            </h3>
            <p className="text-gray-600 mb-6">
              {confirmDialog.message}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setConfirmDialog(null)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  confirmDialog.onConfirm()
                  setConfirmDialog(null)
                }}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default UserDashboard
