#!/usr/bin/env node

/**
 * Production Deployment Script for Backend
 * This script handles the complete production deployment process
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function validateEnvironment() {
  log('Validating production environment...', 'cyan');
  
  const requiredEnvVars = [
    'MONGODB_URI',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'EMAIL_USER',
    'EMAIL_PASS',
    'ADMIN_EMAIL',
    'ADMIN_PASSWORD'
  ];

  const envPath = path.join(__dirname, '../.env.production');
  
  if (!fs.existsSync(envPath)) {
    log('✗ .env.production file not found', 'red');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const missing = [];

  for (const envVar of requiredEnvVars) {
    if (!envContent.includes(`${envVar}=`) || envContent.includes(`${envVar}=your-`)) {
      missing.push(envVar);
    }
  }

  if (missing.length > 0) {
    log(`✗ Missing or placeholder environment variables: ${missing.join(', ')}`, 'red');
    log('Please update .env.production with real values', 'yellow');
    process.exit(1);
  }

  log('✓ Environment validation passed', 'green');
}

function runPreDeploymentChecks() {
  log('Running pre-deployment checks...', 'cyan');
  
  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 16) {
    log(`✗ Node.js version ${nodeVersion} is not supported. Please use Node.js 16 or higher.`, 'red');
    process.exit(1);
  }
  
  log(`✓ Node.js version ${nodeVersion} is supported`, 'green');

  // Check if TypeScript compiles
  try {
    execSync('npx tsc --noEmit', { stdio: 'pipe' });
    log('✓ TypeScript compilation check passed', 'green');
  } catch (error) {
    log('✗ TypeScript compilation failed', 'red');
    log(error.stdout?.toString() || error.message, 'red');
    process.exit(1);
  }

  // Check if all required directories exist
  const requiredDirs = ['src', 'uploads'];
  for (const dir of requiredDirs) {
    if (!fs.existsSync(path.join(__dirname, '..', dir))) {
      fs.mkdirSync(path.join(__dirname, '..', dir), { recursive: true });
      log(`✓ Created directory: ${dir}`, 'green');
    }
  }

  log('✓ Pre-deployment checks passed', 'green');
}

function installDependencies() {
  log('Installing production dependencies...', 'cyan');
  
  try {
    execSync('npm ci --only=production', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    log('✓ Dependencies installed successfully', 'green');
  } catch (error) {
    log('✗ Failed to install dependencies', 'red');
    process.exit(1);
  }
}

function buildApplication() {
  log('Building application...', 'cyan');
  
  try {
    execSync('npm run build', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..'),
      env: {
        ...process.env,
        NODE_ENV: 'production'
      }
    });
    log('✓ Application built successfully', 'green');
  } catch (error) {
    log('✗ Build failed', 'red');
    process.exit(1);
  }
}

function setupDatabase() {
  log('Setting up database...', 'cyan');
  
  try {
    // Run database migrations if they exist
    const migrationsPath = path.join(__dirname, '../migrations');
    if (fs.existsSync(migrationsPath)) {
      execSync('npm run migrate', { 
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });
      log('✓ Database migrations completed', 'green');
    }

    // Seed initial data if needed
    const seedPath = path.join(__dirname, '../scripts/seed-production.js');
    if (fs.existsSync(seedPath)) {
      execSync('node scripts/seed-production.js', { 
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });
      log('✓ Database seeding completed', 'green');
    }

  } catch (error) {
    log('✗ Database setup failed', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

function createSystemdService() {
  log('Creating systemd service...', 'cyan');
  
  const serviceName = 'microlocs-backend';
  const serviceContent = `[Unit]
Description=MicroLocs Backend API
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=${process.cwd()}
Environment=NODE_ENV=production
ExecStart=${process.execPath} dist/server.js
Restart=on-failure
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=${serviceName}

[Install]
WantedBy=multi-user.target`;

  const servicePath = `/etc/systemd/system/${serviceName}.service`;
  
  try {
    fs.writeFileSync(servicePath, serviceContent);
    execSync('systemctl daemon-reload');
    execSync(`systemctl enable ${serviceName}`);
    log(`✓ Systemd service created: ${serviceName}`, 'green');
  } catch (error) {
    log('⚠ Could not create systemd service (requires sudo)', 'yellow');
    log('You may need to create the service manually', 'yellow');
  }
}

function setupNginx() {
  log('Setting up Nginx configuration...', 'cyan');
  
  const nginxConfig = `server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    # SSL Configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # API proxy
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Static files
    location /uploads/ {
        alias ${process.cwd()}/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Frontend (if serving from same domain)
    location / {
        root /var/www/microlocs-frontend;
        try_files $uri $uri/ /index.html;
    }
}`;

  const nginxPath = '/etc/nginx/sites-available/microlocs';
  
  try {
    fs.writeFileSync(nginxPath, nginxConfig);
    execSync(`ln -sf ${nginxPath} /etc/nginx/sites-enabled/`);
    execSync('nginx -t');
    log('✓ Nginx configuration created', 'green');
  } catch (error) {
    log('⚠ Could not setup Nginx (requires sudo)', 'yellow');
    log('You may need to configure Nginx manually', 'yellow');
  }
}

function startServices() {
  log('Starting services...', 'cyan');
  
  try {
    // Start the application
    execSync('systemctl start microlocs-backend', { stdio: 'inherit' });
    log('✓ Backend service started', 'green');
    
    // Reload Nginx
    execSync('systemctl reload nginx', { stdio: 'inherit' });
    log('✓ Nginx reloaded', 'green');
    
  } catch (error) {
    log('⚠ Could not start services automatically', 'yellow');
    log('You may need to start services manually with sudo', 'yellow');
  }
}

function runHealthCheck() {
  log('Running health check...', 'cyan');
  
  // Wait a moment for services to start
  setTimeout(() => {
    try {
      const response = execSync('curl -f http://localhost:3000/api/health', { 
        encoding: 'utf8',
        timeout: 10000
      });
      
      const healthData = JSON.parse(response);
      if (healthData.success) {
        log('✓ Health check passed', 'green');
      } else {
        log('✗ Health check failed', 'red');
      }
    } catch (error) {
      log('✗ Health check failed - service may not be running', 'red');
    }
  }, 5000);
}

function main() {
  log('🚀 Starting production deployment...', 'bright');
  log('=====================================', 'bright');
  
  const startTime = Date.now();
  
  try {
    validateEnvironment();
    runPreDeploymentChecks();
    installDependencies();
    buildApplication();
    setupDatabase();
    createSystemdService();
    setupNginx();
    startServices();
    runHealthCheck();
    
    const endTime = Date.now();
    const deployTime = ((endTime - startTime) / 1000).toFixed(2);
    
    log('=====================================', 'bright');
    log('🎉 Production deployment completed!', 'green');
    log(`⏱️  Deployment time: ${deployTime} seconds`, 'cyan');
    log('🌐 Your API should be available at: https://yourdomain.com/api', 'cyan');
    log('📊 Health check: https://yourdomain.com/api/health', 'cyan');
    log('=====================================', 'bright');
    
  } catch (error) {
    log('=====================================', 'bright');
    log('💥 Production deployment failed!', 'red');
    log(`Error: ${error.message}`, 'red');
    log('=====================================', 'bright');
    process.exit(1);
  }
}

// Run the deployment process
main();
