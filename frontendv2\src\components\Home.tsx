import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

interface HomeProps {
  currentUser: any;
  onLogout: () => void;
}

export default function Home({ currentUser, onLogout }: HomeProps) {
  const navigate = useNavigate();
  const [showConsultationDetails, setShowConsultationDetails] = useState(false);

  const handleBookService = (service: any) => {
    // Navigate to booking flow with service data
    const searchParams = new URLSearchParams({
      service: JSON.stringify(service)
    });
    navigate(`/booking/datetime?${searchParams.toString()}`);
  };

  const handleLogin = () => {
    navigate('/login');
  };

  const handleSignup = () => {
    navigate('/signup');
  };

  const handleDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className="app">
      {/* Header */}
      <header className="header">
        <div className="header-content">
          <div className="auth-links">
            {currentUser ? (
              <>
                <button
                  className="auth-link"
                  onClick={handleDashboard}
                >
                  DASHBOARD
                </button>
                <button className="auth-link" onClick={onLogout}>
                  LOGOUT
                </button>
              </>
            ) : (
              <>
                <button
                  className="auth-link"
                  onClick={handleSignup}
                >
                  SIGN UP
                </button>
                <button
                  className="auth-link"
                  onClick={handleLogin}
                >
                  LOG IN
                </button>
              </>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="main-content">
        {/* Profile Section */}
        <div className="profile-section">
          <div className="profile-image">
            <div className="profile-avatar">
              <div className="avatar-placeholder">DSB</div>
            </div>
          </div>
          <h1 className="business-name">dammyspicybeauty</h1>
        </div>

        {/* Hero Banner */}
        <div className="hero-banner">
          <div className="hero-content">
            <div className="hero-text">
              <h2 className="hero-title">dammyspicybeauty</h2>
              <p className="location">INDIANAPOLIS, IN</p>
              <p className="welcome-text">Welcome to my booking site!</p>
            </div>
            <div className="hero-image">
              <div className="stylist-image">
                <div className="stylist-placeholder">
                  <div className="stylist-silhouette"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Service Information Sections */}
        <div className="info-sections">
          {/* About Me Section */}
          <div className="info-card about-me-card">
            <h3 className="info-title">About Me</h3>
            <div className="info-content">
              <p>Thank you for choosing dammyspicybeauty.</p>
              <p>My name is Dammy I'm a licenced cosmetologist, I specialize in hair care and beauty treatments of natural hair, microlocs etc.. I'm located in Indianapolis IN. My main objective is putting smile on individual women faces and getting them more styled up.</p>
              <p>I'm excited to begin this healthy care journey with you!</p>
            </div>
          </div>

          {/* Microlocks Section */}
          <div className="info-card microlocks-card">
            <h3 className="info-title">Microlocks</h3>
            <div className="info-content">
              <p>Full payment is required to secure all Microlock establishment appointments. This must be paid at the time of scheduling.</p>
              <p className="methods-title">3 STARTING METHODS TO CHOOSE FROM:</p>
              <ul className="methods-list">
                <li>1 - INTERLOCKS</li>
                <li>2 - STRAND TWIST</li>
                <li>3 - BRAID LOCS</li>
              </ul>
              <p>Establishment usually take 10-12 hours or more depending on Hair, Lock size, Lock Method, Hair Length, Density and Head size</p>
            </div>
          </div>

          {/* Reties Price List Section */}
          <div className="info-card price-list-card">
            <h3 className="info-title">Price list</h3>
            <div className="info-content">
              <h4 className="reties-title">Reties</h4>
              <div className="reties-pricing">
                {/* 4-5 weeks pricing */}
                <div className="retie-period">
                  <h5 className="retie-period-title">4-5 weeks</h5>
                  <div className="retie-item clickable" onClick={() => handleBookService({
                    id: 'retie-4-5-extra-small',
                    name: '4-5 week Retightening - Extra Small',
                    price: '300.00',
                    description: '4-5 week maintenance for extra small locs'
                  })}>
                    <span className="retie-duration">Extra Small</span>
                    <span className="retie-dots">.............</span>
                    <span className="retie-price">$300</span>
                    <button className="retie-book-btn">BOOK</button>
                  </div>
                  <div className="retie-item clickable" onClick={() => handleBookService({
                    id: 'retie-4-5-small',
                    name: '4-5 week Retightening - Small',
                    price: '200.00',
                    description: '4-5 week maintenance for small locs'
                  })}>
                    <span className="retie-duration">Small</span>
                    <span className="retie-dots">.............</span>
                    <span className="retie-price">$200</span>
                    <button className="retie-book-btn">BOOK</button>
                  </div>
                  <div className="retie-item clickable" onClick={() => handleBookService({
                    id: 'retie-4-5-medium',
                    name: '4-5 week Retightening - Medium',
                    price: '150.00',
                    description: '4-5 week maintenance for medium locs'
                  })}>
                    <span className="retie-duration">Medium</span>
                    <span className="retie-dots">.............</span>
                    <span className="retie-price">$150</span>
                    <button className="retie-book-btn">BOOK</button>
                  </div>
                </div>

                {/* 6-7 weeks pricing */}
                <div className="retie-period">
                  <h5 className="retie-period-title">6-7 weeks</h5>
                  <div className="retie-item clickable" onClick={() => handleBookService({
                    id: 'retie-6-7-extra-small',
                    name: '6-7 week Retightening - Extra Small',
                    price: '350.00',
                    description: '6-7 week maintenance for extra small locs'
                  })}>
                    <span className="retie-duration">Extra Small</span>
                    <span className="retie-dots">.............</span>
                    <span className="retie-price">$350</span>
                    <button className="retie-book-btn">BOOK</button>
                  </div>
                  <div className="retie-item clickable" onClick={() => handleBookService({
                    id: 'retie-6-7-small',
                    name: '6-7 week Retightening - Small',
                    price: '250.00',
                    description: '6-7 week maintenance for small locs'
                  })}>
                    <span className="retie-duration">Small</span>
                    <span className="retie-dots">.............</span>
                    <span className="retie-price">$250</span>
                    <button className="retie-book-btn">BOOK</button>
                  </div>
                  <div className="retie-item clickable" onClick={() => handleBookService({
                    id: 'retie-6-7-medium',
                    name: '6-7 week Retightening - Medium',
                    price: '200.00',
                    description: '6-7 week maintenance for medium locs'
                  })}>
                    <span className="retie-duration">Medium</span>
                    <span className="retie-dots">.............</span>
                    <span className="retie-price">$200</span>
                    <button className="retie-book-btn">BOOK</button>
                  </div>
                </div>

                {/* 8 weeks+ pricing */}
                <div className="retie-period">
                  <h5 className="retie-period-title">8 weeks+</h5>
                  <div className="retie-item clickable" onClick={() => handleBookService({
                    id: 'retie-8-extra-small',
                    name: '8+ week Retightening - Extra Small',
                    price: '400.00',
                    description: '8+ week maintenance for extra small locs'
                  })}>
                    <span className="retie-duration">Extra Small</span>
                    <span className="retie-dots">.............</span>
                    <span className="retie-price">$400</span>
                    <button className="retie-book-btn">BOOK</button>
                  </div>
                  <div className="retie-item clickable" onClick={() => handleBookService({
                    id: 'retie-8-small',
                    name: '8+ week Retightening - Small',
                    price: '300.00',
                    description: '8+ week maintenance for small locs'
                  })}>
                    <span className="retie-duration">Small</span>
                    <span className="retie-dots">.............</span>
                    <span className="retie-price">$300</span>
                    <button className="retie-book-btn">BOOK</button>
                  </div>
                  <div className="retie-item clickable" onClick={() => handleBookService({
                    id: 'retie-8-medium',
                    name: '8+ week Retightening - Medium',
                    price: '250.00',
                    description: '8+ week maintenance for medium locs'
                  })}>
                    <span className="retie-duration">Medium</span>
                    <span className="retie-dots">.............</span>
                    <span className="retie-price">$250</span>
                    <button className="retie-book-btn">BOOK</button>
                  </div>
                </div>
              </div>

              {/* Other Services Section */}
              <div className="other-services">
                <h4 className="other-services-title">Other Services</h4>
                
                <div className="service-item clickable" onClick={() => handleBookService({
                  id: 'spa-hair-wash',
                  name: 'Spa Hair Wash',
                  price: '35.00',
                  description: 'PLEASE NO EXTRA GUEST 🙃'
                })}>
                  <div className="service-info">
                    <span className="service-name">Spa Hair Wash</span>
                    <span className="service-dots">.............</span>
                    <span className="service-price">$35</span>
                  </div>
                  <button className="retie-book-btn">BOOK</button>
                </div>

                <div className="service-item clickable" onClick={() => handleBookService({
                  id: 'consultation',
                  name: 'Consultation',
                  price: '30.00',
                  description: 'Required prior to booking a Sisterlocks or Microlocks Establishment. At the consultation, client hair history will be discussed.'
                })}>
                  <div className="service-info">
                    <span className="service-name">Consultation</span>
                    <span className="service-dots">.............</span>
                    <span className="service-price">$30</span>
                  </div>
                  <button className="retie-book-btn">BOOK</button>
                </div>

                <div className="service-item clickable" onClick={() => handleBookService({
                  id: 'lock-repairs',
                  name: 'Lock Repairs',
                  price: '50.00',
                  description: 'Professional lock repair service. Minimal 2hr Session. PLEASE NO EXTRA GUEST 🙃'
                })}>
                  <div className="service-info">
                    <span className="service-name">Lock Repairs</span>
                    <span className="service-dots">.............</span>
                    <span className="service-price">$50</span>
                  </div>
                  <button className="retie-book-btn">BOOK</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Consultation Section */}
        <div className="consultation-section">
          <div className="consultation-card">
            <h3 className="consultation-subtitle">READY TO START YOUR LOC JOURNEY?</h3>

            <p className="consultation-intro">
              To begin, you'll need to book a consultation which costs $30 (non-refundable).
            </p>

            <p className="consultation-options-title">We offer two options to suit your needs:</p>

            <div className="consultation-options">
              <div className="consultation-option">
                <h4>1. IN-PERSON CONSULTATION</h4>
              </div>
              <div className="consultation-option">
                <h4>2. VIDEO CALL CONSULTATION</h4>
              </div>
            </div>

            <p className="consultation-duration">Each session will be 30 minutes</p>

            <div className="consultation-benefits">
              <div className="benefit-item">✓ Curating your hair goals</div>
              <div className="benefit-item">✓ Discussing your hair type and its condition</div>
              <div className="benefit-item">✓ Recommending the best methods for your loc journey to thrive</div>
            </div>

            <p className="consultation-note">
              Keep in mind, this consultation is mandatory for better understanding your hair journey.
            </p>

            <div className="consultation-actions">
              <button
                className="consultation-cta"
                onClick={() => handleBookService({
                  id: 'consultation-main',
                  name: 'Consultation',
                  price: '30.00',
                  description: 'Required prior to booking a Sisterlocks or Microlocks Establishment. At the consultation, client hair history will be discussed. The Sisterlocks hair maintenance system and care will be discussed, including do\'s and don\'ts.'
                })}
              >
                BOOK YOUR CONSULTATION NOW
              </button>
              <button
                className="read-more-btn"
                onClick={() => setShowConsultationDetails(!showConsultationDetails)}
              >
                {showConsultationDetails ? 'Read Less' : 'Read More'}
              </button>
            </div>

            {showConsultationDetails && (
              <div className="consultation-details">
                <div className="consultation-prep">
                  <h4>HOW TO BE PREPARED FOR YOUR CONSULTATION</h4>
                  <ul>
                    <li><strong>Hair Must Be Undone</strong> - Please ensure your hair is completely taken down (no braids, twists, or extensions)</li>
                    <li><strong>Clean & Product-Free</strong> - Wash your hair and avoid using oils, creams, or gels</li>
                    <li><strong>Natural State</strong> - Your hair should be free from heat styling and in its natural texture</li>
                    <li><strong>Good Lighting</strong> - Be in a well-lit area so your hair can be seen clearly</li>
                    <li><strong>Clear Video or Photos</strong> - If the consultation is virtual, send a clear video or pictures of your hair before the appointment</li>
                    <li><strong>Questions Ready</strong> - Write down any questions or concerns you may have about the service</li>
                  </ul>
                </div>

                <div className="installation-prep">
                  <h4>Things to Do Before Your Installation Appointment</h4>
                  <ul>
                    <li><strong>Wash your hair</strong> - the day before or the morning of your appointment</li>
                    <li><strong>Skip blowouts, flat irons, or curling irons</strong></li>
                    <li><strong>Detangle gently</strong> - Make sure your hair is free from knots</li>
                    <li><strong>Avoid products</strong> - avoid gels, oils, or creams in your hair</li>
                    <li><strong>Plan for comfort</strong> - Bring your headphones, snacks, laptop, or a drink - appointments take time!</li>
                    <li><strong>Arrive on time</strong> - Being on time helps us start and finish smoothly</li>
                  </ul>
                </div>

                <div className="transfer-consultation">
                  <h4>CONSULTATION FOR TRANSFERRED CLIENTS</h4>
                  <p>A consultation is required for all transferred clients.</p>
                  <p>The fee is $30 (non-refundable)</p>
                  <p>This process allows us to better understand your loc journey and guide you through the next steps for a smooth, continuous experience.</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Policies Section */}
        <div className="policies-section">
          <div className="policies-card">
            <h2 className="policies-title">Policies & Terms of Service</h2>

            <div className="policies-content">
              <ul className="policies-list">
                <li>
                  <strong>TO BOOK:</strong> Full payment is required to secure any service. Payment must be made at the time of booking.
                </li>
                <li>
                  <strong>RESCHEDULING:</strong> Same-day reschedule - loss of payment
                </li>
                <li>
                  <strong>CANCELLATIONS:</strong> All services must be cancelled at least 24 hours before your appointment.
                </li>
                <li>
                  <strong>NO REFUNDS:</strong> All services are final once completed.
                </li>
                 <li>
                  <strong>LATENESS:</strong>
                    <li> To respect everyone's time, please arrive on time for your appointment.</li>
                    <li>If you are more than 15 minutes late, a $20 late fee will be added to your service.</li>
                    <li>If you are more than 30 minutes late, your appointment may need to be rescheduled, and your payment will be forfeited.</li>
                    <li>Repeated lateness may result in refusal of future bookings.</li>
                </li>
                <li>
                  <strong>PAYMENT OPTIONS:</strong>
                  <div className="payment-methods-single">
                    <div className="payment-method-combined">
                      <div className="payment-option">
                        <strong>Cash App:</strong><br />
                        (*************
                      </div>
                      <div className="payment-option">
                        <strong>Zelle:</strong><br />
                        (*************
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>

            <div className="policies-footer">
              <p>Thank you for choosing us. We appreciate your business and look forward to serving you with love and excellence!</p>
            </div>
          </div>
        </div>

        {/* Disclaimer Section */}
        <div className="disclaimer-section">
          <div className="disclaimer-card">
            <h3 className="disclaimer-title">Disclaimer</h3>
            <div className="disclaimer-content">
              <p>At DammySpicy Beauty, we are committed to delivering the best possible service tailored to each individual. However, please understand that results may vary from person to person due to differences in hair type, density, and other factors. While we strive for excellence, we cannot guarantee identical outcomes for everyone.</p>

              <p>All services are considered final once completed.</p>

              <p>Please note that photos and videos may be shared on our social media platforms for promotional purposes, unless a customer explicitly requests otherwise.</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
