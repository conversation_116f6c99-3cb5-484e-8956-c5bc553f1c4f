// Configuration utilities for environment variables

// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL ||
    (import.meta.env.MODE === 'production'
      ? 'https://microlocsbackend-xgeg.onrender.com/api/v2'
      : 'http://localhost:3001/api/v2'),
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3
};

// App Configuration
export const APP_CONFIG = {
  TITLE: import.meta.env.VITE_APP_TITLE || 'Goldie Locs By Tina - Admin',
  DESCRIPTION: import.meta.env.VITE_APP_DESCRIPTION || 'Professional locs and natural hair care services - Admin Dashboard',
  URL: import.meta.env.VITE_APP_URL || 'http://localhost:5174',
  NODE_ENV: import.meta.env.VITE_NODE_ENV || import.meta.env.MODE || 'development'
};

// Optional external API keys
export const EXTERNAL_APIS = {
  GOOGLE_ANALYTICS_ID: import.meta.env.VITE_GOOGLE_ANALYTICS_ID,
  FACEBOOK_PIXEL_ID: import.meta.env.VITE_FACEBOOK_PIXEL_ID,
  MAPS_API_KEY: import.meta.env.VITE_MAPS_API_KEY
};

// Development helpers
export const isDevelopment = APP_CONFIG.NODE_ENV === 'development';
export const isProduction = APP_CONFIG.NODE_ENV === 'production';

// Log configuration in development
if (isDevelopment) {
  console.log('🔧 API Configuration:', {
    BASE_URL: API_CONFIG.BASE_URL,
    MODE: import.meta.env.MODE,
    NODE_ENV: APP_CONFIG.NODE_ENV
  });
}
