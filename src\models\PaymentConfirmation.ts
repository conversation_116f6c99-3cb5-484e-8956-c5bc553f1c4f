import mongoose, { Document, Schema } from 'mongoose';

export interface IPaymentConfirmation extends Document {
  user: mongoose.Types.ObjectId;
  order?: mongoose.Types.ObjectId;
  appointment?: mongoose.Types.ObjectId;
  amount: number;
  paymentMethod: string;
  proofImage: string;
  notes?: string;
  status: 'pending' | 'verified' | 'rejected';
  verifiedBy?: mongoose.Types.ObjectId;
  verifiedAt?: Date;
  rejectionReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

const paymentConfirmationSchema = new Schema<IPaymentConfirmation>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  order: {
    type: Schema.Types.ObjectId,
    ref: 'Order',
    required: false
  },
  appointment: {
    type: Schema.Types.ObjectId,
    ref: 'Appointment',
    required: false
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  paymentMethod: {
    type: String,
    required: true,
    trim: true
  },
  proofImage: {
    type: String,
    required: true
  },
  notes: {
    type: String,
    trim: true,
    maxlength: 500
  },
  status: {
    type: String,
    enum: ['pending', 'verified', 'rejected'],
    default: 'pending'
  },
  verifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: false
  },
  verifiedAt: {
    type: Date,
    required: false
  },
  rejectionReason: {
    type: String,
    trim: true,
    maxlength: 200
  }
}, {
  timestamps: true
});

// Indexes for better query performance
paymentConfirmationSchema.index({ user: 1 });
paymentConfirmationSchema.index({ order: 1 });
paymentConfirmationSchema.index({ appointment: 1 });
paymentConfirmationSchema.index({ status: 1 });
paymentConfirmationSchema.index({ createdAt: -1 });

// Virtual for populated user data
paymentConfirmationSchema.virtual('userData', {
  ref: 'User',
  localField: 'user',
  foreignField: '_id',
  justOne: true
});

// Virtual for populated order data
paymentConfirmationSchema.virtual('orderData', {
  ref: 'Order',
  localField: 'order',
  foreignField: '_id',
  justOne: true
});

// Virtual for populated appointment data
paymentConfirmationSchema.virtual('appointmentData', {
  ref: 'Appointment',
  localField: 'appointment',
  foreignField: '_id',
  justOne: true
});

// Virtual for populated verifier data
paymentConfirmationSchema.virtual('verifierData', {
  ref: 'User',
  localField: 'verifiedBy',
  foreignField: '_id',
  justOne: true
});

// Ensure virtuals are included in JSON output
paymentConfirmationSchema.set('toJSON', { virtuals: true });
paymentConfirmationSchema.set('toObject', { virtuals: true });

// Custom validation to ensure either order or appointment is provided
paymentConfirmationSchema.pre('validate', function(next) {
  if (!this.order && !this.appointment) {
    next(new Error('Payment confirmation must be associated with either an order or appointment'));
  } else if (this.order && this.appointment) {
    next(new Error('Payment confirmation cannot be associated with both order and appointment'));
  } else {
    next();
  }
});

export const PaymentConfirmation = mongoose.model<IPaymentConfirmation>('PaymentConfirmation', paymentConfirmationSchema);
