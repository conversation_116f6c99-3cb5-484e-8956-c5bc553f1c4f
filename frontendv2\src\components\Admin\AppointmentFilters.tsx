import { useState, useEffect } from 'react';
import { type AppointmentFilters } from '../../utils/api';

interface AppointmentFiltersProps {
  filters: AppointmentFilters;
  onFilterChange: (filters: Partial<AppointmentFilters>) => void;
  selectedCount: number;
  onBulkStatusUpdate: (status: string) => void;
}

export default function AppointmentFilters({
  filters,
  onFilterChange,
  selectedCount,
  onBulkStatusUpdate
}: AppointmentFiltersProps) {
  const [searchTerm, setSearchTerm] = useState(filters.search || '');
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm !== filters.search) {
        onFilterChange({ search: searchTerm, page: 1 });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm, filters.search, onFilterChange]);

  // Handle status filter change
  const handleStatusChange = (status: string) => {
    onFilterChange({ status: status === 'all' ? undefined : status, page: 1 });
  };

  // Handle date filter change
  const handleDateChange = (date: string) => {
    onFilterChange({ date: date || undefined, page: 1 });
  };

  // Handle sort change
  const handleSortChange = (sortBy: string, sortOrder: 'asc' | 'desc') => {
    onFilterChange({ sortBy, sortOrder, page: 1 });
  };

  // Get today's date for date input
  const getTodayDate = () => {
    return new Date().toISOString().split('T')[0];
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    onFilterChange({
      search: undefined,
      status: undefined,
      date: undefined,
      page: 1
    });
  };

  return (
    <div className="appointment-filters">
      <div className="filters-row">
        {/* Search */}
        <div className="filter-group">
          <label htmlFor="search">Search</label>
          <div className="search-input-container">
            <input
              id="search"
              type="text"
              placeholder="Search by name, email, or phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <span className="search-icon">🔍</span>
          </div>
        </div>

        {/* Status Filter */}
        <div className="filter-group">
          <label htmlFor="status">Status</label>
          <select
            id="status"
            value={filters.status || 'all'}
            onChange={(e) => handleStatusChange(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        {/* Date Filter */}
        <div className="filter-group">
          <label htmlFor="date">Date</label>
          <input
            id="date"
            type="date"
            value={filters.date || ''}
            onChange={(e) => handleDateChange(e.target.value)}
            className="filter-input"
          />
        </div>

        {/* Sort */}
        <div className="filter-group">
          <label htmlFor="sort">Sort By</label>
          <select
            id="sort"
            value={`${filters.sortBy}-${filters.sortOrder}`}
            onChange={(e) => {
              const [sortBy, sortOrder] = e.target.value.split('-');
              handleSortChange(sortBy, sortOrder as 'asc' | 'desc');
            }}
            className="filter-select"
          >
            <option value="date-desc">Date (Newest First)</option>
            <option value="date-asc">Date (Oldest First)</option>
            <option value="createdAt-desc">Created (Newest First)</option>
            <option value="createdAt-asc">Created (Oldest First)</option>
            <option value="customerInfo.name-asc">Customer Name (A-Z)</option>
            <option value="customerInfo.name-desc">Customer Name (Z-A)</option>
            <option value="status-asc">Status (A-Z)</option>
            <option value="status-desc">Status (Z-A)</option>
          </select>
        </div>

        {/* Clear Filters */}
        <div className="filter-group">
          <button
            onClick={clearFilters}
            className="btn btn-outline btn-sm"
            title="Clear all filters"
          >
            Clear Filters
          </button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedCount > 0 && (
        <div className="bulk-actions">
          <div className="bulk-actions-info">
            <span className="selected-count">
              {selectedCount} appointment{selectedCount !== 1 ? 's' : ''} selected
            </span>
          </div>
          
          <div className="bulk-actions-buttons">
            <button
              onClick={() => setShowBulkActions(!showBulkActions)}
              className="btn btn-outline btn-sm"
            >
              Bulk Actions {showBulkActions ? '▲' : '▼'}
            </button>
            
            {showBulkActions && (
              <div className="bulk-actions-dropdown">
                <button
                  onClick={() => onBulkStatusUpdate('confirmed')}
                  className="bulk-action-btn"
                >
                  Mark as Confirmed
                </button>
                <button
                  onClick={() => onBulkStatusUpdate('completed')}
                  className="bulk-action-btn"
                >
                  Mark as Completed
                </button>
                <button
                  onClick={() => onBulkStatusUpdate('cancelled')}
                  className="bulk-action-btn"
                >
                  Mark as Cancelled
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Filters */}
      <div className="quick-filters">
        <span className="quick-filters-label">Quick Filters:</span>
        <button
          onClick={() => handleDateChange(getTodayDate())}
          className="quick-filter-btn"
        >
          Today
        </button>
        <button
          onClick={() => handleStatusChange('pending')}
          className="quick-filter-btn"
        >
          Pending Only
        </button>
        <button
          onClick={() => handleStatusChange('confirmed')}
          className="quick-filter-btn"
        >
          Confirmed Only
        </button>
        <button
          onClick={() => {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            handleDateChange(tomorrow.toISOString().split('T')[0]);
          }}
          className="quick-filter-btn"
        >
          Tomorrow
        </button>
      </div>

      {/* Results Summary */}
      <div className="results-summary">
        <span className="results-text">
          Showing {filters.limit || 20} results per page
        </span>
        {(filters.search || filters.status || filters.date) && (
          <span className="active-filters">
            {filters.search && <span className="filter-tag">Search: "{filters.search}"</span>}
            {filters.status && <span className="filter-tag">Status: {filters.status}</span>}
            {filters.date && <span className="filter-tag">Date: {filters.date}</span>}
          </span>
        )}
      </div>
    </div>
  );
}
