import { Request, Response } from 'express';
import { MediaService } from '../services/mediaService';
import { sendSuccess, sendError } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class MediaController {
  /**
   * Upload media file
   */
  static async uploadMedia(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.file) {
        sendError(res, 'No file uploaded', undefined, 400);
        return;
      }

      const { alt, caption, description, tags } = req.body;
      const parsedTags = tags ? JSON.parse(tags) : [];

      const media = await MediaService.uploadMedia(req.file, req.user!._id, {
        alt,
        caption,
        description,
        tags: parsedTags
      });

      sendSuccess(res, 'Media uploaded successfully', { media });
    } catch (error) {
      console.error('Upload media error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Get media library
   */
  static async getMediaLibrary(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        mimeType,
        tags,
        uploadedBy
      } = req.query;

      const parsedTags = tags ? (typeof tags === 'string' ? [tags] : tags as string[]) : undefined;

      const result = await MediaService.getMediaLibrary({
        page: Number(page),
        limit: Number(limit),
        search: search as string,
        mimeType: mimeType as string,
        tags: parsedTags,
        uploadedBy: uploadedBy as string
      });

      sendSuccess(res, 'Media library retrieved successfully', result);
    } catch (error) {
      console.error('Get media library error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Get media by ID
   */
  static async getMediaById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const media = await MediaService.getMediaById(id);

      if (!media) {
        sendError(res, 'Media not found', undefined, 404);
        return;
      }

      sendSuccess(res, 'Media retrieved successfully', { media });
    } catch (error) {
      console.error('Get media by ID error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Update media metadata
   */
  static async updateMedia(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { alt, caption, description, tags } = req.body;

      const media = await MediaService.updateMedia(id, {
        alt,
        caption,
        description,
        tags
      });

      if (!media) {
        sendError(res, 'Media not found', undefined, 404);
        return;
      }

      sendSuccess(res, 'Media updated successfully', { media });
    } catch (error) {
      console.error('Update media error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Delete media
   */
  static async deleteMedia(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const success = await MediaService.deleteMedia(id);

      if (!success) {
        sendError(res, 'Media not found or could not be deleted', undefined, 404);
        return;
      }

      sendSuccess(res, 'Media deleted successfully');
    } catch (error) {
      console.error('Delete media error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Bulk delete media
   */
  static async bulkDeleteMedia(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { ids } = req.body;

      if (!Array.isArray(ids) || ids.length === 0) {
        sendError(res, 'Invalid or empty IDs array', undefined, 400);
        return;
      }

      const deletedCount = await MediaService.bulkDeleteMedia(ids);

      sendSuccess(res, `${deletedCount} media files deleted successfully`, {
        deletedCount,
        totalRequested: ids.length
      });
    } catch (error) {
      console.error('Bulk delete media error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Get media statistics
   */
  static async getMediaStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await MediaService.getMediaStats();
      sendSuccess(res, 'Media statistics retrieved successfully', { stats });
    } catch (error) {
      console.error('Get media stats error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Get all tags
   */
  static async getAllTags(req: Request, res: Response): Promise<void> {
    try {
      const tags = await MediaService.getAllTags();
      sendSuccess(res, 'Tags retrieved successfully', { tags });
    } catch (error) {
      console.error('Get all tags error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Track media usage
   */
  static async trackMediaUsage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { mediaId } = req.params;
      const { type, id, field } = req.body;

      await MediaService.trackMediaUsage(mediaId, { type, id, field });

      sendSuccess(res, 'Media usage tracked successfully');
    } catch (error) {
      console.error('Track media usage error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Remove media usage tracking
   */
  static async removeMediaUsage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { mediaId } = req.params;
      const { type, id, field } = req.body;

      await MediaService.removeMediaUsage(mediaId, { type, id, field });

      sendSuccess(res, 'Media usage tracking removed successfully');
    } catch (error) {
      console.error('Remove media usage error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
