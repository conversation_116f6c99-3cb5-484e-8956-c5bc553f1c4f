import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { productionConfig } from '../config/production';

// General rate limiting
export const generalRateLimit = rateLimit({
  ...productionConfig.security.rateLimit,
  keyGenerator: (req: Request) => {
    // Use IP address as key, but consider X-Forwarded-For for proxies
    return req.ip || req.connection.remoteAddress || 'unknown';
  },
  handler: (req: Request, res: Response) => {
    res.status(429).json({
      success: false,
      message: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.round(productionConfig.security.rateLimit.windowMs / 1000)
    });
  }
});

// Strict rate limiting for authentication endpoints
export const authRateLimit = rateLimit({
  ...productionConfig.security.authRateLimit,
  keyGenerator: (req: Request) => {
    return req.ip || req.connection.remoteAddress || 'unknown';
  },
  handler: (req: Request, res: Response) => {
    res.status(429).json({
      success: false,
      message: 'Too many authentication attempts, please try again later.',
      retryAfter: Math.round(productionConfig.security.authRateLimit.windowMs / 1000)
    });
  }
});

// API-specific rate limiting for different endpoints
export const apiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Higher limit for general API usage
  message: 'API rate limit exceeded, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    // For authenticated users, use user ID + IP
    const userId = (req as any).user?.id;
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    return userId ? `${userId}:${ip}` : ip;
  }
});

// File upload rate limiting
export const uploadRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // 50 uploads per hour
  message: 'Upload rate limit exceeded, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    const userId = (req as any).user?.id;
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    return userId ? `upload:${userId}:${ip}` : `upload:${ip}`;
  }
});

// Admin endpoints rate limiting
export const adminRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // Higher limit for admin operations
  message: 'Admin rate limit exceeded, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    const userId = (req as any).user?.id;
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    return `admin:${userId}:${ip}`;
  }
});

// Payment-related endpoints (very strict)
export const paymentRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Only 10 payment operations per hour
  message: 'Payment rate limit exceeded for security reasons.',
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    const userId = (req as any).user?.id;
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    return `payment:${userId}:${ip}`;
  }
});
