import { useState, useCallback } from 'react'
import mediaService from '../services/mediaService'

export const useMediaLibrary = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedMedia, setSelectedMedia] = useState([])
  const [modalConfig, setModalConfig] = useState({
    multiple: false,
    allowUpload: true,
    onSelect: null
  })

  // Open media library modal
  const openMediaLibrary = useCallback((config = {}) => {
    setModalConfig({
      multiple: false,
      allowUpload: true,
      ...config
    })
    setSelectedMedia(config.selectedImages || [])
    setIsModalOpen(true)
  }, [])

  // Close media library modal
  const closeMediaLibrary = useCallback(() => {
    setIsModalOpen(false)
    setSelectedMedia([])
    setModalConfig({
      multiple: false,
      allowUpload: true,
      onSelect: null
    })
  }, [])

  // Handle media selection
  const handleMediaSelect = useCallback((media) => {
    if (modalConfig.onSelect) {
      modalConfig.onSelect(media)
    }
    closeMediaLibrary()
  }, [modalConfig.onSelect, closeMediaLibrary])

  // Select single image
  const selectSingleImage = useCallback((onSelect) => {
    openMediaLibrary({
      multiple: false,
      allowUpload: true,
      onSelect
    })
  }, [openMediaLibrary])

  // Select multiple images
  const selectMultipleImages = useCallback((onSelect, selectedImages = []) => {
    openMediaLibrary({
      multiple: true,
      allowUpload: true,
      selectedImages,
      onSelect
    })
  }, [openMediaLibrary])

  // Select image for specific purpose (e.g., profile picture, product image)
  const selectImageForPurpose = useCallback((purpose, onSelect) => {
    const purposeConfig = {
      profilePicture: {
        multiple: false,
        allowUpload: true,
        title: 'Select Profile Picture'
      },
      productImage: {
        multiple: true,
        allowUpload: true,
        title: 'Select Product Images'
      },
      serviceImage: {
        multiple: true,
        allowUpload: true,
        title: 'Select Service Images'
      },
      brandingImage: {
        multiple: false,
        allowUpload: true,
        title: 'Select Branding Image'
      },
      heroImage: {
        multiple: false,
        allowUpload: true,
        title: 'Select Hero Image'
      },
      logo: {
        multiple: false,
        allowUpload: true,
        title: 'Select Logo'
      }
    }

    const config = purposeConfig[purpose] || purposeConfig.productImage

    openMediaLibrary({
      ...config,
      onSelect
    })
  }, [openMediaLibrary])

  return {
    // State
    isModalOpen,
    selectedMedia,
    modalConfig,

    // Actions
    openMediaLibrary,
    closeMediaLibrary,
    handleMediaSelect,
    selectSingleImage,
    selectMultipleImages,
    selectImageForPurpose,

    // Modal props
    mediaLibraryProps: {
      isOpen: isModalOpen,
      onClose: closeMediaLibrary,
      onSelect: handleMediaSelect,
      multiple: modalConfig.multiple,
      selectedImages: selectedMedia,
      allowUpload: modalConfig.allowUpload
    }
  }
}

// Hook for media operations
export const useMediaOperations = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Upload media
  const uploadMedia = useCallback(async (files, metadata = {}) => {
    setLoading(true)
    setError(null)
    try {
      const result = await mediaService.uploadMultipleMedia(files, metadata)
      return result
    } catch (err) {
      setError(err.message)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  // Delete media
  const deleteMedia = useCallback(async (mediaId) => {
    setLoading(true)
    setError(null)
    try {
      const result = await mediaService.deleteMedia(mediaId)
      return result
    } catch (err) {
      setError(err.message)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  // Update media
  const updateMedia = useCallback(async (mediaId, metadata) => {
    setLoading(true)
    setError(null)
    try {
      const result = await mediaService.updateMedia(mediaId, metadata)
      return result
    } catch (err) {
      setError(err.message)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  // Get media library
  const getMediaLibrary = useCallback(async (params = {}) => {
    setLoading(true)
    setError(null)
    try {
      const result = await mediaService.getMediaLibrary(params)
      return result
    } catch (err) {
      setError(err.message)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    loading,
    error,
    uploadMedia,
    deleteMedia,
    updateMedia,
    getMediaLibrary,
    clearError: () => setError(null)
  }
}

export default useMediaLibrary
