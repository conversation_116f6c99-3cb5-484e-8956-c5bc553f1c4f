import { body, param, query, ValidationChain } from 'express-validator';

// Auth validation
export const registerValidation: ValidationChain[] = [
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Passwords do not match');
      }
      return true;
    })
];

export const loginValidation: ValidationChain[] = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

export const forgotPasswordValidation: ValidationChain[] = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email')
];

// Appointment validation
export const createAppointmentValidation: ValidationChain[] = [
  body('service')
    .isMongoId()
    .withMessage('Please provide a valid service ID'),
  body('date')
    .isISO8601()
    .toDate()
    .withMessage('Please provide a valid date'),
  body('time')
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Please provide a valid time in HH:MM format'),
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .custom((value) => {
      if (value && value.trim() !== '') {
        // Only validate if phone is provided and not empty
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(value)) {
          throw new Error('Please provide a valid phone number');
        }
      }
      return true;
    }),
  body('message')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Message cannot exceed 500 characters')
];

export const updateAppointmentValidation: ValidationChain[] = [
  param('id')
    .isMongoId()
    .withMessage('Please provide a valid appointment ID'),
  body('date')
    .optional()
    .isISO8601()
    .toDate()
    .withMessage('Please provide a valid date'),
  body('time')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Please provide a valid time in HH:MM format'),
  body('status')
    .optional()
    .isIn(['pending', 'confirmed', 'completed', 'cancelled'])
    .withMessage('Invalid status')
];

// Cart validation
export const addToCartValidation: ValidationChain[] = [
  body('productId')
    .isMongoId()
    .withMessage('Please provide a valid product ID'),
  body('quantity')
    .isInt({ min: 1, max: 100 })
    .withMessage('Quantity must be between 1 and 100')
];

export const updateCartItemValidation: ValidationChain[] = [
  param('itemId')
    .isMongoId()
    .withMessage('Please provide a valid item ID'),
  body('quantity')
    .isInt({ min: 1, max: 100 })
    .withMessage('Quantity must be between 1 and 100')
];

// Order validation
export const createOrderValidation: ValidationChain[] = [
  body('items')
    .isArray({ min: 1 })
    .withMessage('Order must contain at least one item'),
  body('items.*.productId')
    .isMongoId()
    .withMessage('Please provide valid product IDs'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be at least 1'),
  body('shippingAddress.street')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Street address must be between 5 and 200 characters'),
  body('shippingAddress.city')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters'),
  body('shippingAddress.state')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('State must be between 2 and 50 characters'),
  body('shippingAddress.zip')
    .matches(/^\d{5}(-\d{4})?$/)
    .withMessage('Please provide a valid ZIP code'),
  body('paymentMethod')
    .isIn(['credit_card', 'debit_card', 'paypal', 'stripe', 'cash_on_delivery'])
    .withMessage('Invalid payment method')
];

// Review validation
export const createReviewValidation: ValidationChain[] = [
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('title')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Title is required and must be less than 100 characters'),
  body('comment')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Comment is required and must be less than 1000 characters'),
  body('product')
    .optional()
    .isMongoId()
    .withMessage('Invalid product ID'),
  body('service')
    .optional()
    .isMongoId()
    .withMessage('Invalid service ID'),
  body()
    .custom((value) => {
      if (!value.product && !value.service) {
        throw new Error('Either product or service must be specified');
      }
      if (value.product && value.service) {
        throw new Error('Cannot review both product and service in the same review');
      }
      return true;
    })
];

export const updateReviewValidation: ValidationChain[] = [
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('title')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Title must be less than 100 characters'),
  body('comment')
    .optional()
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Comment must be less than 1000 characters')
];

export const reviewStatusValidation: ValidationChain[] = [
  body('status')
    .isIn(['approved', 'rejected'])
    .withMessage('Status must be either approved or rejected')
];

// Payment confirmation validation
export const createPaymentConfirmationValidation: ValidationChain[] = [
  body('amount')
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  body('paymentMethod')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Payment method is required and must be less than 50 characters'),
  body('proofImage')
    .notEmpty()
    .withMessage('Payment proof image is required'),
  body('order')
    .optional()
    .isMongoId()
    .withMessage('Invalid order ID'),
  body('appointment')
    .optional()
    .isMongoId()
    .withMessage('Invalid appointment ID'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Notes must be less than 500 characters'),
  body()
    .custom((value) => {
      if (!value.order && !value.appointment) {
        throw new Error('Either order or appointment must be specified');
      }
      if (value.order && value.appointment) {
        throw new Error('Cannot specify both order and appointment');
      }
      return true;
    })
];

export const updatePaymentConfirmationValidation: ValidationChain[] = [
  body('amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  body('paymentMethod')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Payment method must be less than 50 characters'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Notes must be less than 500 characters'),
  body('proofImage')
    .optional()
    .notEmpty()
    .withMessage('Payment proof image cannot be empty')
];

export const paymentConfirmationStatusValidation: ValidationChain[] = [
  body('status')
    .isIn(['verified', 'rejected'])
    .withMessage('Status must be either verified or rejected'),
  body('rejectionReason')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Rejection reason must be less than 200 characters')
];

// Query validation
export const paginationValidation: ValidationChain[] = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
];

export const mongoIdValidation = (field: string = 'id'): ValidationChain[] => [
  param(field)
    .isMongoId()
    .withMessage(`Please provide a valid ${field}`)
];
