@import "tailwindcss";

/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.scrollbar-thin::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Ensure horizontal scroll is always visible on mobile */
@media (max-width: 768px) {
  .overflow-x-auto {
    overflow-x: scroll !important;
    -webkit-overflow-scrolling: touch;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Custom utility classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for better accessibility */
.focus-visible:focus {
  outline: 2px solid #f3d016;
  outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #f3d016;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #d4b014;
}

/* Enhanced horizontal scrollbar for tables */
.table-scroll::-webkit-scrollbar {
  height: 12px;
}

.table-scroll::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 6px;
  margin: 0 8px;
}

.table-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, #f3d016, #e6c200);
  border-radius: 6px;
  border: 2px solid #f8f9fa;
}

.table-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #d4b014, #c7a600);
}

/* Enhanced scrollbar for desktop tables */
@media (min-width: 768px) {
  .table-scroll-left {
    position: relative;
    overflow-x: auto;
    /* Enhanced scrollbar styling without position changes */
  }

  /* Custom enhanced scrollbar styling */
  .table-scroll-left::-webkit-scrollbar {
    height: 16px;
  }

  .table-scroll-left::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 8px;
    margin: 0 12px;
    border: 2px solid #e2e8f0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .table-scroll-left::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 8px;
    border: 3px solid #f8fafc;
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
  }

  .table-scroll-left::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, #2563eb, #1e40af);
    box-shadow: 0 3px 8px rgba(37, 99, 235, 0.4);
    transform: scaleY(1.1);
  }

  /* Scrollbar corner styling */
  .table-scroll-left::-webkit-scrollbar-corner {
    background: #f8fafc;
    border-radius: 8px;
  }
}

/* Table responsive container */
.table-container {
  position: relative;
  overflow-x: auto;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: white;
}

/* Scroll indicators */
.table-container::before,
.table-container::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 20px;
  pointer-events: none;
  z-index: 10;
  transition: opacity 0.3s ease;
}

.table-container::before {
  left: 0;
  background: linear-gradient(90deg, rgba(255,255,255,0.9), transparent);
}

.table-container::after {
  right: 0;
  background: linear-gradient(270deg, rgba(255,255,255,0.9), transparent);
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-none {
  display: block;
  -webkit-line-clamp: unset;
  line-clamp: unset;
  -webkit-box-orient: unset;
  overflow: visible;
}

/* Improved button hover effects */
.btn-hover-lift {
  transition: all 0.2s ease;
}

.btn-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Loading skeleton animation */
@keyframes skeleton {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton 1.5s infinite;
}

/* Improved form focus states */
.form-input:focus {
  border-color: #f3d016;
  box-shadow: 0 0 0 3px rgba(243, 208, 22, 0.1);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Image overlay effects */
.image-overlay {
  position: relative;
  overflow: hidden;
}

.image-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(243, 208, 22, 0.1), rgba(255, 255, 0, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-overlay:hover::after {
  opacity: 1;
}

/* Responsive text utilities */
@media (max-width: 640px) {
  .text-responsive-xl {
    font-size: 2rem;
    line-height: 2.5rem;
  }
}

@media (min-width: 641px) {
  .text-responsive-xl {
    font-size: 3rem;
    line-height: 3.5rem;
  }
}

/* Responsive form and content utilities */
.form-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.wide-content {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
}

.wide-content::-webkit-scrollbar {
  height: 8px;
}

.wide-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.wide-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.wide-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Mobile responsive grid utilities */
@media (max-width: 640px) {
  .mobile-grid-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }

  .mobile-overflow-x {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-min-w-full {
    min-width: 100% !important;
  }

  .mobile-whitespace-nowrap {
    white-space: nowrap !important;
  }

  /* Mobile button improvements */
  .mobile-btn-stack {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  .mobile-btn-compact {
    padding: 0.5rem !important;
    font-size: 0.875rem !important;
  }

  .mobile-action-buttons {
    min-width: 120px !important;
    justify-content: center !important;
  }

  .mobile-action-buttons .space-x-2 > * + * {
    margin-left: 0.25rem !important;
  }

  /* Ensure buttons don't overlap */
  .mobile-safe-buttons {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.25rem !important;
    justify-content: center !important;
  }

  /* Mobile appointments improvements */
  .mobile-appointment-card {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  .mobile-appointment-header {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.75rem !important;
    margin-bottom: 1rem !important;
  }

  .mobile-appointment-title {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    line-height: 1.3 !important;
    margin: 0 !important;
  }

  .mobile-appointment-status {
    align-self: flex-start !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
    border-radius: 20px !important;
  }

  .mobile-appointment-details {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
    margin-bottom: 1rem !important;
  }

  .mobile-appointment-detail-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 0.5rem 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

  .mobile-appointment-detail-row:last-child {
    border-bottom: none !important;
  }

  .mobile-appointment-label {
    font-weight: 500 !important;
    color: #666 !important;
    font-size: 0.875rem !important;
  }

  .mobile-appointment-value {
    font-weight: 600 !important;
    color: #333 !important;
    font-size: 0.875rem !important;
    text-align: right !important;
  }

  .mobile-appointment-actions {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
    margin-top: 1rem !important;
    padding-top: 1rem !important;
    border-top: 1px solid #f0f0f0 !important;
  }

  .mobile-appointment-btn {
    width: 100% !important;
    padding: 0.75rem !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important;
  }

  .mobile-appointment-btn-primary {
    background-color: #3b82f6 !important;
    color: white !important;
    border: none !important;
  }

  .mobile-appointment-btn-secondary {
    background-color: #f8fafc !important;
    color: #64748b !important;
    border: 1px solid #e2e8f0 !important;
  }

  .mobile-appointment-btn-danger {
    background-color: #ef4444 !important;
    color: white !important;
    border: none !important;
  }

  /* Mobile payment proofs section */
  .mobile-payment-proofs {
    margin-top: 1rem !important;
    padding-top: 1rem !important;
    border-top: 1px solid #f0f0f0 !important;
  }

  .mobile-payment-proof-item {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    padding: 0.75rem !important;
    background: #f8fafc !important;
    border-radius: 8px !important;
    margin-bottom: 0.5rem !important;
  }

  .mobile-payment-proof-image {
    width: 40px !important;
    height: 40px !important;
    border-radius: 6px !important;
    object-fit: cover !important;
  }

  .mobile-payment-proof-details {
    flex: 1 !important;
    min-width: 0 !important;
  }

  .mobile-payment-proof-amount {
    font-weight: 600 !important;
    color: #333 !important;
    font-size: 0.875rem !important;
  }

  .mobile-payment-proof-method {
    color: #666 !important;
    font-size: 0.75rem !important;
  }

  .mobile-safe-buttons button {
    min-width: 36px !important;
    min-height: 36px !important;
    padding: 0.5rem !important;
  }

  /* Hide text on very small screens */
  .hidden.xs\:inline {
    display: none !important;
  }
}

/* Extra small screens (show text) */
@media (min-width: 475px) {
  .hidden.xs\:inline {
    display: inline !important;
  }
}

@media (min-width: 1024px) {
  .text-responsive-xl {
    font-size: 4rem;
    line-height: 4.5rem;
  }
}
