/**
 * Comprehensive search utility for admin dashboard
 */

/**
 * Search appointments across all relevant fields
 * @param {Array} appointments - Array of appointment objects
 * @param {string} searchTerm - The search term
 * @returns {Array} - Filtered appointments
 */
export const searchAppointments = (appointments, searchTerm) => {
  if (!searchTerm || !searchTerm.trim()) return appointments
  
  const term = searchTerm.toLowerCase().trim()
  
  return appointments.filter(appointment => {
    // Customer information search
    const customerName = appointment.customerInfo?.name || appointment.user?.name || ''
    const customerEmail = appointment.customerInfo?.email || appointment.user?.email || ''
    const customerPhone = appointment.customerInfo?.phone || appointment.user?.phone || ''
    
    // Service information search
    const serviceName = appointment.service?.name || appointment.service || ''
    const serviceDescription = appointment.service?.description || ''
    
    // Appointment details search
    const status = appointment.status || ''
    const notes = appointment.message || appointment.notes || ''
    const appointmentId = appointment._id || appointment.id || ''
    
    // Date search (formatted)
    const dateStr = appointment.date ? new Date(appointment.date).toLocaleDateString() : ''
    const timeStr = appointment.time || ''
    
    // Search across all fields
    return (
      customerName.toLowerCase().includes(term) ||
      customerEmail.toLowerCase().includes(term) ||
      customerPhone.toLowerCase().includes(term) ||
      serviceName.toLowerCase().includes(term) ||
      serviceDescription.toLowerCase().includes(term) ||
      status.toLowerCase().includes(term) ||
      notes.toLowerCase().includes(term) ||
      appointmentId.toLowerCase().includes(term) ||
      dateStr.toLowerCase().includes(term) ||
      timeStr.toLowerCase().includes(term)
    )
  })
}

/**
 * Search customers across all relevant fields
 * @param {Array} customers - Array of customer objects
 * @param {string} searchTerm - The search term
 * @returns {Array} - Filtered customers
 */
export const searchCustomers = (customers, searchTerm) => {
  if (!searchTerm || !searchTerm.trim()) return customers
  
  const term = searchTerm.toLowerCase().trim()
  
  return customers.filter(customer => {
    const firstName = customer.firstName || ''
    const lastName = customer.lastName || ''
    const fullName = customer.name || `${firstName} ${lastName}`.trim()
    const email = customer.email || ''
    const phone = customer.phone || ''
    const customerId = customer._id || customer.id || ''
    const address = customer.address || ''
    const city = customer.city || ''
    const state = customer.state || ''
    const zipCode = customer.zipCode || ''
    
    return (
      firstName.toLowerCase().includes(term) ||
      lastName.toLowerCase().includes(term) ||
      fullName.toLowerCase().includes(term) ||
      email.toLowerCase().includes(term) ||
      phone.toLowerCase().includes(term) ||
      customerId.toLowerCase().includes(term) ||
      address.toLowerCase().includes(term) ||
      city.toLowerCase().includes(term) ||
      state.toLowerCase().includes(term) ||
      zipCode.toLowerCase().includes(term)
    )
  })
}

/**
 * Search orders across all relevant fields
 * @param {Array} orders - Array of order objects
 * @param {string} searchTerm - The search term
 * @returns {Array} - Filtered orders
 */
export const searchOrders = (orders, searchTerm) => {
  if (!searchTerm || !searchTerm.trim()) return orders
  
  const term = searchTerm.toLowerCase().trim()
  
  return orders.filter(order => {
    const customerName = order.customer?.name || order.customerName || ''
    const customerEmail = order.customer?.email || order.customerEmail || ''
    const orderId = order._id || order.id || order.orderNumber || ''
    const status = order.status || ''
    const total = order.total?.toString() || ''
    const paymentMethod = order.paymentMethod || ''
    const shippingAddress = order.shippingAddress || ''
    
    // Search in order items
    const itemsMatch = order.items?.some(item => 
      (item.name || '').toLowerCase().includes(term) ||
      (item.description || '').toLowerCase().includes(term) ||
      (item.sku || '').toLowerCase().includes(term)
    ) || false
    
    return (
      customerName.toLowerCase().includes(term) ||
      customerEmail.toLowerCase().includes(term) ||
      orderId.toLowerCase().includes(term) ||
      status.toLowerCase().includes(term) ||
      total.toLowerCase().includes(term) ||
      paymentMethod.toLowerCase().includes(term) ||
      shippingAddress.toLowerCase().includes(term) ||
      itemsMatch
    )
  })
}

/**
 * Search products across all relevant fields
 * @param {Array} products - Array of product objects
 * @param {string} searchTerm - The search term
 * @returns {Array} - Filtered products
 */
export const searchProducts = (products, searchTerm) => {
  if (!searchTerm || !searchTerm.trim()) return products
  
  const term = searchTerm.toLowerCase().trim()
  
  return products.filter(product => {
    const name = product.name || ''
    const description = product.description || ''
    const category = product.category || ''
    const sku = product.sku || ''
    const brand = product.brand || ''
    const tags = Array.isArray(product.tags) ? product.tags.join(' ') : ''
    const price = product.price?.toString() || ''
    const productId = product._id || product.id || ''
    
    return (
      name.toLowerCase().includes(term) ||
      description.toLowerCase().includes(term) ||
      category.toLowerCase().includes(term) ||
      sku.toLowerCase().includes(term) ||
      brand.toLowerCase().includes(term) ||
      tags.toLowerCase().includes(term) ||
      price.toLowerCase().includes(term) ||
      productId.toLowerCase().includes(term)
    )
  })
}

/**
 * Search services across all relevant fields
 * @param {Array} services - Array of service objects
 * @param {string} searchTerm - The search term
 * @returns {Array} - Filtered services
 */
export const searchServices = (services, searchTerm) => {
  if (!searchTerm || !searchTerm.trim()) return services
  
  const term = searchTerm.toLowerCase().trim()
  
  return services.filter(service => {
    const name = service.name || ''
    const description = service.description || ''
    const category = service.category || ''
    const price = service.price?.toString() || ''
    const duration = service.duration?.toString() || ''
    const serviceId = service._id || service.id || ''
    
    return (
      name.toLowerCase().includes(term) ||
      description.toLowerCase().includes(term) ||
      category.toLowerCase().includes(term) ||
      price.toLowerCase().includes(term) ||
      duration.toLowerCase().includes(term) ||
      serviceId.toLowerCase().includes(term)
    )
  })
}

/**
 * Generic search function that can be used for any array of objects
 * @param {Array} items - Array of items to search
 * @param {string} searchTerm - The search term
 * @param {Array} searchFields - Array of field paths to search in
 * @returns {Array} - Filtered items
 */
export const genericSearch = (items, searchTerm, searchFields) => {
  if (!searchTerm || !searchTerm.trim()) return items
  
  const term = searchTerm.toLowerCase().trim()
  
  return items.filter(item => {
    return searchFields.some(field => {
      const value = getNestedValue(item, field)
      return value && value.toString().toLowerCase().includes(term)
    })
  })
}

/**
 * Helper function to get nested object values
 * @param {Object} obj - The object
 * @param {string} path - The path (e.g., 'customer.name' or 'service.price')
 * @returns {any} - The value at the path
 */
const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}
