import { Router } from 'express';
import { AuthController } from '../controllers';
import { authenticate } from '../middleware/auth';
import { validate } from '../middleware/validation';
import {
  registerValidation,
  loginValidation,
  forgotPasswordValidation
} from '../utils/validation';

const router = Router();

// POST /api/auth/register
router.post(
  '/register',
  validate(registerValidation),
  AuthController.register
);

// POST /api/auth/login
router.post(
  '/login',
  validate(loginValidation),
  AuthController.login
);

// POST /api/auth/forgot-password
router.post(
  '/forgot-password',
  validate(forgotPasswordValidation),
  AuthController.forgotPassword
);

// POST /api/auth/reset-password
router.post(
  '/reset-password',
  AuthController.resetPassword
);

// GET /api/auth/verify
router.get(
  '/verify',
  authenticate,
  AuthController.verify
);

// POST /api/auth/logout
router.post(
  '/logout',
  authenticate,
  AuthController.logout
);

export default router;
