import { useState, useEffect } from 'react';
import { adminAPI, getCurrentUser, type AdminAppointment, type Customer } from '../../utils/api';

// Extended admin appointment interface with totalPrice
interface ExtendedAdminAppointment extends AdminAppointment {
  totalPrice?: number;
}

// Extended customer interface with role
interface ExtendedCustomer extends Customer {
  role?: string;
}
import AppointmentManagement from '../Admin/AppointmentManagement';
// Analytics import removed - not currently used
import LoadingSpinner from '../LoadingSpinner';
import ServiceModal from '../ServiceModal';
// Real-time updates removed

interface AdminDashboardProps {
  onLogout: () => void;
}

export default function AdminDashboard({ onLogout }: AdminDashboardProps) {
  const [user] = useState(getCurrentUser() || { id: '', firstName: 'Admin', lastName: 'User', email: '', phone: '', role: 'admin' as const, createdAt: '', password: '' });
  const [users, setUsers] = useState<Customer[]>([]);
  const [appointments, setAppointments] = useState<AdminAppointment[]>([]);
  const [activeTab, setActiveTab] = useState<'dashboard' | 'appointments' | 'customers' | 'services' | 'settings'>('dashboard');
  const [dashboardData, setDashboardData] = useState<{
    services?: any[];
    appointments?: any[];
    customers?: any[];
    stats?: any;
    overview?: {
      totalAppointments?: number;
      todayAppointments?: number;
      totalUsers?: number;
      totalRevenue?: number;
    };
    recentAppointments?: any[];
    statistics?: {
      appointments?: Record<string, number>;
    };
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [editingService, setEditingService] = useState<any>(null);
  const [activeSettingsTab, setActiveSettingsTab] = useState('site');
  // Remove duplicate login logic since authentication is handled at route level

  useEffect(() => {
    loadData();
  }, [activeTab]); // Reload data when tab changes

  // Load dashboard data on initial mount
  useEffect(() => {
    if (activeTab === 'dashboard') {
      loadData();
    }
  }, []); // Run once on mount

  // Real-time updates removed - users will refresh when needed

  const loadData = async () => {
    try {
      setLoading(true);

      console.log('🔄 Loading dashboard data...');
      console.log('🔑 Auth token:', localStorage.getItem('authToken'));
      console.log('👤 Current user:', localStorage.getItem('currentUser'));

      // Only load data for the current active tab to avoid unnecessary API calls
      if (activeTab === 'dashboard') {
        // Load dashboard overview data
        const response = await adminAPI.getDashboard();
        console.log('📊 Dashboard API response:', response);

        if (response.success) {
          console.log('✅ Dashboard data loaded successfully:', response.data);
          setDashboardData(response.data);
        } else {
          console.error('❌ Dashboard API failed:', response);
        }
      } else if (activeTab === 'customers') {
        // Load customers data only
        const response = await adminAPI.getCustomers();
        console.log('👥 Customers API response:', response);

        if (response.success) {
          console.log('✅ Customers data loaded successfully:', response.data);
          setUsers(response.data.customers || []);
        } else {
          console.error('❌ Customers API failed:', response);
        }
      } else if (activeTab === 'appointments') {
        // Load appointments data only
        const response = await adminAPI.getAppointments();
        console.log('📅 Appointments API response:', response);

        if (response.success) {
          console.log('✅ Appointments data loaded successfully:', response.data);
          setAppointments(response.data.appointments || []);
        } else {
          console.error('❌ Appointments API failed:', response);
        }
      } else if (activeTab === 'services') {
        // Load services data only
        const response = await adminAPI.getServices();
        console.log('🛠️ Services API response:', response);

        if (response.success) {
          console.log('✅ Services data loaded successfully:', response.data);
          setDashboardData(prev => ({
            ...prev,
            services: response.data.services || []
          }));
        } else {
          console.error('❌ Services API failed:', response);
        }
      }
    } catch (error) {
      console.error('💥 Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Service management functions
  const handleEditService = (service: any) => {
    setEditingService(service);
    setShowServiceModal(true);
  };

  const handleDeleteService = async (serviceId: string) => {
    if (window.confirm('Are you sure you want to delete this service?')) {
      try {
        await adminAPI.deleteService(serviceId);
        loadData(); // Reload data
      } catch (error) {
        console.error('Error deleting service:', error);
        alert('Failed to delete service');
      }
    }
  };

  const handleSaveService = async (serviceData: any) => {
    try {
      if (editingService) {
        await adminAPI.updateService(editingService.id, serviceData);
      } else {
        await adminAPI.createService(serviceData);
      }
      setShowServiceModal(false);
      setEditingService(null);
      loadData(); // Reload data
    } catch (error) {
      console.error('Error saving service:', error);
      alert('Failed to save service');
    }
  };

  // Login logic removed - handled at route level

  const handleLogout = () => {
    // Clear any local storage and call parent logout
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
    onLogout();
  };

  // Note: Appointment management functions moved to AppointmentManagement component

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'completed': return '#6c757d';
      case 'cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  // Analytics calculations
  const totalRevenue = appointments
    .filter(apt => apt.status === 'completed')
    .reduce((sum, apt) => sum + ((apt as ExtendedAdminAppointment).totalPrice || 0), 0);

  // Note: pendingRevenue calculation removed as it's not used

  const todayAppointments = appointments.filter(apt => {
    const today = new Date().toDateString();
    return new Date(apt.date).toDateString() === today;
  });

  const upcomingAppointments = appointments.filter(apt => 
    new Date(apt.date) >= new Date() && apt.status !== 'cancelled'
  );

  // Authentication is now handled at route level

  if (!user || user.role !== 'admin') {
    return <div>Access denied. Admin privileges required.</div>;
  }

  return (
    <div className="dashboard-container admin" style={{ minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      {/* Header */}
      <header className="dashboard-header" style={{
        backgroundColor: '#fff',
        borderBottom: '1px solid #e2e8f0',
        padding: '1rem',
        position: 'sticky',
        top: 0,
        zIndex: 1000
      }}>
        <div className="dashboard-header-content" style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          maxWidth: '1200px',
          margin: '0 auto',
          flexWrap: 'wrap',
          gap: '1rem'
        }}>
          <div className="dashboard-logo">
            <h1 style={{
              fontSize: 'clamp(1.2rem, 4vw, 1.8rem)',
              fontWeight: 'bold',
              color: '#1a202c',
              margin: 0
            }}>dammyspicybeauty - Admin</h1>
          </div>
          <div className="dashboard-user-menu" style={{
            display: 'flex',
            alignItems: 'center',
            gap: '1rem',
            flexWrap: 'wrap'
          }}>
            <span className="welcome-text" style={{
              fontSize: 'clamp(0.8rem, 2vw, 1rem)',
              color: '#4a5568'
            }}>Welcome, {user.firstName}!</span>
            <button className="logout-button" onClick={handleLogout} style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#e53e3e',
              color: 'white',
              border: 'none',
              borderRadius: '0.375rem',
              fontSize: '0.875rem',
              cursor: 'pointer'
            }}>
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main" style={{ padding: '1rem', maxWidth: '1200px', margin: '0 auto' }}>
        <div className="dashboard-content">
          {/* Navigation Tabs */}
          <div className="dashboard-tabs" style={{
            display: 'flex',
            gap: '0.5rem',
            marginBottom: '2rem',
            overflowX: 'auto',
            padding: '0.5rem 0',
            borderBottom: '1px solid #e2e8f0'
          }}>
            <button
              className={`tab-button ${activeTab === 'dashboard' ? 'active' : ''}`}
              onClick={() => setActiveTab('dashboard')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'dashboard' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'dashboard' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              📊 DASHBOARD
            </button>
            <button
              className={`tab-button ${activeTab === 'appointments' ? 'active' : ''}`}
              onClick={() => setActiveTab('appointments')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'appointments' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'appointments' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              📅 APPOINTMENTS
            </button>
            <button
              className={`tab-button ${activeTab === 'customers' ? 'active' : ''}`}
              onClick={() => setActiveTab('customers')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'customers' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'customers' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              👥 CUSTOMERS
            </button>
            <button
              className={`tab-button ${activeTab === 'services' ? 'active' : ''}`}
              onClick={() => setActiveTab('services')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'services' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'services' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              🛍️ SERVICES
            </button>
            <button
              className={`tab-button ${activeTab === 'settings' ? 'active' : ''}`}
              onClick={() => setActiveTab('settings')}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: 'clamp(0.7rem, 2vw, 0.875rem)',
                fontWeight: '500',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                backgroundColor: activeTab === 'settings' ? '#3b82f6' : '#f1f5f9',
                color: activeTab === 'settings' ? 'white' : '#64748b',
                transition: 'all 0.2s'
              }}
            >
              ⚙️ SETTINGS
            </button>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'dashboard' && (
              <div className="overview-section">
                {loading ? (
                  <LoadingSpinner message="Loading dashboard data..." />
                ) : (
                  <>
                    <div className="stats-grid admin-stats" style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                      gap: '1rem',
                      marginBottom: '2rem'
                    }}>
                      <div className="stat-card" style={{
                        backgroundColor: 'white',
                        padding: '1.5rem',
                        borderRadius: '0.75rem',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                        textAlign: 'center'
                      }}>
                        <h3 style={{
                          fontSize: 'clamp(1.5rem, 4vw, 2rem)',
                          fontWeight: 'bold',
                          color: '#1a202c',
                          margin: '0 0 0.5rem 0'
                        }}>{dashboardData?.overview?.totalAppointments || appointments.length}</h3>
                        <p style={{
                          fontSize: 'clamp(0.8rem, 2vw, 0.875rem)',
                          color: '#64748b',
                          margin: 0
                        }}>Total Appointments</p>
                      </div>
                      <div className="stat-card" style={{
                        backgroundColor: 'white',
                        padding: '1.5rem',
                        borderRadius: '0.75rem',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                        textAlign: 'center'
                      }}>
                        <h3 style={{
                          fontSize: 'clamp(1.5rem, 4vw, 2rem)',
                          fontWeight: 'bold',
                          color: '#1a202c',
                          margin: '0 0 0.5rem 0'
                        }}>{dashboardData?.overview?.todayAppointments || todayAppointments.length}</h3>
                        <p style={{
                          fontSize: 'clamp(0.8rem, 2vw, 0.875rem)',
                          color: '#64748b',
                          margin: 0
                        }}>Today's Appointments</p>
                      </div>
                      <div className="stat-card" style={{
                        backgroundColor: 'white',
                        padding: '1.5rem',
                        borderRadius: '0.75rem',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                        textAlign: 'center'
                      }}>
                        <h3 style={{
                          fontSize: 'clamp(1.5rem, 4vw, 2rem)',
                          fontWeight: 'bold',
                          color: '#1a202c',
                          margin: '0 0 0.5rem 0'
                        }}>{dashboardData?.overview?.totalUsers || users.filter(u => (u as ExtendedCustomer).role === 'user').length}</h3>
                        <p style={{
                          fontSize: 'clamp(0.8rem, 2vw, 0.875rem)',
                          color: '#64748b',
                          margin: 0
                        }}>Total Clients</p>
                      </div>
                      <div className="stat-card" style={{
                        backgroundColor: 'white',
                        padding: '1.5rem',
                        borderRadius: '0.75rem',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                        textAlign: 'center'
                      }}>
                        <h3 style={{
                          fontSize: 'clamp(1.5rem, 4vw, 2rem)',
                          fontWeight: 'bold',
                          color: '#1a202c',
                          margin: '0 0 0.5rem 0'
                        }}>${(dashboardData?.overview?.totalRevenue || totalRevenue || 0).toFixed(2)}</h3>
                        <p style={{
                          fontSize: 'clamp(0.8rem, 2vw, 0.875rem)',
                          color: '#64748b',
                          margin: 0
                        }}>Total Revenue</p>
                      </div>
                    </div>

                    <div className="recent-activity">
                      <h3>Recent Appointments</h3>
                      <div className="appointments-list compact">
                        {(dashboardData?.recentAppointments || upcomingAppointments).slice(0, 5).map((appointment: any) => (
                          <div key={appointment.id || appointment._id} className="appointment-card compact">
                            <div className="appointment-header">
                              <h4>
                                {appointment.customerInfo?.name ||
                                 `${appointment.customerInfo?.firstName || ''} ${appointment.customerInfo?.lastName || ''}`.trim() ||
                                 appointment.user?.name || 'Guest'}
                              </h4>
                              <span
                                className="status-badge"
                                style={{ backgroundColor: getStatusColor(appointment.status) }}
                              >
                                {appointment.status.toUpperCase()}
                              </span>
                            </div>
                            <div className="appointment-details">
                              <p>{appointment.service?.name || appointment.serviceName}</p>
                              <p>{formatDate(appointment.date)} at {appointment.time}</p>
                              <p>${(appointment.service?.price || appointment.totalPrice || 0).toFixed(2)}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Appointment Status Overview */}
                    {dashboardData?.statistics?.appointments && (
                      <div className="status-overview">
                        <h3>Appointment Status Overview</h3>
                        <div className="status-grid">
                          {Object.entries(dashboardData.statistics.appointments).map(([status, count]: [string, any]) => (
                            <div key={status} className="status-stat">
                              <div className="status-count">{count}</div>
                              <div className="status-label">{status.charAt(0).toUpperCase() + status.slice(1)}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            )}

            {activeTab === 'appointments' && (
              <AppointmentManagement onAppointmentUpdate={loadData} />
            )}

            {activeTab === 'customers' && (
              <div className="users-section">
                <h3>Client Management</h3>
                <div className="users-list">
                  {users.map(customer => {
                    return (
                      <div key={customer.id} className="user-card">
                        <div className="user-header">
                          <h4>{customer.firstName} {customer.lastName}</h4>
                          <span className="user-stats">{customer.totalAppointments || 0} appointments</span>
                        </div>
                        <div className="user-details">
                          <p><strong>Email:</strong> {customer.email}</p>
                          <p><strong>Phone:</strong> {customer.phone}</p>
                          <p><strong>Member Since:</strong> {new Date(customer.createdAt).toLocaleDateString()}</p>
                          <p><strong>Total Spent:</strong> ${(customer.totalSpent || 0).toFixed(2)}</p>
                          <p><strong>Last Appointment:</strong> {customer.lastAppointment ? new Date(customer.lastAppointment).toLocaleDateString() : 'Never'}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {activeTab === 'services' && (
              <div className="services-section bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex justify-between items-center mb-6">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">Service Management</h3>
                    <p className="text-gray-600">Manage your service offerings and pricing</p>
                  </div>
                  <button
                    onClick={() => setShowServiceModal(true)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 shadow-sm"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Add Service
                  </button>
                </div>

                {/* Services Grid */}
                {dashboardData?.services && dashboardData.services.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {dashboardData.services.map((service: any) => (
                      <div key={service.id || service._id} className="bg-gray-50 rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
                        <div className="flex justify-between items-start mb-4">
                          <div className="flex-1">
                            <h4 className="text-lg font-semibold text-gray-900 mb-1">{service.name}</h4>
                            <p className="text-sm text-gray-600 bg-gray-200 px-2 py-1 rounded-full inline-block">{service.category}</p>
                          </div>
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ml-2 ${
                            service.isActive
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {service.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>

                        <p className="text-gray-700 text-sm mb-4 line-clamp-3 min-h-[60px]">{service.description}</p>

                        <div className="flex justify-between items-center mb-4">
                          <div className="text-lg font-bold text-green-600">
                            ${service.price}
                          </div>
                        </div>

                        <div className="flex gap-2">
                          <button
                            onClick={() => handleEditService(service)}
                            className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteService(service.id || service._id)}
                            className="flex-1 bg-red-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <div className="text-gray-400 text-6xl mb-4">🛠️</div>
                    <h4 className="text-lg font-medium text-gray-900 mb-2">No Services Yet</h4>
                    <p className="text-gray-600 mb-4">Start by adding your first service offering</p>
                    <button
                      onClick={() => setShowServiceModal(true)}
                      className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
                    >
                      Add Your First Service
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="settings-section">
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-gray-900">Admin Settings</h3>
                  <p className="text-gray-600">Manage your site configuration and preferences</p>
                </div>

                {/* Settings Tabs */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                  <div className="border-b border-gray-200 bg-gray-50">
                    <nav className="flex space-x-0" aria-label="Settings tabs">
                      {['site', 'payment', 'theme', 'branding'].map((tab) => (
                        <button
                          key={tab}
                          onClick={() => setActiveSettingsTab(tab)}
                          className={`flex-1 py-4 px-6 border-b-2 font-medium text-sm capitalize transition-all duration-200 ${
                            activeSettingsTab === tab
                              ? 'border-blue-500 text-blue-600 bg-white'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          {tab} Settings
                        </button>
                      ))}
                    </nav>
                  </div>

                  <div className="p-8">
                    {activeSettingsTab === 'site' && (
                      <div className="space-y-8">
                        <div className="border-b border-gray-200 pb-4">
                          <h4 className="text-xl font-semibold text-gray-900">Site Configuration</h4>
                          <p className="text-sm text-gray-600 mt-1">Manage your basic site information and contact details</p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">
                              Site Name *
                            </label>
                            <input
                              type="text"
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                              placeholder="Your Site Name"
                            />
                          </div>
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">
                              Contact Email *
                            </label>
                            <input
                              type="email"
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                              placeholder="<EMAIL>"
                            />
                          </div>
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">
                              Contact Phone
                            </label>
                            <input
                              type="tel"
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                              placeholder="+****************"
                            />
                          </div>
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">
                              Business Address
                            </label>
                            <input
                              type="text"
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                              placeholder="123 Main St, City, State 12345"
                            />
                          </div>
                        </div>
                        <div className="flex justify-end pt-6 border-t border-gray-200">
                          <button className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium">
                            Save Site Settings
                          </button>
                        </div>
                      </div>
                    )}

                    {activeSettingsTab === 'payment' && (
                      <div className="space-y-8">
                        <div className="border-b border-gray-200 pb-4">
                          <h4 className="text-xl font-semibold text-gray-900">Payment Configuration</h4>
                          <p className="text-sm text-gray-600 mt-1">Configure your accepted payment methods and details</p>
                        </div>
                        <div className="space-y-6">
                          <div className="bg-gray-50 p-6 rounded-lg">
                            <label className="block text-sm font-medium text-gray-700 mb-4">
                              Accepted Payment Methods
                            </label>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                              {['CashApp', 'Zelle', 'Cash', 'Credit Card'].map((method) => (
                                <label key={method} className="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer">
                                  <input
                                    type="checkbox"
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    defaultChecked={method === 'CashApp' || method === 'Zelle'}
                                  />
                                  <span className="ml-3 text-sm font-medium text-gray-700">{method}</span>
                                </label>
                              ))}
                            </div>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <label className="block text-sm font-medium text-gray-700">
                                CashApp Handle
                              </label>
                              <input
                                type="text"
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                placeholder="$YourCashApp"
                              />
                            </div>
                            <div className="space-y-2">
                              <label className="block text-sm font-medium text-gray-700">
                                Zelle Email/Phone
                              </label>
                              <input
                                type="text"
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                placeholder="<EMAIL> or phone"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex justify-end pt-6 border-t border-gray-200">
                          <button className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium">
                            Save Payment Settings
                          </button>
                        </div>
                      </div>
                    )}

                    {activeSettingsTab === 'theme' && (
                      <div className="space-y-8">
                        <div className="border-b border-gray-200 pb-4">
                          <h4 className="text-xl font-semibold text-gray-900">Theme Customization</h4>
                          <p className="text-sm text-gray-600 mt-1">Customize your site's visual appearance and branding colors</p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                          <div className="space-y-4">
                            <label className="block text-sm font-medium text-gray-700">
                              Primary Color
                            </label>
                            <div className="flex items-center space-x-4">
                              <input
                                type="color"
                                className="w-16 h-12 border border-gray-300 rounded-lg cursor-pointer"
                                defaultValue="#3B82F6"
                              />
                              <div className="flex-1">
                                <input
                                  type="text"
                                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  defaultValue="#3B82F6"
                                  placeholder="#3B82F6"
                                />
                              </div>
                            </div>
                          </div>
                          <div className="space-y-4">
                            <label className="block text-sm font-medium text-gray-700">
                              Secondary Color
                            </label>
                            <div className="flex items-center space-x-4">
                              <input
                                type="color"
                                className="w-16 h-12 border border-gray-300 rounded-lg cursor-pointer"
                                defaultValue="#10B981"
                              />
                              <div className="flex-1">
                                <input
                                  type="text"
                                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  defaultValue="#10B981"
                                  placeholder="#10B981"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="bg-gray-50 p-6 rounded-lg">
                          <h5 className="text-sm font-medium text-gray-700 mb-3">Preview</h5>
                          <div className="flex space-x-4">
                            <div className="w-20 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white text-xs font-medium">Primary</div>
                            <div className="w-20 h-12 bg-green-500 rounded-lg flex items-center justify-center text-white text-xs font-medium">Secondary</div>
                          </div>
                        </div>
                        <div className="flex justify-end pt-6 border-t border-gray-200">
                          <button className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium">
                            Save Theme Settings
                          </button>
                        </div>
                      </div>
                    )}

                    {activeSettingsTab === 'branding' && (
                      <div className="space-y-8">
                        <div className="border-b border-gray-200 pb-4">
                          <h4 className="text-xl font-semibold text-gray-900">Branding & Content</h4>
                          <p className="text-sm text-gray-600 mt-1">Manage your business branding and content that appears on your site</p>
                        </div>
                        <div className="space-y-6">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <label className="block text-sm font-medium text-gray-700">
                                Business Name *
                              </label>
                              <input
                                type="text"
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                placeholder="Your Business Name"
                              />
                            </div>
                            <div className="space-y-2">
                              <label className="block text-sm font-medium text-gray-700">
                                Tagline
                              </label>
                              <input
                                type="text"
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                placeholder="Your business tagline"
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">
                              About Your Business
                            </label>
                            <textarea
                              rows={5}
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                              placeholder="Tell customers about your business, your experience, and what makes you special..."
                            />
                            <p className="text-xs text-gray-500">This text will appear on your homepage and about section.</p>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <label className="block text-sm font-medium text-gray-700">
                                Instagram Handle
                              </label>
                              <input
                                type="text"
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                placeholder="@yourbusiness"
                              />
                            </div>
                            <div className="space-y-2">
                              <label className="block text-sm font-medium text-gray-700">
                                Facebook Page
                              </label>
                              <input
                                type="text"
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                placeholder="facebook.com/yourbusiness"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex justify-end pt-6 border-t border-gray-200">
                          <button className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium">
                            Save Branding Settings
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Service Modal */}
      <ServiceModal
        isOpen={showServiceModal}
        onClose={() => {
          setShowServiceModal(false);
          setEditingService(null);
        }}
        onSave={handleSaveService}
        service={editingService}
      />
    </div>
  );
}
