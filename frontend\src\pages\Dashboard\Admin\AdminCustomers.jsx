import { useState, useMemo } from 'react'
import { <PERSON><PERSON>sers, FiSearch, <PERSON>Eye, FiEdit3, FiTrash2, FiPlus } from 'react-icons/fi'
import CustomerModal from '../../../components/Modals/CustomerModal'
import CustomerDetailModal from '../../../components/Modals/CustomerDetailModal'
import { adminService } from '../../../services'
import { useDebouncedSearch } from '../../../hooks/useDebounce'
import { searchCustomers } from '../../../utils/searchUtils'
import { useApiWithToast, TOAST_MESSAGES } from '../../../utils/apiWithToast'

const AdminCustomers = ({
  customers,
  sectionLoading,
  searchTerm,
  setSearchTerm,
  showAddModal,
  setShowAddModal,
  modalType,
  setModalType,
  editingItem,
  setEditingItem,
  setViewingItem,
  showToast,
  setConfirmDialog,
  handleDeleteCustomer,
  branding,
  refreshData,
  refreshAllData
}) => {
  // State for detail modal
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [detailItem, setDetailItem] = useState(null)

  // Toast notifications
  const { executeWithToast, showSuccess, showError } = useApiWithToast()

  // Debug: Log the customers data to see what we're getting (reduced logging)
  // console.log('AdminCustomers received customers:', customers, 'Type:', typeof customers, 'Is Array:', Array.isArray(customers))

  // Ensure customers is always an array - handle backend response structure
  const customersArray = Array.isArray(customers)
    ? customers
    : (customers?.customers && Array.isArray(customers.customers))
      ? customers.customers
    : (customers?.data?.customers && Array.isArray(customers.data.customers))
      ? customers.data.customers
      : (customers?.data && Array.isArray(customers.data))
        ? customers.data
        : []

  // Use debounced search for better performance
  const debouncedSearchTerm = useDebouncedSearch(searchTerm, 150)

  // Use memoized filtering for better performance
  const filteredCustomers = useMemo(() => {
    return searchCustomers(customersArray, debouncedSearchTerm)
  }, [customersArray, debouncedSearchTerm])

  // Handlers for detail modal
  const handleViewDetails = (customer) => {
    setDetailItem(customer)
    setShowDetailModal(true)
  }

  const handleEditFromDetail = (customer) => {
    setShowDetailModal(false)
    setEditingItem(customer)
    setModalType('edit')
    setShowAddModal(true)
  }

  const handleDeleteFromDetail = async (customerId) => {
    await handleDeleteCustomer(customerId)
    setShowDetailModal(false)
    // Background refresh is already handled in handleDeleteCustomer
  }

  const handleSaveCustomer = async (customerData) => {
    if (editingItem) {
      await executeWithToast(
        () => adminService.updateCustomer(editingItem.id, customerData),
        {
          loadingMessage: 'Updating customer...',
          successMessage: TOAST_MESSAGES.UPDATE_SUCCESS,
          errorMessage: 'Failed to update customer',
          onSuccess: () => {
            setShowAddModal(false)
            setEditingItem(null)
            // Trigger background refresh for all admin data
            if (refreshAllData) refreshAllData()
          }
        }
      )
    } else {
      await executeWithToast(
        () => adminService.createCustomer(customerData),
        {
          loadingMessage: 'Creating customer...',
          successMessage: TOAST_MESSAGES.CREATE_SUCCESS,
          errorMessage: 'Failed to create customer',
          onSuccess: () => {
            setShowAddModal(false)
            setEditingItem(null)
            // Trigger background refresh for all admin data
            if (refreshAllData) refreshAllData()
          }
        }
      )
    }
  }

  const handleEditCustomer = (customer) => {
    setEditingItem(customer)
    setModalType('customer')
    setShowAddModal(true)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Customer Management</h2>
          <p className="text-gray-600 mt-1">View and manage your customer database</p>
        </div>
        <button
          onClick={() => {
            setEditingItem(null)
            setModalType('customer')
            setShowAddModal(true)
          }}
          className="flex items-center justify-center px-4 sm:px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02] w-full sm:w-auto text-sm sm:text-base"
        >
          <FiPlus className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
          <span className="hidden xs:inline">Add </span>Customer
        </button>
      </div>

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        {/* Search */}
        <div className="p-6 border-b border-gray-200">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search customers by name, email, or phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
          </div>
        </div>

        {sectionLoading?.customers ? (
          <div className="overflow-x-auto table-scroll table-scroll-left">
            <table className="min-w-full divide-y divide-gray-200" style={{ minWidth: '800px' }}>
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '200px' }}>
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '180px' }}>
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                    Join Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                    Appointments
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                    Total Spent
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {[...Array(5)].map((_, index) => (
                  <tr key={index} className="animate-pulse">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                        <div className="ml-4 space-y-2">
                          <div className="h-4 bg-gray-300 rounded w-24"></div>
                          <div className="h-3 bg-gray-300 rounded w-32"></div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        <div className="h-4 bg-gray-300 rounded w-20"></div>
                        <div className="h-3 bg-gray-300 rounded w-24"></div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-300 rounded w-16"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-300 rounded w-8"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-300 rounded w-12"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex justify-end space-x-2">
                        <div className="h-8 w-8 bg-gray-300 rounded"></div>
                        <div className="h-8 w-8 bg-gray-300 rounded"></div>
                        <div className="h-8 w-8 bg-gray-300 rounded"></div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : filteredCustomers.length > 0 ? (
          <>
            {/* Mobile Card Layout */}
            <div className="block md:hidden">
              <div className="space-y-4 p-4">
                {filteredCustomers.map((customer, index) => (
                  <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                          <span className="text-white font-semibold text-sm">
                            {(customer.name || customer.firstName || 'U').charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="ml-4">
                          <div className="font-medium text-gray-900">{customer.name || customer.firstName + ' ' + customer.lastName}</div>
                          <div className="text-sm text-gray-500">{customer.email}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleViewDetails(customer)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="View Details"
                        >
                          <FiEye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleEditCustomer(customer)}
                          className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="Edit"
                        >
                          <FiEdit3 className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setConfirmDialog({
                              title: 'Delete Customer',
                              message: `Are you sure you want to delete ${customer.name || customer.firstName + ' ' + customer.lastName}? This action cannot be undone.`,
                              onConfirm: () => handleDeleteCustomer(customer._id || customer.id)
                            })
                          }}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="Delete"
                        >
                          <FiTrash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Phone:</span>
                        <div className="font-medium">{customer.phone || 'N/A'}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Join Date:</span>
                        <div className="font-medium">
                          {customer.joinDate || customer.createdAt ?
                            new Date(customer.joinDate || customer.createdAt).toLocaleDateString() :
                            'Recently'
                          }
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500">Appointments:</span>
                        <div className="font-medium">{customer.appointments || 0}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Total Spent:</span>
                        <div className="font-medium">${customer.totalSpent || 0}</div>
                      </div>
                    </div>
                    {customer.address && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <span className="text-gray-500 text-sm">Address:</span>
                        <div className="text-sm">{customer.address}</div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Desktop Table Layout */}
            <div className="hidden md:block overflow-x-auto table-scroll table-scroll-left">
              <table className="min-w-full divide-y divide-gray-200" style={{ minWidth: '800px' }}>
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '200px' }}>
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '180px' }}>
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                  Join Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                  Appointments
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                  Total Spent
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: '120px' }}>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCustomers.map((customer, index) => (
                <tr key={index} className="hover:bg-gray-50 transition-colors duration-200">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                        <span className="text-white font-semibold text-sm">
                          {(customer.name || customer.firstName || 'U').charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div className="ml-4">
                        <div className="font-medium text-gray-900">{customer.name || customer.firstName + ' ' + customer.lastName}</div>
                        <div className="text-sm text-gray-500">{customer.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>{customer.phone}</div>
                    <div className="text-gray-500">{customer.address}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {customer.joinDate || customer.createdAt ? 
                      new Date(customer.joinDate || customer.createdAt).toLocaleDateString() : 
                      'Recently'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {customer.appointments || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${customer.totalSpent || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2 mobile-safe-buttons">
                      <button
                        onClick={() => handleViewDetails(customer)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        title="View Details"
                      >
                        <FiEye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEditCustomer(customer)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        title="Edit"
                      >
                        <FiEdit3 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          setConfirmDialog({
                            title: 'Delete Customer',
                            message: `Are you sure you want to delete "${customer.firstName} ${customer.lastName}"? This action cannot be undone.`,
                            onConfirm: () => handleDeleteCustomer(customer._id || customer.id)
                          })
                        }}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        title="Delete"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <FiUsers className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm 
                ? 'Try adjusting your search criteria.'
                : 'Get started by adding your first customer.'
              }
            </p>
            {!searchTerm && (
              <button
                onClick={() => {
                  setEditingItem(null)
                  setModalType('customer')
                  setShowAddModal(true)
                }}
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 cursor-pointer"
              >
                <FiPlus className="w-4 h-4 mr-2" />
                Add First Customer
              </button>
            )}
          </div>
        )}
      </div>

      {/* Customer Modal */}
      <CustomerModal
        isOpen={showAddModal && modalType === 'customer'}
        onClose={() => {
          setShowAddModal(false)
          setEditingItem(null)
          setModalType('')
        }}
        onSave={handleSaveCustomer}
        customer={editingItem}
        isEdit={!!editingItem}
        branding={branding}
      />

      {/* Customer Detail Modal */}
      <CustomerDetailModal
        customer={detailItem}
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        onEdit={handleEditFromDetail}
        onDelete={handleDeleteFromDetail}
      />
    </div>
  )
}

export default AdminCustomers
