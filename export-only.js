const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');

// Source database connection
const SOURCE_URI = 'mongodb+srv://inspiredolajosh:<EMAIL>/MicrolocsHq?retryWrites=true&w=majority';

// Export directory
const EXPORT_DIR = './database-export';

async function exportDatabase() {
  let sourceClient;
  
  try {
    console.log('🚀 Starting Database Export');
    console.log('===========================');
    
    // Create export directory
    if (!fs.existsSync(EXPORT_DIR)) {
      fs.mkdirSync(EXPORT_DIR, { recursive: true });
      console.log(`📁 Created export directory: ${EXPORT_DIR}`);
    }
    
    // Connect to source database
    console.log('\n🔗 Connecting to source database...');
    sourceClient = new MongoClient(SOURCE_URI, {
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
      socketTimeoutMS: 10000,
      maxPoolSize: 1
    });
    
    await sourceClient.connect();
    console.log('✅ Connected to source database');
    
    const sourceDb = sourceClient.db('MicrolocsHq');
    
    // Get all collections
    const collections = await sourceDb.listCollections().toArray();
    console.log(`\n📋 Found ${collections.length} collections`);
    
    let totalDocuments = 0;
    let exportedCollections = 0;
    const exportSummary = [];
    
    // Export each collection
    for (const collectionInfo of collections) {
      const collectionName = collectionInfo.name;
      
      try {
        console.log(`\n📄 Exporting: ${collectionName}`);
        
        const collection = sourceDb.collection(collectionName);
        const count = await collection.countDocuments();
        
        console.log(`   📊 Documents: ${count}`);
        
        if (count === 0) {
          console.log(`   ⚠️  Empty collection, creating empty file...`);
          const filePath = path.join(EXPORT_DIR, `${collectionName}.json`);
          fs.writeFileSync(filePath, '[]');
          
          exportSummary.push({
            collection: collectionName,
            documents: 0,
            status: 'empty',
            file: `${collectionName}.json`
          });
          continue;
        }
        
        // Get all documents
        const documents = await collection.find({}).toArray();
        
        // Save to JSON file
        const filePath = path.join(EXPORT_DIR, `${collectionName}.json`);
        fs.writeFileSync(filePath, JSON.stringify(documents, null, 2));
        
        console.log(`   ✅ Exported ${documents.length} documents to ${collectionName}.json`);
        totalDocuments += documents.length;
        exportedCollections++;
        
        exportSummary.push({
          collection: collectionName,
          documents: documents.length,
          status: 'success',
          file: `${collectionName}.json`
        });
        
      } catch (error) {
        console.log(`   ❌ Failed to export ${collectionName}: ${error.message}`);
        exportSummary.push({
          collection: collectionName,
          documents: 0,
          status: 'failed',
          error: error.message
        });
      }
    }
    
    // Create import script
    const importScript = `const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');

// Target database connection - UPDATE THIS WITH YOUR TARGET DATABASE URL
const TARGET_URI = 'mongodb+srv://dammyspicy4:<EMAIL>/MicrolocsHq?retryWrites=true&w=majority';

async function importDatabase() {
  let targetClient;
  
  try {
    console.log('🚀 Starting Database Import');
    console.log('===========================');
    
    targetClient = new MongoClient(TARGET_URI);
    await targetClient.connect();
    console.log('✅ Connected to target database');
    
    const targetDb = targetClient.db('MicrolocsHq');
    
    // Import each collection
    const files = fs.readdirSync('./').filter(f => f.endsWith('.json') && f !== 'import-report.json');
    
    for (const file of files) {
      const collectionName = file.replace('.json', '');
      console.log(\`\\n📄 Importing: \${collectionName}\`);
      
      const data = JSON.parse(fs.readFileSync(file, 'utf8'));
      console.log(\`   📊 Documents: \${data.length}\`);
      
      if (data.length > 0) {
        const collection = targetDb.collection(collectionName);
        await collection.deleteMany({}); // Clear existing data
        await collection.insertMany(data);
        console.log(\`   ✅ Imported \${data.length} documents\`);
      }
    }
    
    // Create indexes
    console.log('\\n🔧 Creating indexes...');
    await targetDb.collection('users').createIndex({ email: 1 }, { unique: true });
    await targetDb.collection('appointments').createIndex({ user: 1 });
    await targetDb.collection('appointments').createIndex({ service: 1 });
    console.log('✅ Indexes created');
    
    console.log('\\n🎉 Import completed successfully!');
    
  } catch (error) {
    console.error('❌ Import failed:', error.message);
  } finally {
    if (targetClient) await targetClient.close();
  }
}

importDatabase().catch(console.error);`;
    
    // Save import script
    const importScriptPath = path.join(EXPORT_DIR, 'import-to-new-database.js');
    fs.writeFileSync(importScriptPath, importScript);
    
    // Save export summary
    const summaryPath = path.join(EXPORT_DIR, 'export-summary.json');
    fs.writeFileSync(summaryPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      totalCollections: collections.length,
      exportedCollections: exportedCollections,
      totalDocuments: totalDocuments,
      collections: exportSummary
    }, null, 2));
    
    console.log('\n🎉 Export Summary:');
    console.log('==================');
    console.log(`✅ Collections exported: ${exportedCollections}`);
    console.log(`📄 Total documents: ${totalDocuments}`);
    console.log(`📁 Export location: ${path.resolve(EXPORT_DIR)}`);
    
    console.log('\n📋 Exported collections:');
    exportSummary.forEach(item => {
      const status = item.status === 'success' ? '✅' : 
                    item.status === 'empty' ? '⚠️' : '❌';
      console.log(`   ${status} ${item.collection}: ${item.documents} documents`);
    });
    
    console.log('\n📝 Next Steps:');
    console.log('==============');
    console.log('1. All your data has been exported to JSON files');
    console.log('2. Check the target MongoDB Atlas cluster is accessible');
    console.log('3. Run the import script when ready:');
    console.log(`   cd ${EXPORT_DIR}`);
    console.log('   node import-to-new-database.js');
    
    console.log('\n✅ Export completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Export failed:', error.message);
    process.exit(1);
  } finally {
    if (sourceClient) {
      await sourceClient.close();
      console.log('🔌 Disconnected from source database');
    }
  }
}

// Run export
exportDatabase().catch(console.error);
