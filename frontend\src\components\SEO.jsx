import { useEffect } from 'react'

const SEO = ({
  title = '',
  description = '',
  keywords = '',
  image = '',
  url = window.location.href,
  type = 'website'
}) => {
  useEffect(() => {
    // Update document title
    document.title = title

    // Update or create meta tags
    const updateMetaTag = (name, content, property = false) => {
      const attribute = property ? 'property' : 'name'
      let element = document.querySelector(`meta[${attribute}="${name}"]`)
      
      if (element) {
        element.setAttribute('content', content)
      } else {
        element = document.createElement('meta')
        element.setAttribute(attribute, name)
        element.setAttribute('content', content)
        document.head.appendChild(element)
      }
    }

    // Basic meta tags
    updateMetaTag('description', description)
    updateMetaTag('keywords', keywords)
    updateMetaTag('author', 'Goldie Locs By Tina')
    updateMetaTag('robots', 'index, follow')
    updateMetaTag('viewport', 'width=device-width, initial-scale=1.0')

    // Open Graph tags
    updateMetaTag('og:title', title, true)
    updateMetaTag('og:description', description, true)
    updateMetaTag('og:image', image, true)
    updateMetaTag('og:url', url, true)
    updateMetaTag('og:type', type, true)
    updateMetaTag('og:site_name', 'Goldie Locs By Tina', true)

    // Twitter Card tags
    updateMetaTag('twitter:card', 'summary_large_image')
    updateMetaTag('twitter:title', title)
    updateMetaTag('twitter:description', description)
    updateMetaTag('twitter:image', image)

    // Business-specific meta tags
    updateMetaTag('geo.region', 'US-GA')
    updateMetaTag('geo.placename', 'Atlanta')
    updateMetaTag('geo.position', '33.7490;-84.3880')
    updateMetaTag('ICBM', '33.7490, -84.3880')

    // Theme color for mobile browsers - will be empty if no branding data
    updateMetaTag('theme-color', '')
    updateMetaTag('msapplication-TileColor', '')

    // Canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]')
    if (canonicalLink) {
      canonicalLink.setAttribute('href', url)
    } else {
      canonicalLink = document.createElement('link')
      canonicalLink.setAttribute('rel', 'canonical')
      canonicalLink.setAttribute('href', url)
      document.head.appendChild(canonicalLink)
    }

    // JSON-LD structured data for local business - minimal structure when no data available
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": title.split(' - ')[1] || title || '',
      "description": description || '',
      "url": url || '',
      "image": image || ''
    }

    // Only add additional fields if they have values
    if (title.includes('Hair') || title.includes('Salon')) {
      structuredData["@type"] = "HairSalon"
    }

    // Add or update structured data
    let structuredDataScript = document.querySelector('script[type="application/ld+json"]')
    if (structuredDataScript) {
      structuredDataScript.textContent = JSON.stringify(structuredData)
    } else {
      structuredDataScript = document.createElement('script')
      structuredDataScript.type = 'application/ld+json'
      structuredDataScript.textContent = JSON.stringify(structuredData)
      document.head.appendChild(structuredDataScript)
    }

  }, [title, description, keywords, image, url, type])

  return null // This component doesn't render anything
}

export default SEO
