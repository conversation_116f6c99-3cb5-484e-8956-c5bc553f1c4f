import { isMobileDevice, isTabletDevice, isDesktopDevice, hasCameraSupport } from '../utils/constants'

const DeviceTestComponent = () => {
  const mobile = isMobileDevice()
  const tablet = isTabletDevice()
  const desktop = isDesktopDevice()
  const camera = hasCameraSupport()

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="font-bold mb-2">Device Detection Test</h3>
      <div className="space-y-1 text-sm">
        <p>Mobile: {mobile ? '✅ Yes' : '❌ No'}</p>
        <p>Tablet: {tablet ? '✅ Yes' : '❌ No'}</p>
        <p>Desktop: {desktop ? '✅ Yes' : '❌ No'}</p>
        <p>Camera Support: {camera ? '✅ Yes' : '❌ No'}</p>
        <p>User Agent: {navigator.userAgent.substring(0, 50)}...</p>
        <p>Screen: {window.innerWidth}x{window.innerHeight}</p>
        <p>Touch Points: {navigator.maxTouchPoints}</p>
      </div>
    </div>
  )
}

export default DeviceTestComponent
