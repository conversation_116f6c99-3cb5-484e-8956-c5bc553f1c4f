import { createContext, useContext, useState, useEffect } from 'react'
import { useToast } from './ToastContext'

const FavoritesContext = createContext()

export const useFavorites = () => {
  const context = useContext(FavoritesContext)
  if (!context) {
    throw new Error('useFavorites must be used within a FavoritesProvider')
  }
  return context
}

export const FavoritesProvider = ({ children }) => {
  const [favorites, setFavorites] = useState([])
  const { showSuccess, showError } = useToast()

  // Load favorites from localStorage on mount
  useEffect(() => {
    const savedFavorites = localStorage.getItem('favorites')
    if (savedFavorites) {
      try {
        setFavorites(JSON.parse(savedFavorites))
      } catch (error) {
        console.error('Error loading favorites from localStorage:', error)
      }
    }
  }, [])

  // Save favorites to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('favorites', JSON.stringify(favorites))
  }, [favorites])

  const addToFavorites = (product) => {
    setFavorites(prevFavorites => {
      const isAlreadyFavorite = prevFavorites.some(item => item.id === product.id)
      
      if (isAlreadyFavorite) {
        showError(`${product.name} is already in your favorites`)
        return prevFavorites
      }
      
      const newFavorite = {
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image,
        category: product.category,
        description: product.description,
        addedAt: new Date().toISOString()
      }
      
      showSuccess(`${product.name} added to favorites`)
      return [...prevFavorites, newFavorite]
    })
  }

  const removeFromFavorites = (productId) => {
    setFavorites(prevFavorites => {
      const item = prevFavorites.find(item => item.id === productId)
      if (item) {
        showSuccess(`${item.name} removed from favorites`)
      }
      return prevFavorites.filter(item => item.id !== productId)
    })
  }

  const isFavorite = (productId) => {
    return favorites.some(item => item.id === productId)
  }

  const toggleFavorite = (product) => {
    if (isFavorite(product.id)) {
      removeFromFavorites(product.id)
    } else {
      addToFavorites(product)
    }
  }

  const clearFavorites = () => {
    setFavorites([])
    showSuccess('All favorites cleared')
  }

  const getFavoritesCount = () => {
    return favorites.length
  }

  const value = {
    favorites,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    toggleFavorite,
    clearFavorites,
    getFavoritesCount
  }

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  )
}
