import { useEffect, useRef } from 'react';

interface ChartData {
  _id: string;
  count: number;
  confirmed: number;
  completed: number;
  cancelled: number;
}

interface AnalyticsChartProps {
  data: ChartData[];
  type: 'line' | 'bar';
  title: string;
}

export default function AnalyticsChart({ data, type, title }: AnalyticsChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current || !data.length) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Chart dimensions
    const padding = 60;
    const chartWidth = rect.width - padding * 2;
    const chartHeight = rect.height - padding * 2;

    // Clear canvas
    ctx.clearRect(0, 0, rect.width, rect.height);

    // Find max value for scaling
    const maxValue = Math.max(...data.map(d => d.count));
    const yScale = chartHeight / (maxValue * 1.1); // Add 10% padding

    // Draw grid lines
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 1;

    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = padding + (chartHeight / 5) * i;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(padding + chartWidth, y);
      ctx.stroke();

      // Y-axis labels
      const value = Math.round((maxValue * 1.1) * (1 - i / 5));
      ctx.fillStyle = '#6b7280';
      ctx.font = '12px sans-serif';
      ctx.textAlign = 'right';
      ctx.fillText(value.toString(), padding - 10, y + 4);
    }

    // Vertical grid lines and X-axis labels
    const xStep = chartWidth / (data.length - 1);
    data.forEach((item, index) => {
      const x = padding + xStep * index;
      
      // Grid line
      ctx.strokeStyle = '#e5e7eb';
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, padding + chartHeight);
      ctx.stroke();

      // X-axis label
      ctx.fillStyle = '#6b7280';
      ctx.font = '10px sans-serif';
      ctx.textAlign = 'center';
      const date = new Date(item._id);
      const label = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      ctx.fillText(label, x, padding + chartHeight + 20);
    });

    if (type === 'line') {
      // Draw line chart
      ctx.strokeStyle = '#3b82f6';
      ctx.lineWidth = 3;
      ctx.beginPath();

      data.forEach((item, index) => {
        const x = padding + xStep * index;
        const y = padding + chartHeight - (item.count * yScale);

        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();

      // Draw data points
      ctx.fillStyle = '#3b82f6';
      data.forEach((item, index) => {
        const x = padding + xStep * index;
        const y = padding + chartHeight - (item.count * yScale);

        ctx.beginPath();
        ctx.arc(x, y, 4, 0, 2 * Math.PI);
        ctx.fill();

        // Add hover effect (simplified)
        ctx.fillStyle = '#1f2937';
        ctx.font = '11px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(item.count.toString(), x, y - 10);
        ctx.fillStyle = '#3b82f6';
      });

    } else if (type === 'bar') {
      // Draw bar chart
      const barWidth = (chartWidth / data.length) * 0.8;
      const barSpacing = (chartWidth / data.length) * 0.2;

      data.forEach((item, index) => {
        const x = padding + (chartWidth / data.length) * index + barSpacing / 2;
        const barHeight = item.count * yScale;
        const y = padding + chartHeight - barHeight;

        // Draw bar
        ctx.fillStyle = '#3b82f6';
        ctx.fillRect(x, y, barWidth, barHeight);

        // Draw value on top of bar
        ctx.fillStyle = '#1f2937';
        ctx.font = '11px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(item.count.toString(), x + barWidth / 2, y - 5);
      });
    }

    // Draw axes
    ctx.strokeStyle = '#374151';
    ctx.lineWidth = 2;

    // Y-axis
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, padding + chartHeight);
    ctx.stroke();

    // X-axis
    ctx.beginPath();
    ctx.moveTo(padding, padding + chartHeight);
    ctx.lineTo(padding + chartWidth, padding + chartHeight);
    ctx.stroke();

    // Chart title
    ctx.fillStyle = '#1f2937';
    ctx.font = 'bold 16px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(title, rect.width / 2, 30);

  }, [data, type, title]);

  if (!data.length) {
    return (
      <div className="chart-empty">
        <p>No data available for the selected period</p>
      </div>
    );
  }

  return (
    <div className="analytics-chart">
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '300px',
          border: '1px solid #e5e7eb',
          borderRadius: '8px'
        }}
      />
      
      {/* Legend */}
      <div className="chart-legend">
        <div className="legend-item">
          <div className="legend-color" style={{ backgroundColor: '#3b82f6' }}></div>
          <span>Total Appointments</span>
        </div>
      </div>
    </div>
  );
}
