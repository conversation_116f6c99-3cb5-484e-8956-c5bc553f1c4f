// Customer API utilities and interfaces

import { API_CONFIG } from './config';

const API_BASE_URL = API_CONFIG.BASE_URL;

// Customer interfaces
export interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  totalAppointments: number;
  totalSpent: number;
  lastAppointment?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface CustomerDetails extends Customer {
  isVerified: boolean;
  notificationPreferences: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  statistics: {
    totalAppointments: number;
    totalSpent: number;
    lastAppointment?: string;
  };
  recentAppointments: Array<{
    id: string;
    service: {
      id: string;
      name: string;
      price: number;
    } | null;
    date: string;
    time: string;
    status: string;
    totalPrice: number;
  }>;
}

export interface CustomerListResponse {
  success: boolean;
  data: {
    customers: Customer[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface CustomerFilters {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Helper function for API requests
async function apiRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
  const token = localStorage.getItem('authToken') || localStorage.getItem('token');
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
  
  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: 'Network error' }));
    throw new Error(error.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// Customer API functions
export const customerAPI = {
  // Get all customers with filtering and pagination
  getCustomers: async (filters: CustomerFilters = {}): Promise<CustomerListResponse> => {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, value.toString());
      }
    });

    return await apiRequest(`/admin/customers?${params.toString()}`);
  },

  // Get single customer details
  getCustomer: async (id: string): Promise<{ success: boolean; data: CustomerDetails }> => {
    return await apiRequest(`/admin/customers/${id}`);
  },

  // Update customer
  updateCustomer: async (id: string, updateData: {
    firstName?: string;
    lastName?: string;
    phone?: string;
    notificationPreferences?: {
      email?: boolean;
      sms?: boolean;
      push?: boolean;
    };
  }): Promise<{ success: boolean; data: CustomerDetails }> => {
    return await apiRequest(`/admin/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  },
};
