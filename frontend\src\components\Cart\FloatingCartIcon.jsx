import { FiShoppingCart } from 'react-icons/fi'
import { useCart } from '../../contexts/CartContext'
import { useBranding } from '../../contexts/BrandingContext'

const FloatingCartIcon = () => {
  const { getCartItemCount, toggleCart } = useCart()
  const { branding } = useBranding()
  const itemCount = getCartItemCount()

  if (itemCount === 0) return null

  return (
    <button
      onClick={toggleCart}
      className="fixed bottom-6 right-6 z-50 flex items-center justify-center w-14 h-14 rounded-full shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 cursor-pointer"
      style={{ backgroundColor: branding.colors.secondary }}
      onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
      onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
    >
      <FiShoppingCart className="w-6 h-6 text-white" />
      {itemCount > 0 && (
        <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
          {itemCount > 99 ? '99+' : itemCount}
        </span>
      )}
    </button>
  )
}

export default FloatingCartIcon
