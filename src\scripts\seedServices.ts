import mongoose from 'mongoose';
import { Service } from '../models';
import { config } from '../config';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Hardcoded services data from frontend
const servicesData = [
  // 4-5 weeks retie services
  {
    name: '4-5 week Retightening - Extra Small',
    price: 300,
    duration: 120,
    category: '4-5 weeks',
    description: '4-5 week maintenance for extra small locs',
    isActive: true,
    type: 'retightening'
  },
  {
    name: '4-5 week Retightening - Small',
    price: 200,
    duration: 150,
    category: '4-5 weeks',
    description: '4-5 week maintenance for small locs',
    isActive: true,
    type: 'retightening'
  },
  {
    name: '4-5 week Retightening - Medium',
    price: 150,
    duration: 180,
    category: '4-5 weeks',
    description: '4-5 week maintenance for medium locs',
    isActive: true,
    type: 'retightening'
  },
  // 6-7 weeks retie services
  {
    name: '6-7 week Retightening - Extra Small',
    price: 350,
    duration: 120,
    category: '6-7 weeks',
    description: '6-7 week maintenance for extra small locs',
    isActive: true,
    type: 'retightening'
  },
  {
    name: '6-7 week Retightening - Small',
    price: 250,
    duration: 150,
    category: '6-7 weeks',
    description: '6-7 week maintenance for small locs',
    isActive: true,
    type: 'retightening'
  },
  {
    name: '6-7 week Retightening - Medium',
    price: 200,
    duration: 180,
    category: '6-7 weeks',
    description: '6-7 week maintenance for medium locs',
    isActive: true,
    type: 'retightening'
  },
  // 8+ weeks retie services
  {
    name: '8+ week Retightening - Extra Small',
    price: 400,
    duration: 120,
    category: '8+ weeks',
    description: '8+ week maintenance for extra small locs',
    isActive: true,
    type: 'retightening'
  },
  {
    name: '8+ week Retightening - Small',
    price: 300,
    duration: 150,
    category: '8+ weeks',
    description: '8+ week maintenance for small locs',
    isActive: true,
    type: 'retightening'
  },
  {
    name: '8+ week Retightening - Medium',
    price: 250,
    duration: 180,
    category: '8+ weeks',
    description: '8+ week maintenance for medium locs',
    isActive: true,
    type: 'retightening'
  },
  // Other services
  {
    name: 'Spa Hair Wash',
    price: 35,
    duration: 60,
    category: 'Other Services',
    description: 'PLEASE NO EXTRA GUEST 🙃',
    isActive: true,
    type: 'service'
  },
  {
    name: 'Consultation',
    price: 30,
    duration: 30,
    category: 'Consultation',
    description: 'Required prior to booking a Sisterlocks or Microlocks Establishment. At the consultation, client hair history will be discussed. The Sisterlocks hair maintenance system and care will be discussed, including do\'s and don\'ts.',
    isActive: true,
    type: 'consultation'
  },
  {
    name: 'Lock Repairs',
    price: 50,
    duration: 120,
    category: 'Other Services',
    description: 'Professional lock repair service. Minimal 2hr Session. PLEASE NO EXTRA GUEST 🙃',
    isActive: true,
    type: 'service'
  }
];

async function connectDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI || config.MONGODB_URI;
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function cleanServices() {
  try {
    console.log('🧹 Cleaning existing services...');
    const deleteResult = await Service.deleteMany({});
    console.log(`✅ Deleted ${deleteResult.deletedCount} existing services`);
  } catch (error) {
    console.error('❌ Error cleaning services:', error);
    throw error;
  }
}

async function seedServices() {
  try {
    console.log('🌱 Seeding new services...');
    const createdServices = await Service.insertMany(servicesData);
    console.log(`✅ Created ${createdServices.length} new services`);
    
    // Display created services with their IDs
    console.log('\n📋 Created Services:');
    createdServices.forEach(service => {
      console.log(`  - ${service.name} (ID: ${service._id})`);
    });
    
    return createdServices;
  } catch (error) {
    console.error('❌ Error seeding services:', error);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting service database seeding...\n');
    
    // Connect to database
    await connectDatabase();
    
    // Clean existing services
    await cleanServices();
    
    // Seed new services
    const services = await seedServices();
    
    console.log('\n✅ Service seeding completed successfully!');
    console.log(`📊 Total services created: ${services.length}`);
    
  } catch (error) {
    console.error('\n❌ Service seeding failed:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  main();
}

export { main as seedServices };
