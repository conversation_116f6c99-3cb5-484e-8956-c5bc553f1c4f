const mongoose = require('mongoose');
require('dotenv').config();

// Define the Branding schema (matching the backend model)
const brandingSchema = new mongoose.Schema({
  global: {
    siteName: { type: String, default: '' },
    tagline: { type: String, default: '' },
    logo: { type: String, default: '' },
    favicon: { type: String, default: '' },
    phone: { type: String, default: '' },
    email: { type: String, default: '' },
    address: { type: String, default: '' },
    instagram: { type: String, default: '' },
    facebook: { type: String, default: '' },
    twitter: { type: String, default: '' },
    youtube: { type: String, default: '' }
  },
  business: {
    address: {
      street: { type: String, default: '' },
      city: { type: String, default: '' },
      state: { type: String, default: '' },
      zip: { type: String, default: '' },
      full: { type: String, default: '' }
    },
    social: {
      instagram: { type: String, default: '' },
      facebook: { type: String, default: '' },
      twitter: { type: String, default: '' }
    },
    hours: {
      monday: { type: String, default: '' },
      tuesday: { type: String, default: '' },
      wednesday: { type: String, default: '' },
      thursday: { type: String, default: '' },
      friday: { type: String, default: '' },
      saturday: { type: String, default: '' },
      sunday: { type: String, default: '' }
    },
    name: { type: String, default: '' },
    tagline: { type: String, default: '' },
    description: { type: String, default: '' },
    phone: { type: String, default: '' },
    email: { type: String, default: '' }
  }
}, {
  timestamps: true
});

const Branding = mongoose.model('Branding', brandingSchema);

async function syncBrandingFields() {
  try {
    // Connect to MongoDB
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/microlocsbackend');
    console.log('✅ Connected to MongoDB');

    // Get the current branding document
    const branding = await Branding.findOne();
    
    if (!branding) {
      console.log('❌ No branding document found');
      return;
    }

    console.log('📊 Current data:');
    console.log(`   Global phone: ${branding.global?.phone || 'N/A'}`);
    console.log(`   Business phone: ${branding.business?.phone || 'N/A'}`);
    console.log(`   Global email: ${branding.global?.email || 'N/A'}`);
    console.log(`   Business email: ${branding.business?.email || 'N/A'}`);

    // Synchronize fields - prioritize global fields as the source of truth
    let updated = false;

    if (branding.global) {
      // Ensure business object exists
      if (!branding.business) {
        branding.business = {
          name: '',
          tagline: '',
          description: '',
          phone: '',
          email: '',
          address: {
            street: '',
            city: '',
            state: '',
            zip: '',
            full: ''
          },
          social: {
            instagram: '',
            facebook: '',
            twitter: ''
          },
          hours: {
            monday: '',
            tuesday: '',
            wednesday: '',
            thursday: '',
            friday: '',
            saturday: '',
            sunday: ''
          }
        };
      }

      // Sync phone
      if (branding.global.phone && branding.global.phone !== branding.business.phone) {
        console.log(`📞 Syncing phone: ${branding.business.phone} → ${branding.global.phone}`);
        branding.business.phone = branding.global.phone;
        updated = true;
      }

      // Sync email
      if (branding.global.email && branding.global.email !== branding.business.email) {
        console.log(`📧 Syncing email: ${branding.business.email} → ${branding.global.email}`);
        branding.business.email = branding.global.email;
        updated = true;
      }

      // Sync site name
      if (branding.global.siteName && branding.global.siteName !== branding.business.name) {
        console.log(`🏢 Syncing name: ${branding.business.name} → ${branding.global.siteName}`);
        branding.business.name = branding.global.siteName;
        updated = true;
      }

      // Sync tagline
      if (branding.global.tagline && branding.global.tagline !== branding.business.tagline) {
        console.log(`🏷️ Syncing tagline: ${branding.business.tagline} → ${branding.global.tagline}`);
        branding.business.tagline = branding.global.tagline;
        updated = true;
      }

      // Sync address
      if (branding.global.address) {
        if (!branding.business.address) {
          branding.business.address = {
            street: '',
            city: '',
            state: '',
            zip: '',
            full: ''
          };
        }
        if (branding.global.address !== branding.business.address.full) {
          console.log(`🏠 Syncing address: ${branding.business.address.full} → ${branding.global.address}`);
          branding.business.address.full = branding.global.address;
          updated = true;
        }
      }

      // Sync social media
      if (!branding.business.social) {
        branding.business.social = {
          instagram: '',
          facebook: '',
          twitter: ''
        };
      }

      if (branding.global.instagram && branding.global.instagram !== branding.business.social.instagram) {
        console.log(`📱 Syncing Instagram: ${branding.business.social.instagram} → ${branding.global.instagram}`);
        branding.business.social.instagram = branding.global.instagram;
        updated = true;
      }

      if (branding.global.facebook && branding.global.facebook !== branding.business.social.facebook) {
        console.log(`📘 Syncing Facebook: ${branding.business.social.facebook} → ${branding.global.facebook}`);
        branding.business.social.facebook = branding.global.facebook;
        updated = true;
      }

      if (branding.global.twitter && branding.global.twitter !== branding.business.social.twitter) {
        console.log(`🐦 Syncing Twitter: ${branding.business.social.twitter} → ${branding.global.twitter}`);
        branding.business.social.twitter = branding.global.twitter;
        updated = true;
      }
    }

    if (updated) {
      await branding.save();
      console.log('✅ Branding fields synchronized successfully!');
      
      // Verify the sync
      console.log('📊 Updated data:');
      console.log(`   Global phone: ${branding.global?.phone || 'N/A'}`);
      console.log(`   Business phone: ${branding.business?.phone || 'N/A'}`);
      console.log(`   Global email: ${branding.global?.email || 'N/A'}`);
      console.log(`   Business email: ${branding.business?.email || 'N/A'}`);
    } else {
      console.log('ℹ️ No synchronization needed - fields are already in sync');
    }

  } catch (error) {
    console.error('❌ Error synchronizing branding fields:', error);
  } finally {
    // Close the database connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  syncBrandingFields();
}

module.exports = { syncBrandingFields };
