import { createContext, useContext, useState, useEffect } from 'react'
import { useToast } from './ToastContext'
import { cartService } from '../services'

const CartContext = createContext()

export const useCart = () => {
  const context = useContext(CartContext)
  if (!context) {
    throw new Error('useCart must be used within a CartProvider')
  }
  return context
}

export const CartProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState([])
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { showSuccess, showError } = useToast()

  // Load cart from localStorage on mount (guest cart) and check for user cart
  useEffect(() => {
    const initializeCart = async () => {
      // First, try to load user cart if there's a token
      const token = localStorage.getItem('token')
      if (token) {
        try {
          await loadUserCart()
          return // If user cart loaded successfully, don't load guest cart
        } catch (error) {
          console.error('Error loading user cart:', error)
          // Fall back to guest cart if user cart fails
        }
      }

      // Load guest cart from localStorage
      const savedCart = localStorage.getItem('cart')
      if (savedCart) {
        try {
          setCartItems(JSON.parse(savedCart))
        } catch (error) {
          console.error('Error loading cart from localStorage:', error)
        }
      }
    }

    initializeCart()
  }, [])

  // Load cart from backend if user is logged in
  const loadUserCart = async () => {
    try {
      setIsLoading(true)
      const response = await cartService.getCart()
      if (response.success) {
        if (response.data.isGuest) {
          // User is not logged in, keep using localStorage cart
          return
        }
        // User is logged in, use backend cart
        setCartItems(response.data.items || [])
      }
    } catch (error) {
      console.error('Error loading user cart:', error)
      // Continue with localStorage cart on error
    } finally {
      setIsLoading(false)
    }
  }

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(cartItems))
  }, [cartItems])

  const addToCart = async (product, quantity = 1) => {
    try {
      setIsLoading(true)

      // Try to add to backend cart first
      const cartItem = {
        productId: product.id || product._id,
        quantity: quantity
      }
      const response = await cartService.addToCart(cartItem)

      if (response.success) {
        if (response.data.isGuest) {
          // User is not logged in, handle as guest cart
          setCartItems(prevItems => {
            const existingItem = prevItems.find(item =>
              (item.id || item._id) === (product.id || product._id)
            )

            if (existingItem) {
              // Update quantity if item already exists
              const updatedItems = prevItems.map(item =>
                (item.id || item._id) === (product.id || product._id)
                  ? { ...item, quantity: item.quantity + quantity }
                  : item
              )
              showSuccess(`Updated ${product.name} quantity in cart`)
              return updatedItems
            } else {
              // Add new item to cart
              const newItem = {
                id: product.id || product._id,
                _id: product.id || product._id,
                name: product.name,
                price: product.price,
                image: product.image || product.images?.[0],
                category: product.category,
                quantity: quantity
              }
              showSuccess(`${product.name} added to cart`)
              return [...prevItems, newItem]
            }
          })
        } else {
          // User is logged in, reload cart from backend
          await loadUserCart()
          showSuccess(`${product.name} added to cart`)
        }
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      showError(`Failed to add ${product.name} to cart`)
    } finally {
      setIsLoading(false)
    }
  }

  const removeFromCart = (productId) => {
    setCartItems(prevItems => {
      const item = prevItems.find(item => item.id === productId)
      if (item) {
        showSuccess(`${item.name} removed from cart`)
      }
      return prevItems.filter(item => item.id !== productId)
    })
  }

  const updateQuantity = (productId, newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(productId)
      return
    }

    setCartItems(prevItems =>
      prevItems.map(item =>
        item.id === productId
          ? { ...item, quantity: newQuantity }
          : item
      )
    )
  }

  const clearCart = () => {
    setCartItems([])
    showSuccess('Cart cleared')
  }

  const getCartTotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  const getCartItemCount = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0)
  }

  const toggleCart = () => {
    setIsCartOpen(!isCartOpen)
  }

  // Merge guest cart with user cart after login
  const mergeGuestCart = async () => {
    try {
      const guestCart = localStorage.getItem('cart')
      if (guestCart) {
        const guestItems = JSON.parse(guestCart)
        if (guestItems.length > 0) {
          const response = await cartService.mergeGuestCart({ items: guestItems })
          if (response.success) {
            localStorage.removeItem('cart')
            await loadUserCart()
            showSuccess('Guest cart merged successfully!')
          }
        }
      }
    } catch (error) {
      console.error('Error merging guest cart:', error)
      showError('Failed to merge cart items')
    }
  }

  const value = {
    cartItems,
    isCartOpen,
    isLoading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getCartTotal,
    getCartItemCount,
    toggleCart,
    setIsCartOpen,
    loadUserCart,
    mergeGuestCart
  }

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  )
}
