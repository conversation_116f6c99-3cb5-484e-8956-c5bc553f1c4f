import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

// Query keys for consistent caching
export const queryKeys = {
  // Services
  services: ['services'],
  service: (id) => ['services', id],
  
  // Products
  products: ['products'],
  product: (id) => ['products', id],
  
  // Appointments
  appointments: ['appointments'],
  appointment: (id) => ['appointments', id],
  appointmentAvailability: (params) => ['appointments', 'availability', params],
  
  // Reviews
  reviews: (params) => ['reviews', params],
  reviewStats: (params) => ['reviews', 'stats', params],
  
  // User data
  user: ['user'],
  userAppointments: ['user', 'appointments'],
  userOrders: ['user', 'orders'],
  
  // Admin data
  adminStats: ['admin', 'stats'],
  adminAppointments: (params) => ['admin', 'appointments', params],
  adminCustomers: (params) => ['admin', 'customers', params],
  adminOrders: (params) => ['admin', 'orders', params],
  adminProducts: (params) => ['admin', 'products', params],
  adminServices: (params) => ['admin', 'services', params],
  
  // Branding
  branding: ['branding'],
  
  // Cart
  cart: ['cart'],
}

// Generic query hook
export const useApiQuery = (queryKey, queryFn, options = {}) => {
  return useQuery({
    queryKey,
    queryFn,
    ...options,
  })
}

// Services queries
export const useServices = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.services,
    queryFn: async () => {
      const { serviceService } = await import('../services')
      const response = await serviceService.getServices()
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch services')
      }
      return response.data
    },
    ...options,
  })
}

export const useService = (serviceId, options = {}) => {
  return useQuery({
    queryKey: queryKeys.service(serviceId),
    queryFn: async () => {
      const { serviceService } = await import('../services')
      const response = await serviceService.getService(serviceId)
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch service')
      }
      return response.data
    },
    enabled: !!serviceId,
    ...options,
  })
}

// Products queries
export const useProducts = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.products,
    queryFn: async () => {
      const { productService } = await import('../services')
      const response = await productService.getProducts()
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch products')
      }
      return response.data
    },
    ...options,
  })
}

export const useProduct = (productId, options = {}) => {
  return useQuery({
    queryKey: queryKeys.product(productId),
    queryFn: async () => {
      const { productService } = await import('../services')
      const response = await productService.getProduct(productId)
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch product')
      }
      return response.data
    },
    enabled: !!productId,
    ...options,
  })
}

// Appointments queries
export const useAppointments = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.appointments,
    queryFn: async () => {
      const { appointmentService } = await import('../services')
      const response = await appointmentService.getAppointments()
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch appointments')
      }
      return response.data
    },
    ...options,
  })
}

export const useAppointmentAvailability = (params, options = {}) => {
  return useQuery({
    queryKey: queryKeys.appointmentAvailability(params),
    queryFn: async () => {
      const { appointmentService } = await import('../services')
      const response = await appointmentService.getAvailability(params)
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch availability')
      }
      return response.data
    },
    enabled: !!(params?.date && params?.service),
    ...options,
  })
}

// Reviews queries
export const useReviews = (params, options = {}) => {
  return useQuery({
    queryKey: queryKeys.reviews(params),
    queryFn: async () => {
      const { apiService } = await import('../services')
      const queryParams = new URLSearchParams({
        page: params.page?.toString() || '1',
        limit: params.limit?.toString() || '10'
      })
      
      if (params.productId) queryParams.append('product', params.productId)
      if (params.serviceId) queryParams.append('service', params.serviceId)
      
      const response = await apiService.get(`/reviews?${queryParams}`)
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch reviews')
      }
      return response.data
    },
    ...options,
  })
}

// User queries
export const useUserAppointments = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.userAppointments,
    queryFn: async () => {
      const { userService } = await import('../services')
      const response = await userService.getAppointments()
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch user appointments')
      }
      return response.data
    },
    ...options,
  })
}

// Admin queries
export const useAdminStats = (options = {}) => {
  return useQuery({
    queryKey: queryKeys.adminStats,
    queryFn: async () => {
      const { adminService } = await import('../services')
      const response = await adminService.getDashboardStats()
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch admin stats')
      }
      return response.data
    },
    ...options,
  })
}

export const useAdminCustomers = (params = {}, options = {}) => {
  return useQuery({
    queryKey: queryKeys.adminCustomers(params),
    queryFn: async () => {
      const { adminService } = await import('../services')
      const response = await adminService.getCustomers(params)
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch customers')
      }
      return response.data
    },
    ...options,
  })
}

export const useAdminOrders = (params = {}, options = {}) => {
  return useQuery({
    queryKey: queryKeys.adminOrders(params),
    queryFn: async () => {
      const { adminService } = await import('../services')
      const response = await adminService.getOrders(params)
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch orders')
      }
      return response.data
    },
    ...options,
  })
}

export const useAdminProducts = (params = {}, options = {}) => {
  return useQuery({
    queryKey: queryKeys.adminProducts(params),
    queryFn: async () => {
      const { adminService } = await import('../services')
      const response = await adminService.getProducts(params)
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch products')
      }
      return response.data
    },
    ...options,
  })
}

export const useAdminServices = (params = {}, options = {}) => {
  return useQuery({
    queryKey: queryKeys.adminServices(params),
    queryFn: async () => {
      const { adminService } = await import('../services')
      const response = await adminService.getServices(params)
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch services')
      }
      return response.data
    },
    ...options,
  })
}

export const useAdminAppointments = (params = {}, options = {}) => {
  return useQuery({
    queryKey: queryKeys.adminAppointments(params),
    queryFn: async () => {
      const { adminService } = await import('../services')
      const response = await adminService.getAppointments(params)
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch admin appointments')
      }
      return response.data
    },
    ...options,
  })
}

// Mutation hooks
export const useCreateAppointment = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (appointmentData) => {
      const { appointmentService } = await import('../services')
      const response = await appointmentService.createAppointment(appointmentData)
      if (!response.success) {
        throw new Error(response.message || 'Failed to create appointment')
      }
      return response.data
    },
    onSuccess: () => {
      // Invalidate and refetch appointments
      queryClient.invalidateQueries({ queryKey: queryKeys.appointments })
      queryClient.invalidateQueries({ queryKey: queryKeys.userAppointments })
      queryClient.invalidateQueries({ queryKey: queryKeys.adminAppointments() })
    },
  })
}

// Generic mutation hook
export const useApiMutation = (mutationFn, options = {}) => {
  return useMutation({
    mutationFn,
    ...options,
  })
}
