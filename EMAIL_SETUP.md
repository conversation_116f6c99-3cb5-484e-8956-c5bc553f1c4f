# Email Service Setup Guide

## Overview
The MicroLocs backend now includes a comprehensive email service with beautiful templates and automated scheduling for marketing campaigns and customer communications.

## Features
- ✅ Welcome emails for new users
- ✅ Appointment confirmation emails
- ✅ Consultation booking confirmations
- ✅ Appointment reminders (24 hours before)
- ✅ Weekly promotional campaigns
- ✅ Birthday special offers
- ✅ Quarterly customer appreciation emails
- ✅ Beautiful HTML templates with branding
- ✅ Automated scheduling with cron jobs
- ✅ Admin controls for manual triggers
- ✅ Test email functionality

## Environment Variables Setup

Add the following environment variables to your `.env` file:

```env
# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Frontend URL (for email links)
FRONTEND_URL=http://localhost:5173
```

### Gmail Setup Instructions

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a new app password for "Mail"
   - Use this password as `SMTP_PASS`

3. **Alternative Email Providers**:
   - **Outlook/Hotmail**: `smtp-mail.outlook.com`, port 587
   - **Yahoo**: `smtp.mail.yahoo.com`, port 587
   - **SendGrid**: `smtp.sendgrid.net`, port 587
   - **Mailgun**: `smtp.mailgun.org`, port 587

## Email Templates

### 1. Welcome Email
- Sent automatically when new users register
- Includes account details and temporary password (if applicable)
- Features service highlights and call-to-action

### 2. Appointment Confirmation
- Sent when appointments are booked
- Includes appointment details, location, and preparation tips
- Provides links to manage appointments

### 3. Consultation Confirmation
- Sent when consultations are scheduled
- Explains what to expect during consultation
- Includes preparation guidelines

### 4. Appointment Reminders
- Sent 24 hours before appointments
- Includes preparation checklist
- Provides easy rescheduling options

### 5. Promotional Emails
- Weekly campaigns with rotating offers
- Seasonal promotions
- Personalized discount codes

### 6. Birthday Specials
- Sent during user's birthday month
- Exclusive birthday offers
- Special birthday makeover packages

## Scheduled Email Campaigns

### Daily (9:00 AM)
- **Appointment Reminders**: Sent to users with appointments tomorrow

### Weekly (Monday 10:00 AM)
- **Promotional Campaigns**: Rotating weekly offers for recent customers

### Monthly (1st at 9:00 AM)
- **Birthday Emails**: Sent to users with birthdays this month

### Quarterly (1st at 10:00 AM)
- **Customer Appreciation**: Seasonal offers for all active customers

## Admin Email Controls

Access the email management endpoints at `/api/admin/emails/`:

### Manual Triggers
```bash
# Trigger appointment reminders
POST /api/admin/emails/trigger/appointment-reminders

# Trigger weekly promotions
POST /api/admin/emails/trigger/weekly-promotions

# Trigger birthday emails
POST /api/admin/emails/trigger/birthday-emails

# Trigger customer appreciation
POST /api/admin/emails/trigger/customer-appreciation
```

### Custom Campaigns
```bash
# Send custom promotion to specific users
POST /api/admin/emails/send/custom-promotion
{
  "userIds": ["user1", "user2"],
  "promotion": {
    "title": "Special Offer",
    "description": "Limited time deal",
    "discount": "25%",
    "validUntil": "December 31, 2024",
    "code": "SPECIAL25"
  }
}
```

### Test Emails
```bash
# Send test welcome email
POST /api/admin/emails/test/welcome
{
  "email": "<EMAIL>"
}

# Send test birthday email
POST /api/admin/emails/test/birthday
{
  "email": "<EMAIL>"
}
```

### Service Status
```bash
# Check email service status
GET /api/admin/emails/status
```

## Email Template Customization

All email templates are located in `src/services/emailService.ts` and include:

- **Responsive Design**: Works on all devices
- **Brand Colors**: Uses MicroLocs yellow (#f3d016) theme
- **Professional Layout**: Header, content, and footer sections
- **Call-to-Action Buttons**: Prominent booking and management links
- **Social Media Links**: Footer includes social media references

## Troubleshooting

### Common Issues

1. **Emails not sending**:
   - Check SMTP credentials in environment variables
   - Verify app password for Gmail
   - Check firewall/network restrictions

2. **Emails going to spam**:
   - Set up SPF, DKIM, and DMARC records
   - Use a dedicated email service (SendGrid, Mailgun)
   - Warm up your sending domain

3. **Scheduled emails not working**:
   - Ensure server stays running
   - Check server timezone settings
   - Verify cron job initialization in logs

### Logs and Monitoring

Email service logs include:
- Successful email sends
- Failed email attempts with error details
- Scheduled job execution status
- Service initialization status

Check server logs for email-related messages:
```bash
# Look for email service logs
grep -i "email" server.log
grep -i "smtp" server.log
```

## Production Recommendations

1. **Use Professional Email Service**:
   - SendGrid, Mailgun, or AWS SES
   - Better deliverability and analytics
   - Higher sending limits

2. **Set Up Email Authentication**:
   - SPF records
   - DKIM signing
   - DMARC policy

3. **Monitor Email Metrics**:
   - Delivery rates
   - Open rates
   - Click-through rates
   - Bounce rates

4. **Implement Unsubscribe**:
   - Add unsubscribe links to promotional emails
   - Maintain suppression lists
   - Respect user preferences

## Security Considerations

- Store SMTP credentials securely
- Use app passwords instead of main passwords
- Implement rate limiting for email endpoints
- Validate email addresses before sending
- Sanitize user input in email content
- Use HTTPS for all email-related links
