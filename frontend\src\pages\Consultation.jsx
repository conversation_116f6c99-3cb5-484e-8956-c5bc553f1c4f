import { useState, useEffect } from 'react'
import { FiCalendar, FiClock, FiUser, FiMail, FiPhone, FiMessageSquare, FiLock, FiX, FiAlertCircle, FiEye, FiEyeOff, FiArrowLeft } from 'react-icons/fi'
import { serviceService, consultationService, authService, paymentConfirmationService } from '../services'
import { useBranding } from '../contexts/BrandingContext'
import PhoneInput from '../components/PhoneInput'
import { useApiWithToast, TOAST_MESSAGES } from '../utils/apiWithToast'
import { PaymentConfirmationForm } from '../components/PaymentConfirmation'
import requestLock, { OPERATIONS } from '../utils/requestLock'

const Consultation = ({ user, userProfile, onNavigate }) => {
  const { branding } = useBranding()
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [currentUser, setCurrentUser] = useState(null)
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    service: '',
    date: '',
    time: '',
    message: ''
  })

  const [services, setServices] = useState([])
  const [availableSlots, setAvailableSlots] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingServices, setIsLoadingServices] = useState(true)
  const [error, setError] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [step, setStep] = useState(1) // 1: Consultation Form, 2: Payment Confirmation
  const [appointmentData, setAppointmentData] = useState(null)

  // Toast notifications
  const { executeWithToast, showSuccess, showError } = useApiWithToast()

  // Check if user is logged in and load their data
  useEffect(() => {
    const checkUserAuth = async () => {
      try {
        // Check if user is passed from App.jsx or if authenticated via authService
        if (user && userProfile) {
          // User data is available from App.jsx
          setIsLoggedIn(true)
          setCurrentUser(userProfile)

          // Pre-populate form with user data
          setFormData(prevData => ({
            ...prevData,
            firstName: userProfile.firstName || '',
            lastName: userProfile.lastName || '',
            email: userProfile.email || '',
            phone: userProfile.phone || '',
            // Don't pre-populate password for security
            password: ''
          }))
        } else if (authService.isAuthenticated()) {
          // Fallback to authService if user props are not available
          const authUser = await authService.getCurrentUser()
          if (authUser) {
            setIsLoggedIn(true)
            setCurrentUser(authUser)

            // Pre-populate form with user data
            setFormData(prevData => ({
              ...prevData,
              firstName: authUser.firstName || '',
              lastName: authUser.lastName || '',
              email: authUser.email || '',
              phone: authUser.phone || '',
              // Don't pre-populate password for security
              password: ''
            }))
          }
        } else {
          setIsLoggedIn(false)
          setCurrentUser(null)
        }
      } catch (error) {
        console.error('Error checking user authentication:', error)
        setIsLoggedIn(false)
        setCurrentUser(null)
      }
    }

    checkUserAuth()
  }, [user, userProfile])

  // Load services on component mount
  useEffect(() => {
    const loadServices = async () => {
      try {
        setIsLoadingServices(true)
        const response = await serviceService.getServices()

        if (response.success) {
          setServices(response.data)
        }
      } catch (error) {
        console.error('Error loading services:', error)
        showError(`Failed to load services: ${error.response?.data?.message || error.message || 'Unknown error'}`)
      } finally {
        setIsLoadingServices(false)
      }
    }

    loadServices()
  }, [])

  // Default available time slots (fallback when API fails)
  const getDefaultTimeSlots = () => {
    return [
      '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
      '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
      '15:00', '15:30', '16:00', '16:30', '17:00', '17:30'
    ]
  }

  // Load available time slots when date changes
  useEffect(() => {
    const loadAvailableSlots = async () => {
      if (!formData.date) {
        setAvailableSlots([])
        return
      }

      try {
        const response = await consultationService.getAvailability(formData.date, formData.service)

        if (response.success && response.data.availableSlots && response.data.availableSlots.length > 0) {
          setAvailableSlots(response.data.availableSlots)
        } else {
          // Use default slots if API returns empty or no slots
          setAvailableSlots(getDefaultTimeSlots())
        }
      } catch (error) {
        console.error('Error loading availability:', error)
        // Use default slots as fallback instead of showing error
        setAvailableSlots(getDefaultTimeSlots())
        console.warn('Using default time slots due to availability API error')
      }
    }

    loadAvailableSlots()
  }, [formData.date, formData.service])

  // Helper functions

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  // Helper function to convert 12-hour time to 24-hour format
  const convertTo24Hour = (time12h) => {
    if (!time12h) return ''

    const [time, modifier] = time12h.split(' ')
    let [hours, minutes] = time.split(':')

    if (hours === '12') {
      hours = '00'
    }

    if (modifier === 'PM') {
      hours = parseInt(hours, 10) + 12
    }

    return `${hours.toString().padStart(2, '0')}:${minutes}`
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'email', 'service', 'date', 'time']
    const missingFields = requiredFields.filter(field => !formData[field])

    if (missingFields.length > 0) {
      showError('Please fill in all required fields.')
      return
    }

    // For non-logged-in users, password is required for new accounts
    if (!isLoggedIn && !formData.password) {
      showError('Please set a password for your account.')
      return
    }

    // Validate password strength for new users
    if (!isLoggedIn && formData.password && formData.password.length < 6) {
      showError('Password must be at least 6 characters long.')
      return
    }

    // Use request lock to prevent duplicate appointment bookings
    const lockIdentifier = `${formData.email}_${formData.date}_${formData.time}`;

    try {
      await requestLock.withLock(OPERATIONS.APPOINTMENT_BOOKING, lockIdentifier, async () => {
        setIsLoading(true)

        try {
          let response

          // Convert time to 24-hour format for backend
          const time24h = convertTo24Hour(formData.time)

          if (isLoggedIn) {
            // For logged-in users, use authenticated consultation booking
            const appointmentData = {
              name: `${formData.firstName} ${formData.lastName}`.trim(),
              email: formData.email,
              phone: formData.phone || undefined,
              service: formData.service,
              date: formData.date,
              time: time24h,
              message: formData.message
            }

            // Use authenticated consultation service for logged-in users
            response = await consultationService.bookAuthenticatedConsultation(appointmentData)
          } else {
            // For non-logged-in users, use the smart consultation service
            const consultationData = {
              name: `${formData.firstName} ${formData.lastName}`.trim(),
              email: formData.email,
              phone: formData.phone || undefined,
              password: formData.password, // Include password for new user creation
              service: formData.service,
              date: formData.date,
              time: time24h,
              message: formData.message
            }

            response = await consultationService.smartBookConsultation(consultationData)
          }

          if (response.success) {
            let message = isLoggedIn
              ? 'Appointment booked successfully! Please submit payment confirmation.'
              : 'Consultation booked successfully! Please submit payment confirmation.'

            // If a new user was created and tokens were returned, they're already logged in
            if (!isLoggedIn && response.data?.isNewUser && response.data?.token) {
              message += ' An account has been created for you and you are now logged in.'

              // The consultation service already saved the tokens, just update the UI state
              try {
                // Update the app's authentication state
                const user = response.data.user;
                const userType = user?.role === 'admin' ? 'admin' : 'user';

                // Store user data for the app
                localStorage.setItem('rememberedUser', userType);

                // Update local state to reflect login
                setIsLoggedIn(true);

                // Dispatch login event for other components to update
                window.dispatchEvent(new CustomEvent('user-login', {
                  detail: {
                    user: user,
                    token: response.data.token
                  }
                }));

                console.log('User auto-logged in after consultation booking:', user);
              } catch (error) {
                console.error('Error updating login state after consultation:', error);
                // Don't show error to user as the main operation (booking) was successful
              }
            }

            showSuccess(message)

            // Store appointment data and move to payment confirmation step
            setAppointmentData(response.data)
            setStep(2)
          } else {
            showError(`Failed to book consultation: ${response.message || response.error || 'Unknown error'}`)
          }
        } catch (error) {
          console.error('Error booking consultation:', error)

          // Handle specific error messages with detailed information
          const errorMessage = error.response?.data?.message ||
                              error.response?.data?.error ||
                              error.message ||
                              'Unknown error occurred'
          showError(`Failed to book consultation: ${errorMessage}`)
          throw error; // Re-throw to ensure lock is released
        } finally {
          setIsLoading(false)
        }
      });
    } catch (error) {
      if (error.message.includes('already in progress')) {
        showError('Appointment booking is already in progress. Please wait for the current request to complete.');
      }
      // If it's a different error, it was already handled above
    }
  }

  const handlePaymentConfirmationSubmit = async (confirmationData) => {
    try {
      // Submit payment confirmation to the API
      const response = await paymentConfirmationService.submitPaymentConfirmation(confirmationData)

      if (response.success) {
        showSuccess('Payment confirmation submitted! We will verify and confirm your appointment soon.')
        onNavigate('user-dashboard')
      } else {
        throw new Error(response.message || 'Failed to submit payment confirmation')
      }
    } catch (error) {
      console.error('Payment confirmation submission error:', error)
      throw error // Let the form handle the error
    }
  }

  // Format time slots for display (convert 24h to 12h format if needed)
  const formatTimeSlot = (time) => {
    // If time is already in 12h format, return as is
    if (time.includes('AM') || time.includes('PM')) {
      return time
    }

    // Convert 24h format to 12h format
    const [hours, minutes] = time.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'PM' : 'AM'
    const displayHour = hour % 12 || 12
    return `${displayHour}:${minutes} ${ampm}`
  }

  const timeSlots = availableSlots



  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          {step === 2 && (
            <div className="flex items-center justify-center mb-6">
              <button
                onClick={() => setStep(1)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 mr-4"
              >
                <FiArrowLeft className="w-5 h-5" />
              </button>
              <div className="text-left">
                <h1 className="text-4xl font-bold text-gray-900">Payment Confirmation</h1>
                <p className="text-gray-600">Submit payment proof for your appointment</p>
              </div>
            </div>
          )}

          {step === 1 && (
            <>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                {isLoggedIn ? 'Book Your Appointment' : branding.content.consultationTitle}
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                {isLoggedIn
                  ? 'Schedule your next hair care appointment with us'
                  : branding.content.consultationSubtitle
                }
              </p>
              <p className="text-lg text-gray-500 max-w-xl mx-auto mt-4">
                {isLoggedIn
                  ? 'Choose your preferred service, date, and time. We\'ll take care of the rest!'
                  : branding.content.consultationDescription
                }
              </p>
            </>
          )}
        </div>

        {step === 1 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Consultation Info */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">What to Expect</h2>

            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <FiUser className="w-6 h-6" style={{ color: '#008000' }} />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Personal Assessment</h3>
                  <p className="text-gray-600">
                    We'll examine your hair type, texture, and current condition to recommend the best loc style for you.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                  <FiMessageSquare className="w-6 h-6" style={{ color: '#f3d016' }} />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Style Discussion</h3>
                  <p className="text-gray-600">
                    Discuss your lifestyle, maintenance preferences, and desired look to create a customized plan.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <FiCalendar className="w-6 h-6" style={{ color: '#008000' }} />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Treatment Plan</h3>
                  <p className="text-gray-600">
                    Receive a detailed timeline and care plan for your loc journey, including maintenance schedules.
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-8 p-6 bg-yellow-50 rounded-xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Consultation Details</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <FiClock className="w-4 h-4 mr-2" style={{ color: '#f3d016' }} />
                  Duration: 45-60 minutes
                </li>
                <li className="flex items-center">
                  <FiCalendar className="w-4 h-4 mr-2" style={{ color: '#f3d016' }} />
                  Price: $50 (Applied to service if booked)
                </li>
              </ul>
            </div>
          </div>

          {/* Booking Form */}
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                {isLoggedIn ? 'Appointment Details' : branding.content.consultationFormTitle}
              </h2>
              <p className="text-gray-600 mt-2">
                {isLoggedIn
                  ? 'Fill in your appointment preferences below'
                  : branding.content.consultationFormSubtitle
                }
              </p>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
                <FiAlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                    First Name *
                  </label>
                  <div className="relative">
                    <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      required
                      value={formData.firstName}
                      onChange={handleChange}
                      readOnly={isLoggedIn}
                      className={`w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200 ${
                        isLoggedIn ? 'bg-gray-50 cursor-not-allowed' : ''
                      }`}
                      style={{ focusRingColor: '#f3d016', focusBorderColor: 'transparent' }}
                      onFocus={(e) => {
                        if (!isLoggedIn) {
                          e.target.style.outline = 'none'
                          e.target.style.borderColor = '#f3d016'
                          e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                        }
                      }}
                      onBlur={(e) => {
                        if (!isLoggedIn) {
                          e.target.style.borderColor = '#d1d5db'
                          e.target.style.boxShadow = 'none'
                        }
                      }}
                      placeholder="Enter your first name"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name *
                  </label>
                  <div className="relative">
                    <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      required
                      value={formData.lastName}
                      onChange={handleChange}
                      readOnly={isLoggedIn}
                      className={`w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200 ${
                        isLoggedIn ? 'bg-gray-50 cursor-not-allowed' : ''
                      }`}
                      style={{ focusRingColor: '#f3d016', focusBorderColor: 'transparent' }}
                      onFocus={(e) => {
                        if (!isLoggedIn) {
                          e.target.style.outline = 'none'
                          e.target.style.borderColor = '#f3d016'
                          e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                        }
                      }}
                      onBlur={(e) => {
                        if (!isLoggedIn) {
                          e.target.style.borderColor = '#d1d5db'
                          e.target.style.boxShadow = 'none'
                        }
                      }}
                      placeholder="Enter your last name"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <div className="relative">
                  <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    readOnly={isLoggedIn}
                    className={`w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200 ${
                      isLoggedIn ? 'bg-gray-50 cursor-not-allowed' : ''
                    }`}
                    onFocus={(e) => {
                      if (!isLoggedIn) {
                        e.target.style.outline = 'none'
                        e.target.style.borderColor = '#f3d016'
                        e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                      }
                    }}
                    onBlur={(e) => {
                      if (!isLoggedIn) {
                        e.target.style.borderColor = '#d1d5db'
                        e.target.style.boxShadow = 'none'
                      }
                    }}
                    placeholder="Enter your email"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <PhoneInput
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={(value) => setFormData(prev => ({ ...prev, phone: value }))}
                  required
                  readOnly={isLoggedIn}
                  placeholder="Enter your phone number"
                  onFocus={(e) => {
                    if (!isLoggedIn) {
                      e.target.style.outline = 'none'
                      e.target.style.borderColor = '#f3d016'
                      e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                    }
                  }}
                  onBlur={(e) => {
                    if (!isLoggedIn) {
                      e.target.style.borderColor = '#d1d5db'
                      e.target.style.boxShadow = 'none'
                    }
                  }}
                />
              </div>

              {/* Only show password field for non-logged-in users */}
              {!isLoggedIn && (
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                    Set Your Password *
                  </label>
                  <p className="text-sm text-gray-600 mb-2">
                    Create a secure password for your account. If you already have an account, enter your existing password.
                  </p>
                  <div className="relative">
                    <FiLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type={showPassword ? 'text' : 'password'}
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      placeholder="Enter a secure password (min. 6 characters)"
                      className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                      onFocus={(e) => {
                        e.target.style.outline = 'none'
                        e.target.style.borderColor = '#f3d016'
                        e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = '#d1d5db'
                        e.target.style.boxShadow = 'none'
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                      title={showPassword ? 'Hide password' : 'Show password'}
                    >
                      {showPassword ? <FiEyeOff className="w-5 h-5" /> : <FiEye className="w-5 h-5" />}
                    </button>
                  </div>

                  {/* Password strength indicator */}
                  {formData.password && (
                    <div className="mt-2">
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              formData.password.length >= 8
                                ? 'bg-green-500 w-full'
                                : formData.password.length >= 6
                                ? 'bg-yellow-500 w-2/3'
                                : 'bg-red-500 w-1/3'
                            }`}
                          />
                        </div>
                        <span className={`text-xs font-medium ${
                          formData.password.length >= 8
                            ? 'text-green-600'
                            : formData.password.length >= 6
                            ? 'text-yellow-600'
                            : 'text-red-600'
                        }`}>
                          {formData.password.length >= 8 ? 'Strong' : formData.password.length >= 6 ? 'Good' : 'Weak'}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Password should be at least 6 characters long. 8+ characters recommended.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Show user info for logged-in users */}
              {isLoggedIn && currentUser && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <FiUser className="text-green-600 w-5 h-5 mr-2" />
                    <span className="text-green-800 font-medium">Booking as: {currentUser.firstName} {currentUser.lastName}</span>
                  </div>
                  <p className="text-green-700 text-sm">
                    Your account information has been automatically filled in. You can modify the details above if needed.
                  </p>
                </div>
              )}

              <div>
                <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                  Service Interest *
                </label>
                <select
                  id="service"
                  name="service"
                  required
                  value={formData.service}
                  onChange={handleChange}
                  disabled={isLoadingServices}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.borderColor = '#f3d016'
                    e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db'
                    e.target.style.boxShadow = 'none'
                  }}
                >
                  <option value="">
                    {isLoadingServices ? 'Loading services...' : 'Select a service'}
                  </option>
                  {services.map((service) => (
                    <option key={service._id} value={service._id}>
                      {service.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Date *
                  </label>
                  <input
                    type="date"
                    id="date"
                    name="date"
                    required
                    value={formData.date}
                    onChange={handleChange}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                    onFocus={(e) => {
                      e.target.style.outline = 'none'
                      e.target.style.borderColor = '#f3d016'
                      e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#d1d5db'
                      e.target.style.boxShadow = 'none'
                    }}
                  />
                </div>

                <div>
                  <label htmlFor="time" className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Time *
                  </label>
                  <select
                    id="time"
                    name="time"
                    required
                    value={formData.time}
                    onChange={handleChange}
                    disabled={!formData.date}
                    className={`w-full px-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200 ${
                      !formData.date ? 'bg-gray-100 cursor-not-allowed' : ''
                    }`}
                    onFocus={(e) => {
                      if (formData.date) {
                        e.target.style.outline = 'none'
                        e.target.style.borderColor = '#f3d016'
                        e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                      }
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#d1d5db'
                      e.target.style.boxShadow = 'none'
                    }}
                  >
                    <option value="">
                      {!formData.date ? 'Please select a date first' : 'Select time'}
                    </option>
                    {formData.date && timeSlots.map((time) => (
                      <option key={time} value={time}>
                        {formatTimeSlot(time)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Notes
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={4}
                  value={formData.message}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.borderColor = '#f3d016'
                    e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db'
                    e.target.style.boxShadow = 'none'
                  }}
                  placeholder="Tell us about your hair goals, any concerns, or special requests..."
                />
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-3 px-6 rounded-lg font-medium text-white transition-colors duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ backgroundColor: '#f3d016' }}
                onMouseEnter={(e) => !e.target.disabled && (e.target.style.backgroundColor = '#d4b014')}
                onMouseLeave={(e) => !e.target.disabled && (e.target.style.backgroundColor = '#f3d016')}
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Booking...
                  </>
                ) : (
                  <>
                    <FiCalendar className="w-5 h-5 mr-2" />
                    {isLoggedIn ? 'Book Appointment' : 'Book Consultation'}
                  </>
                )}
              </button>
            </form>
          </div>
        </div>
        ) : (
          /* Payment Confirmation Step */
          <PaymentConfirmationForm
            onSubmit={handlePaymentConfirmationSubmit}
            branding={branding}
            appointmentData={appointmentData}
            onCancel={() => setStep(1)}
          />
        )}
      </div>
    </div>
  )
}

export default Consultation
