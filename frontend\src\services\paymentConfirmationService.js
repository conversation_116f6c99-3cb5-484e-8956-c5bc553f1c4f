import apiService from './api.js'

/**
 * Payment confirmation service for handling payment proof submissions
 */
class PaymentConfirmationService {
  /**
   * Create a new payment confirmation
   */
  async createPaymentConfirmation(confirmationData) {
    try {
      const response = await apiService.post('/payment-confirmations', confirmationData)
      return response
    } catch (error) {
      console.error('Create payment confirmation error:', error)
      throw error
    }
  }

  /**
   * Get user's payment confirmations
   */
  async getUserPaymentConfirmations(params = {}) {
    try {
      const response = await apiService.get('/payment-confirmations/my', params)
      return response
    } catch (error) {
      console.error('Get user payment confirmations error:', error)
      throw error
    }
  }

  /**
   * Get payment confirmation by ID
   */
  async getPaymentConfirmationById(confirmationId) {
    try {
      const response = await apiService.get(`/payment-confirmations/${confirmationId}`)
      return response
    } catch (error) {
      console.error('Get payment confirmation by ID error:', error)
      throw error
    }
  }

  /**
   * Update payment confirmation
   */
  async updatePaymentConfirmation(confirmationId, updateData) {
    try {
      const response = await apiService.put(`/payment-confirmations/${confirmationId}`, updateData)
      return response
    } catch (error) {
      console.error('Update payment confirmation error:', error)
      throw error
    }
  }

  /**
   * Delete payment confirmation
   */
  async deletePaymentConfirmation(confirmationId) {
    try {
      const response = await apiService.delete(`/payment-confirmations/${confirmationId}`)
      return response
    } catch (error) {
      console.error('Delete payment confirmation error:', error)
      throw error
    }
  }

  /**
   * Upload payment proof image
   */
  async uploadPaymentProof(imageFile) {
    try {
      const formData = new FormData()
      formData.append('image', imageFile)
      
      const response = await apiService.post('/upload/payment-proof', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response
    } catch (error) {
      console.error('Upload payment proof error:', error)
      throw error
    }
  }

  /**
   * Convert image file to base64 for API submission
   */
  async convertImageToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result)
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  /**
   * Submit payment confirmation with image upload
   */
  async submitPaymentConfirmation(confirmationData) {
    try {
      // If there's an image file, convert it to base64
      if (confirmationData.proofImage && confirmationData.proofImage instanceof File) {
        const base64Image = await this.convertImageToBase64(confirmationData.proofImage)
        confirmationData.proofImage = base64Image
      }

      const response = await this.createPaymentConfirmation(confirmationData)
      return response
    } catch (error) {
      console.error('Submit payment confirmation error:', error)
      throw error
    }
  }
}

// Create and export singleton instance
const paymentConfirmationService = new PaymentConfirmationService()
export default paymentConfirmationService
