import { Component } from 'react'
import { <PERSON><PERSON>lertTriangle, FiRefreshCw, FiHome } from 'react-icons/fi'

class ErrorBoundary extends Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    })

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error caught by boundary:', error, errorInfo)
    }

    // Log error to external service in production (if available)
    if (process.env.NODE_ENV === 'production') {
      // You can integrate with error tracking services like Sentry here
      console.error('Production error:', error.message)
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null, errorInfo: null })
    // Force a page reload to reset the component state
    window.location.reload()
  }

  handleGoHome = () => {
    this.setState({ hasError: false, error: null, errorInfo: null })
    if (this.props.onNavigate) {
      this.props.onNavigate('home')
    } else {
      // Use proper path-based navigation
      window.location.href = 'http://localhost:5173/home'
    }
  }

  getErrorMessage() {
    const error = this.state.error
    if (!error) return "We're sorry for the inconvenience. The page encountered an unexpected error."

    // Provide user-friendly messages for common errors
    if (error.message.includes('ChunkLoadError') || error.message.includes('Loading chunk')) {
      return "There was an issue loading the page. This usually happens after an app update. Please refresh the page."
    }

    if (error.message.includes('Network Error') || error.message.includes('fetch')) {
      return "Unable to connect to our servers. Please check your internet connection and try again."
    }

    if (error.message.includes('Authentication') || error.message.includes('token')) {
      return "Your session has expired. Please log in again to continue."
    }

    return "We're sorry for the inconvenience. The page encountered an unexpected error."
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center px-4">
          <div className="max-w-md w-full bg-white rounded-2xl shadow-2xl p-8 text-center transform transition-all duration-300">
            <div className="w-20 h-20 bg-gradient-to-br from-red-100 to-red-200 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
              <FiAlertTriangle className="w-10 h-10 text-red-600" />
            </div>

            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Oops! Something went wrong
            </h1>

            <p className="text-gray-600 mb-8 leading-relaxed">
              {this.getErrorMessage()}
            </p>

            <div className="space-y-3">
              <button
                onClick={this.handleRetry}
                className="w-full flex items-center justify-center px-6 py-3 text-white rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                style={{
                  background: 'linear-gradient(135deg, #f3d016, #e6c200)',
                  boxShadow: '0 4px 15px rgba(243, 208, 22, 0.3)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = 'linear-gradient(135deg, #e6c200, #d4b014)'
                  e.target.style.boxShadow = '0 6px 20px rgba(243, 208, 22, 0.4)'
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = 'linear-gradient(135deg, #f3d016, #e6c200)'
                  e.target.style.boxShadow = '0 4px 15px rgba(243, 208, 22, 0.3)'
                }}
              >
                <FiRefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </button>

              <button
                onClick={this.handleGoHome}
                className="w-full flex items-center justify-center px-6 py-3 text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
              >
                <FiHome className="w-4 h-4 mr-2" />
                Go to Homepage
              </button>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500 mb-3">
                Still having issues? We're here to help!
              </p>
              <button
                onClick={() => window.location.href = 'mailto:<EMAIL>?subject=Website Error Report'}
                className="text-sm text-blue-600 hover:text-blue-700 transition-colors duration-200 underline"
              >
                Contact Support
              </button>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
