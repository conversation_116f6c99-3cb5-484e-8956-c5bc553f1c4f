import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { connectDatabase } from './config/database';
import { config } from './config';
import routes from './routes';
import v2Routes from './routes/v2';
import { errorHandler, notFound } from './middleware/errorHandler';
import { initializeServer } from './utils/initializeServer';
import { scheduledEmailService } from './services/scheduledEmailService';
import { AuthController } from './controllers';
import { authenticate } from './middleware/auth';
import { validate } from './middleware/validation';
import {
  registerValidation,
  loginValidation,
  forgotPasswordValidation
} from './utils/validation';

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Trust proxy
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration - Allow all origins
app.use(cors({
  origin: '*', // Allow all origins
  credentials: false, // Set to false when using origin: '*'
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  optionsSuccessStatus: 200
}));

// Compression middleware
app.use(compression());

// Logging middleware
if (config.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files (for uploaded files)
app.use('/uploads', express.static('uploads'));

// Handle preflight requests explicitly
app.options('*', cors());

// API routes
app.use('/api', routes);

// V2 API routes
app.use('/api/v2', v2Routes);

// ===== V2 API ROUTES (Individual routes - can be removed after testing) =====
// V2 Auth routes
app.post('/api/v2/auth/register', validate(registerValidation), AuthController.register);
app.post('/api/v2/auth/login', validate(loginValidation), AuthController.login);
app.post('/api/v2/auth/forgot-password', validate(forgotPasswordValidation), AuthController.forgotPassword);
app.post('/api/v2/auth/reset-password', AuthController.resetPassword);
app.get('/api/v2/auth/verify', authenticate, AuthController.verify);
app.post('/api/v2/auth/logout', authenticate, AuthController.logout);
app.get('/api/v2/auth/me', authenticate, (req: any, res) => {
  const user = req.user;
  if (!user) {
    return res.status(401).json({ success: false, message: 'User not authenticated' });
  }
  res.json({
    success: true,
    data: {
      id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      createdAt: user.createdAt
    }
  });
});

// V2 Health check endpoint
app.get('/api/v2/health', (_req, res) => {
  res.json({
    success: true,
    message: 'API v2 is running',
    version: '2.0.0',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    endpoints: {
      auth: '/api/v2/auth',
      services: '/api/v2/services',
      appointments: '/api/v2/appointments',
      paymentConfirmations: '/api/v2/payment-confirmations',
      user: '/api/v2/user',
      admin: '/api/v2/admin'
    }
  });
});

// Root endpoint
app.get('/', (_req, res) => {
  res.json({
    success: true,
    message: 'Welcome to MicroLocs Backend API',
    version: '1.0.0',
    documentation: '/api/health',
    endpoints: {
      auth: '/api/auth',
      appointments: '/api/appointments',
      services: '/api/services',
      products: '/api/products',
      cart: '/api/cart',
      orders: '/api/orders',
      users: '/api/users',
      notifications: '/api/notifications',
      admin: '/api/admin'
    }
  });
});

// 404 handler
app.use(notFound);

// Error handling middleware
app.use(errorHandler);

// Start server
const startServer = async (): Promise<void> => {
  try {
    // Connect to database
    await connectDatabase();

    // Initialize server (create default admin, etc.)
    await initializeServer();

    // Start listening
    const PORT = Number(config.PORT) || 3000;
    const HOST = config.HOST || '0.0.0.0';

    app.listen(PORT, HOST, () => {
      console.log(`
🚀 Server is running on ${HOST}:${PORT}
📊 Environment: ${config.NODE_ENV}
🔗 Local API URL: http://localhost:${PORT}
🌐 Public API URL: http://${HOST}:${PORT}
📚 Health Check: http://${HOST}:${PORT}/api/health
🔧 Admin Panel: http://${HOST}:${PORT}/api/admin
      `);

      // Initialize scheduled email service
      try {
        scheduledEmailService.init();
        console.log('📧 Email scheduling service initialized');
      } catch (error) {
        console.error('Failed to initialize email scheduling service:', error);
      }
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err: Error) => {
  console.error('Unhandled Promise Rejection:', err);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err: Error) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

// Start the server
startServer();

export default app;
