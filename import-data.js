const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');

// Target database connection
const TARGET_URI = 'mongodb+srv://dammyspicy4:<EMAIL>/MicrolocsHq?retryWrites=true&w=majority';
const TARGET_DB = 'MicrolocsHq';

// Import directory
const IMPORT_DIR = './mongodb-export';

class DataImporter {
  constructor() {
    this.client = null;
    this.importLog = [];
  }

  async connect() {
    console.log('🔗 Connecting to target database...');
    
    try {
      this.client = new MongoClient(TARGET_URI);
      await this.client.connect();
      await this.client.db(TARGET_DB).admin().ping();
      console.log('✅ Connected to target database');
    } catch (error) {
      console.error('❌ Connection failed:', error.message);
      throw error;
    }
  }

  async getExportedFiles() {
    try {
      if (!fs.existsSync(IMPORT_DIR)) {
        throw new Error(`Export directory ${IMPORT_DIR} does not exist`);
      }

      const files = fs.readdirSync(IMPORT_DIR)
        .filter(file => file.endsWith('.json') && file !== 'export-report.json')
        .map(file => ({
          collection: file.replace('.json', ''),
          filePath: path.join(IMPORT_DIR, file)
        }));

      return files;
    } catch (error) {
      console.error('❌ Failed to read export directory:', error.message);
      throw error;
    }
  }

  async promptUser(question) {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question(question, (answer) => {
        rl.close();
        resolve(answer);
      });
    });
  }

  async importCollection(collectionName, filePath) {
    try {
      console.log(`\n📦 Importing collection: ${collectionName}`);
      console.log(`   📁 From file: ${filePath}`);
      
      // Read JSON file
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const documents = JSON.parse(fileContent);
      
      console.log(`   📊 Documents to import: ${documents.length}`);

      if (documents.length === 0) {
        console.log(`   ⚠️  No documents to import for ${collectionName}`);
        this.importLog.push({
          collection: collectionName,
          status: 'skipped',
          reason: 'empty',
          documents: 0
        });
        return;
      }

      const db = this.client.db(TARGET_DB);
      const collection = db.collection(collectionName);

      // Check if target collection exists and has data
      const targetCount = await collection.countDocuments();
      if (targetCount > 0) {
        console.log(`   ⚠️  Target collection ${collectionName} already has ${targetCount} documents`);
        const answer = await this.promptUser(`Do you want to clear the target collection ${collectionName} before import? (y/n): `);
        if (answer.toLowerCase() === 'y') {
          await collection.deleteMany({});
          console.log(`   🗑️  Cleared target collection ${collectionName}`);
        }
      }

      // Import documents in batches
      const batchSize = 1000;
      let importedCount = 0;

      for (let i = 0; i < documents.length; i += batchSize) {
        const batch = documents.slice(i, i + batchSize);
        
        try {
          await collection.insertMany(batch, { ordered: false });
          importedCount += batch.length;
          
          const progress = ((importedCount / documents.length) * 100).toFixed(1);
          console.log(`   📈 Progress: ${importedCount}/${documents.length} (${progress}%)`);
        } catch (error) {
          // Handle duplicate key errors gracefully
          if (error.code === 11000) {
            console.log(`   ⚠️  Some documents already exist, skipping duplicates...`);
            // Try inserting one by one to skip duplicates
            for (const doc of batch) {
              try {
                await collection.insertOne(doc);
                importedCount++;
              } catch (dupError) {
                if (dupError.code !== 11000) {
                  throw dupError;
                }
              }
            }
          } else {
            throw error;
          }
        }
      }

      console.log(`   ✅ Successfully imported ${importedCount} documents`);
      this.importLog.push({
        collection: collectionName,
        status: 'success',
        documents: importedCount
      });

    } catch (error) {
      console.error(`   ❌ Failed to import ${collectionName}:`, error.message);
      this.importLog.push({
        collection: collectionName,
        status: 'failed',
        error: error.message,
        documents: 0
      });
    }
  }

  async createIndexes() {
    try {
      console.log('\n🔧 Creating indexes on target database...');
      const db = this.client.db(TARGET_DB);

      // User indexes
      try {
        await db.collection('users').createIndex({ email: 1 }, { unique: true });
        console.log('   ✅ Created unique index on users.email');
      } catch (error) {
        console.log('   ⚠️  Index on users.email already exists or failed');
      }

      // Appointment indexes
      await db.collection('appointments').createIndex({ user: 1 });
      await db.collection('appointments').createIndex({ service: 1 });
      await db.collection('appointments').createIndex({ date: 1 });
      await db.collection('appointments').createIndex({ status: 1 });
      console.log('   ✅ Created indexes on appointments collection');

      // Product indexes
      await db.collection('products').createIndex({ name: 1 });
      await db.collection('products').createIndex({ category: 1 });
      console.log('   ✅ Created indexes on products collection');

    } catch (error) {
      console.error('❌ Failed to create some indexes:', error.message);
    }
  }

  async generateImportReport() {
    console.log('\n📊 Import Report:');
    console.log('='.repeat(50));
    
    let totalSuccess = 0;
    let totalFailed = 0;
    let totalDocuments = 0;

    this.importLog.forEach(log => {
      const status = log.status === 'success' ? '✅' : 
                    log.status === 'failed' ? '❌' : '⚠️';
      console.log(`${status} ${log.collection}: ${log.documents} documents`);
      
      if (log.status === 'success') {
        totalSuccess++;
        totalDocuments += log.documents;
      } else if (log.status === 'failed') {
        totalFailed++;
        console.log(`   Error: ${log.error}`);
      }
    });

    console.log('='.repeat(50));
    console.log(`📈 Summary:`);
    console.log(`   ✅ Successful collections: ${totalSuccess}`);
    console.log(`   ❌ Failed collections: ${totalFailed}`);
    console.log(`   📄 Total documents imported: ${totalDocuments}`);

    // Save report to file
    const reportPath = path.join(IMPORT_DIR, 'import-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      targetUri: TARGET_URI.replace(/\/\/.*@/, '//***:***@'),
      importDirectory: IMPORT_DIR,
      summary: {
        successfulCollections: totalSuccess,
        failedCollections: totalFailed,
        totalDocuments: totalDocuments
      },
      details: this.importLog
    }, null, 2));

    console.log(`📋 Import report saved to: ${reportPath}`);
  }

  async disconnect() {
    try {
      if (this.client) {
        await this.client.close();
        console.log('🔌 Disconnected from target database');
      }
    } catch (error) {
      console.error('❌ Error during disconnect:', error.message);
    }
  }

  async import() {
    try {
      console.log('🚀 Starting data import...');
      console.log(`📍 Target: ${TARGET_URI.replace(/\/\/.*@/, '//***:***@')}`);
      console.log(`📁 Import Directory: ${IMPORT_DIR}`);
      
      await this.connect();
      
      // Get exported files
      const exportedFiles = await this.getExportedFiles();
      console.log(`📦 Found ${exportedFiles.length} collections to import:`);
      exportedFiles.forEach(file => console.log(`   - ${file.collection}`));

      // Confirm import
      const confirm = await this.promptUser('\n❓ Do you want to proceed with the import? (y/n): ');
      if (confirm.toLowerCase() !== 'y') {
        console.log('❌ Import cancelled by user');
        return;
      }

      // Import each collection
      for (const file of exportedFiles) {
        await this.importCollection(file.collection, file.filePath);
      }

      // Create indexes
      await this.createIndexes();

      // Generate report
      await this.generateImportReport();

      console.log('\n🎉 Data import completed successfully!');

    } catch (error) {
      console.error('❌ Import failed:', error.message);
      throw error;
    } finally {
      await this.disconnect();
    }
  }
}

// Main execution
async function main() {
  const importer = new DataImporter();
  
  try {
    await importer.import();
  } catch (error) {
    console.error('💥 Import process failed:', error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Import interrupted by user');
  process.exit(0);
});

// Run import if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = DataImporter;
