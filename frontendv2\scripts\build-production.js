#!/usr/bin/env node

/**
 * Production Build Script for Frontend v2
 * This script handles the complete production build process
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function generateBuildInfo() {
  const buildDate = new Date().toISOString();
  const buildHash = crypto.randomBytes(8).toString('hex');
  
  return {
    buildDate,
    buildHash,
    version: process.env.npm_package_version || '1.0.0',
    nodeVersion: process.version,
    environment: 'production'
  };
}

function updateEnvironmentFile(buildInfo) {
  const envPath = path.join(__dirname, '../.env.production');
  
  if (fs.existsSync(envPath)) {
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // Replace build placeholders
    envContent = envContent.replace('__BUILD_DATE__', buildInfo.buildDate);
    envContent = envContent.replace('__BUILD_HASH__', buildInfo.buildHash);
    
    fs.writeFileSync(envPath, envContent);
    log('✓ Updated environment file with build info', 'green');
  }
}

function createBuildInfoFile(buildInfo) {
  const buildInfoPath = path.join(__dirname, '../public/build-info.json');
  
  fs.writeFileSync(buildInfoPath, JSON.stringify(buildInfo, null, 2));
  log('✓ Created build info file', 'green');
}

function runPreBuildChecks() {
  log('Running pre-build checks...', 'cyan');
  
  // Check if required files exist
  const requiredFiles = [
    'package.json',
    'src/index.tsx',
    'public/index.html',
    '.env.production'
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(path.join(__dirname, '..', file))) {
      log(`✗ Required file missing: ${file}`, 'red');
      process.exit(1);
    }
  }
  
  log('✓ All required files present', 'green');
  
  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 16) {
    log(`✗ Node.js version ${nodeVersion} is not supported. Please use Node.js 16 or higher.`, 'red');
    process.exit(1);
  }
  
  log(`✓ Node.js version ${nodeVersion} is supported`, 'green');
}

function installDependencies() {
  log('Installing production dependencies...', 'cyan');
  
  try {
    execSync('npm ci --only=production', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    log('✓ Dependencies installed successfully', 'green');
  } catch (error) {
    log('✗ Failed to install dependencies', 'red');
    process.exit(1);
  }
}

function runBuild() {
  log('Building application for production...', 'cyan');
  
  try {
    execSync('npm run build', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..'),
      env: {
        ...process.env,
        NODE_ENV: 'production',
        GENERATE_SOURCEMAP: 'false'
      }
    });
    log('✓ Build completed successfully', 'green');
  } catch (error) {
    log('✗ Build failed', 'red');
    process.exit(1);
  }
}

function optimizeBuild() {
  log('Optimizing build...', 'cyan');
  
  const buildDir = path.join(__dirname, '../build');
  
  if (!fs.existsSync(buildDir)) {
    log('✗ Build directory not found', 'red');
    process.exit(1);
  }
  
  // Get build size
  const getBuildSize = (dir) => {
    let size = 0;
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        size += getBuildSize(filePath);
      } else {
        size += stats.size;
      }
    }
    
    return size;
  };
  
  const buildSize = getBuildSize(buildDir);
  const buildSizeMB = (buildSize / 1024 / 1024).toFixed(2);
  
  log(`✓ Build size: ${buildSizeMB} MB`, 'green');
  
  // Check if build size is reasonable (warn if > 10MB)
  if (buildSize > 10 * 1024 * 1024) {
    log(`⚠ Warning: Build size is large (${buildSizeMB} MB). Consider code splitting or asset optimization.`, 'yellow');
  }
}

function runPostBuildTasks() {
  log('Running post-build tasks...', 'cyan');
  
  // Create robots.txt for production
  const robotsTxt = `User-agent: *
Allow: /

Sitemap: https://yourdomain.com/sitemap.xml`;
  
  fs.writeFileSync(path.join(__dirname, '../build/robots.txt'), robotsTxt);
  log('✓ Created robots.txt', 'green');
  
  // Create .htaccess for Apache servers
  const htaccess = `RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>`;
  
  fs.writeFileSync(path.join(__dirname, '../build/.htaccess'), htaccess);
  log('✓ Created .htaccess', 'green');
}

function main() {
  log('🚀 Starting production build process...', 'bright');
  log('=====================================', 'bright');
  
  const startTime = Date.now();
  
  try {
    // Generate build information
    const buildInfo = generateBuildInfo();
    
    // Run all build steps
    runPreBuildChecks();
    updateEnvironmentFile(buildInfo);
    createBuildInfoFile(buildInfo);
    installDependencies();
    runBuild();
    optimizeBuild();
    runPostBuildTasks();
    
    const endTime = Date.now();
    const buildTime = ((endTime - startTime) / 1000).toFixed(2);
    
    log('=====================================', 'bright');
    log('🎉 Production build completed successfully!', 'green');
    log(`⏱️  Build time: ${buildTime} seconds`, 'cyan');
    log(`📦 Build hash: ${buildInfo.buildHash}`, 'cyan');
    log(`📅 Build date: ${buildInfo.buildDate}`, 'cyan');
    log('=====================================', 'bright');
    
  } catch (error) {
    log('=====================================', 'bright');
    log('💥 Production build failed!', 'red');
    log(`Error: ${error.message}`, 'red');
    log('=====================================', 'bright');
    process.exit(1);
  }
}

// Run the build process
main();
