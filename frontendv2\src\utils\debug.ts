// Debug utilities for API logging

export const logApiRequest = (endpoint: string, token: string | null) => {
  console.log(`🔗 API Request: ${endpoint}`);
  console.log(`🔑 Token: ${token ? 'Present' : 'Missing'}`);
  if (token) {
    console.log(`🔑 Token preview: ${token.substring(0, 20)}...`);
  }
};

export const logApiResponse = (endpoint: string, status: number, data: any) => {
  console.log(`📥 API Response for ${endpoint}: ${status}`);
  console.log(`📄 Response data:`, data);
};

export const logAuthState = () => {
  const token = localStorage.getItem('authToken');
  const user = localStorage.getItem('currentUser');
  
  console.log('🔍 Auth State Check:');
  console.log('🔑 Auth Token:', token ? 'Present' : 'Missing');
  console.log('👤 Current User:', user ? JSON.parse(user) : 'None');
  
  return { token, user: user ? JSON.parse(user) : null };
};
