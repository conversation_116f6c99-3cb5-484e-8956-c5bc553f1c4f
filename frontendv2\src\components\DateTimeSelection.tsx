import { useState } from 'react'
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css';

interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
}

interface BookingState {
  selectedService: any;
  selectedAddOns: AddOnService[];
  selectedDate: string;
  selectedTime: string;
  step: 'services' | 'datetime' | 'details' | 'checkout';
}

interface DateTimeSelectionProps {
  booking: BookingState;
  onBack: () => void;
  onSelect: (date: string, time: string) => void;
  addOnServices: AddOnService[];
  onAddOnToggle: (addOn: AddOnService) => void;
}

export default function DateTimeSelection({ 
  booking, 
  onBack, 
  onSelect, 
  addOnServices, 
  onAddOnToggle 
}: DateTimeSelectionProps) {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string>('');

  const handleDateSelect = (date: Date | null) => {
    setSelectedDate(date);
    setSelectedTime(''); // Reset time when date changes
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    if (selectedDate) {
      const dateString = selectedDate.toISOString().split('T')[0];
      onSelect(dateString, time);
    }
  };

  // Get minimum date (tomorrow)
  const minDate = new Date();
  minDate.setDate(minDate.getDate() + 1);

  // Get maximum date (3 months from now)
  const maxDate = new Date();
  maxDate.setMonth(maxDate.getMonth() + 3);

  // Filter out Sundays and past dates
  const isDateDisabled = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Disable past dates and Sundays (0 = Sunday)
    return date < today || date.getDay() === 0;
  };

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <div className="auth-links">
            <a href="#" className="auth-link">SIGN UP</a>
            <a href="#" className="auth-link">LOG IN</a>
          </div>
        </div>
      </header>

      <main className="main-content">
        <div className="datetime-selection">
          <div className="datetime-header">
            <button className="back-button" onClick={onBack}>
              ← SELECT APPOINTMENT
            </button>
            <h2 className="datetime-title">Date & Time</h2>
          </div>

          {/* Appointment Summary */}
          <div className="appointment-summary">
            <h3>APPOINTMENT</h3>
            <div className="appointment-card">
              <div className="appointment-details">
                <h4>{booking.selectedService?.name}</h4>
                <p className="appointment-price">${booking.selectedService?.price}</p>
                
                {booking.selectedAddOns.map(addOn => (
                  <div key={addOn.id} className="addon-line">
                    + {addOn.name}, {addOn.duration} minutes @ ${addOn.price}
                  </div>
                ))}
                
                <p className="appointment-note">*EXISTING CLIENTS ONLY*</p>
              </div>
              <button className="close-button">×</button>
            </div>
          </div>

          {/* Add-on Services */}
          <div className="addon-section">
            <h3>ADD TO APPOINTMENT</h3>
            <div className="addon-grid">
              {addOnServices.map(addOn => (
                <div key={addOn.id} className="addon-item">
                  <input
                    type="checkbox"
                    id={addOn.id}
                    checked={booking.selectedAddOns.some(item => item.id === addOn.id)}
                    onChange={() => onAddOnToggle(addOn)}
                  />
                  <label htmlFor={addOn.id}>
                    {addOn.name}
                    <br />
                    + {addOn.duration} minutes @ ${addOn.price}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Calendar */}
          <div className="calendar-section">
            <h3>Select Date</h3>
            <div className="datepicker-wrapper">
              <DatePicker
                selected={selectedDate}
                onChange={handleDateSelect}
                minDate={minDate}
                maxDate={maxDate}
                filterDate={(date) => !isDateDisabled(date)}
                inline
                calendarClassName="custom-calendar"
                dayClassName={(date) => {
                  if (isDateDisabled(date)) return 'disabled-date';
                  return 'available-date';
                }}
              />
            </div>

            {/* Selected Date and Time */}
            {selectedDate && (
              <div className="time-selection">
                <div className="selected-date">
                  <h4>{selectedDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}</h4>
                  <p className="timezone">TIME ZONE: CENTRAL TIME (GMT-6:00)</p>
                </div>

                <div className="time-slots">
                  {['9:00 AM', '10:00 AM', '11:00 AM', '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM'].map(time => (
                    <button
                      key={time}
                      className={`time-slot ${selectedTime === time ? 'selected' : ''}`}
                      onClick={() => handleTimeSelect(time)}
                    >
                      {time}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
