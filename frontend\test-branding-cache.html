<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Branding Cache Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .cache-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Branding Cache Test</h1>
        <p>This test verifies the localStorage caching functionality for branding data.</p>
        
        <div class="test-section info">
            <h3>📋 Test Instructions</h3>
            <ol>
                <li>Click "Test Cache Functionality" to run automated tests</li>
                <li>Check the cache status and timing information</li>
                <li>Verify that cached data is used within 30 minutes</li>
                <li>Test cache expiration after 30 minutes (simulated)</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔧 Cache Controls</h3>
            <button onclick="testCacheFunctionality()">Test Cache Functionality</button>
            <button onclick="clearCache()">Clear Cache</button>
            <button onclick="viewCacheData()">View Cache Data</button>
            <button onclick="simulateExpiredCache()">Simulate Expired Cache</button>
        </div>

        <div id="results"></div>
        
        <div class="cache-info" id="cacheInfo">
            <strong>Cache Status:</strong> <span id="cacheStatus">Not checked</span><br>
            <strong>Cache Age:</strong> <span id="cacheAge">N/A</span><br>
            <strong>Cache Size:</strong> <span id="cacheSize">N/A</span>
        </div>
    </div>

    <script>
        const CACHE_KEY = 'branding_data_cache';
        const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function updateCacheInfo() {
            const cached = localStorage.getItem(CACHE_KEY);
            const statusEl = document.getElementById('cacheStatus');
            const ageEl = document.getElementById('cacheAge');
            const sizeEl = document.getElementById('cacheSize');

            if (cached) {
                try {
                    const { timestamp } = JSON.parse(cached);
                    const age = Date.now() - timestamp;
                    const ageMinutes = Math.floor(age / 60000);
                    const ageSeconds = Math.floor((age % 60000) / 1000);
                    
                    statusEl.textContent = age < CACHE_DURATION ? 'Valid' : 'Expired';
                    ageEl.textContent = `${ageMinutes}m ${ageSeconds}s`;
                    sizeEl.textContent = `${Math.round(cached.length / 1024 * 100) / 100} KB`;
                } catch (e) {
                    statusEl.textContent = 'Invalid';
                    ageEl.textContent = 'N/A';
                    sizeEl.textContent = 'N/A';
                }
            } else {
                statusEl.textContent = 'No cache';
                ageEl.textContent = 'N/A';
                sizeEl.textContent = 'N/A';
            }
        }

        function testCacheFunctionality() {
            log('🚀 Starting cache functionality tests...', 'info');
            
            // Test 1: Clear existing cache
            localStorage.removeItem(CACHE_KEY);
            log('✅ Test 1: Cache cleared', 'success');
            
            // Test 2: Create mock cache data
            const mockData = {
                global: { siteName: 'Test Site', phone: '************' },
                home: { heroTitle: 'Welcome Test' },
                theme: { colors: { primary: '#f3d016' } }
            };
            
            const cacheData = {
                data: mockData,
                timestamp: Date.now()
            };
            
            localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
            log('✅ Test 2: Mock cache data created', 'success');
            
            // Test 3: Verify cache retrieval
            const retrieved = localStorage.getItem(CACHE_KEY);
            if (retrieved) {
                const parsed = JSON.parse(retrieved);
                if (parsed.data && parsed.timestamp) {
                    log('✅ Test 3: Cache data retrieved successfully', 'success');
                } else {
                    log('❌ Test 3: Cache data structure invalid', 'error');
                }
            } else {
                log('❌ Test 3: Failed to retrieve cache data', 'error');
            }
            
            // Test 4: Check cache age validation
            const age = Date.now() - cacheData.timestamp;
            if (age < CACHE_DURATION) {
                log('✅ Test 4: Cache age validation passed (cache is fresh)', 'success');
            } else {
                log('❌ Test 4: Cache age validation failed (cache expired)', 'error');
            }
            
            updateCacheInfo();
            log('🎉 Cache functionality tests completed!', 'info');
        }

        function clearCache() {
            localStorage.removeItem(CACHE_KEY);
            log('🗑️ Cache cleared manually', 'info');
            updateCacheInfo();
        }

        function viewCacheData() {
            const cached = localStorage.getItem(CACHE_KEY);
            if (cached) {
                try {
                    const parsed = JSON.parse(cached);
                    log(`📄 Cache Data: <pre>${JSON.stringify(parsed, null, 2)}</pre>`, 'info');
                } catch (e) {
                    log('❌ Invalid cache data format', 'error');
                }
            } else {
                log('📭 No cache data found', 'info');
            }
            updateCacheInfo();
        }

        function simulateExpiredCache() {
            const mockData = {
                global: { siteName: 'Expired Test Site' },
                home: { heroTitle: 'Expired Welcome' }
            };
            
            // Set timestamp to 31 minutes ago (expired)
            const expiredTimestamp = Date.now() - (31 * 60 * 1000);
            const cacheData = {
                data: mockData,
                timestamp: expiredTimestamp
            };
            
            localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
            log('⏰ Simulated expired cache (31 minutes old)', 'info');
            updateCacheInfo();
        }

        // Update cache info on page load
        updateCacheInfo();
        
        // Auto-update cache info every 5 seconds
        setInterval(updateCacheInfo, 5000);
    </script>
</body>
</html>
