import { useState, useEffect, useCallback } from 'react';
import { adminAPI, type AdminAppointment, type AppointmentFilters } from '../../utils/api';
import AppointmentList from './AppointmentList';
import AppointmentModal from './AppointmentModal';
import FilterComponent from './AppointmentFilters';
import LoadingSpinner from '../LoadingSpinner';
import { useNotifications } from '../../hooks/useRealTimeUpdates';

interface AppointmentManagementProps {
  onAppointmentUpdate?: () => void;
}

export default function AppointmentManagement({ onAppointmentUpdate }: AppointmentManagementProps) {
  const [appointments, setAppointments] = useState<AdminAppointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAppointments, setSelectedAppointments] = useState<string[]>([]);
  const { showNotification } = useNotifications();
  
  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit' | 'view'>('create');
  const [editingAppointment, setEditingAppointment] = useState<AdminAppointment | null>(null);
  
  // Filter states
  const [filters, setFilters] = useState<AppointmentFilters>({
    page: 1,
    limit: 20,
    status: 'all',
    sortBy: 'date',
    sortOrder: 'desc'
  });
  
  // Pagination
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });

  // Load appointments
  const loadAppointments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await adminAPI.getAppointments(filters);
      
      if (response.success) {
        setAppointments(response.data.appointments);
        setPagination(response.data.pagination);
      } else {
        setError('Failed to load appointments');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load appointments');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Load appointments on mount and filter changes
  useEffect(() => {
    loadAppointments();
  }, [loadAppointments]);

  // Real-time updates removed - users will refresh when needed

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<AppointmentFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: newFilters.page || 1 // Reset to page 1 when filters change (except pagination)
    }));
  };

  // Handle appointment status update
  const handleStatusUpdate = async (appointmentId: string, status: string) => {
    try {
      const response = await adminAPI.updateAppointmentStatus(appointmentId, status);

      if (response.success) {
        // Update the appointment in the list
        setAppointments(prev =>
          prev.map(apt =>
            apt.id === appointmentId
              ? { ...apt, status: status as any, updatedAt: new Date().toISOString() }
              : apt
          )
        );

        // Show success notification
        showNotification(`Appointment status updated to ${status}`, 'success');

        // Notify parent component
        onAppointmentUpdate?.();
      } else {
        setError('Failed to update appointment status');
        showNotification('Failed to update appointment status', 'error');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update appointment status';
      setError(errorMessage);
      showNotification(errorMessage, 'error');
    }
  };

  // Handle bulk status update
  const handleBulkStatusUpdate = async (status: string) => {
    if (selectedAppointments.length === 0) {
      setError('Please select appointments to update');
      showNotification('Please select appointments to update', 'error');
      return;
    }

    try {
      const response = await adminAPI.bulkUpdateStatus(selectedAppointments, status);

      if (response.success) {
        // Reload appointments to get updated data
        await loadAppointments();
        setSelectedAppointments([]);
        showNotification(`${selectedAppointments.length} appointments updated to ${status}`, 'success');
        onAppointmentUpdate?.();
      } else {
        setError('Failed to update appointments');
        showNotification('Failed to update appointments', 'error');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update appointments';
      setError(errorMessage);
      showNotification(errorMessage, 'error');
    }
  };

  // Handle appointment deletion
  const handleDelete = async (appointmentId: string) => {
    if (!window.confirm('Are you sure you want to delete this appointment?')) {
      return;
    }

    try {
      const response = await adminAPI.deleteAppointment(appointmentId);

      if (response.success) {
        // Remove appointment from list
        setAppointments(prev => prev.filter(apt => apt.id !== appointmentId));
        showNotification('Appointment deleted successfully', 'success');
        onAppointmentUpdate?.();
      } else {
        setError('Failed to delete appointment');
        showNotification('Failed to delete appointment', 'error');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete appointment';
      setError(errorMessage);
      showNotification(errorMessage, 'error');
    }
  };

  // Handle appointment creation/editing
  const handleSave = async (appointmentData: any) => {
    try {
      let response;

      if (modalType === 'edit' && editingAppointment) {
        response = await adminAPI.updateAppointment(editingAppointment.id, appointmentData);
      } else {
        response = await adminAPI.createAppointment(appointmentData);
      }

      if (response.success) {
        await loadAppointments();
        setShowModal(false);
        setEditingAppointment(null);
        showNotification(
          `Appointment ${modalType === 'edit' ? 'updated' : 'created'} successfully`,
          'success'
        );
        onAppointmentUpdate?.();
      } else {
        const errorMessage = `Failed to ${modalType === 'edit' ? 'update' : 'create'} appointment`;
        setError(errorMessage);
        showNotification(errorMessage, 'error');
      }
    } catch (err) {
      let errorMessage = `Failed to ${modalType === 'edit' ? 'update' : 'create'} appointment`;

      if (err instanceof Error) {
        // Use the specific error message from the API
        errorMessage = err.message;
      }

      setError(errorMessage);
      showNotification(errorMessage, 'error');
    }
  };

  // Handle edit appointment
  const handleEdit = (appointment: AdminAppointment) => {
    setEditingAppointment(appointment);
    setModalType('edit');
    setShowModal(true);
  };

  // Handle view appointment
  const handleView = (appointment: AdminAppointment) => {
    setEditingAppointment(appointment);
    setModalType('view');
    setShowModal(true);
  };

  // Handle create new appointment
  const handleCreate = () => {
    setEditingAppointment(null);
    setModalType('create');
    setShowModal(true);
  };

  // Clear error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  if (loading && appointments.length === 0) {
    return <LoadingSpinner />;
  }

  return (
    <div className="appointment-management">
      <div className="appointment-management-header">
        <div className="header-content">
          <h2>Appointment Management</h2>
          <p>Manage all customer appointments and bookings</p>
        </div>
        
        <div className="header-actions">
          <button 
            className="btn btn-primary"
            onClick={handleCreate}
          >
            + New Appointment
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError(null)} className="error-close">×</button>
        </div>
      )}

      <FilterComponent
        filters={filters}
        onFilterChange={handleFilterChange}
        selectedCount={selectedAppointments.length}
        onBulkStatusUpdate={handleBulkStatusUpdate}
      />

      <AppointmentList
        appointments={appointments}
        loading={loading}
        selectedAppointments={selectedAppointments}
        onSelectionChange={setSelectedAppointments}
        onStatusUpdate={handleStatusUpdate}
        onEdit={handleEdit}
        onView={handleView}
        onDelete={handleDelete}
        pagination={pagination}
        onPageChange={(page) => handleFilterChange({ page })}
      />

      {showModal && (
        <AppointmentModal
          isOpen={showModal}
          onClose={() => {
            setShowModal(false);
            setEditingAppointment(null);
          }}
          onSave={handleSave}
          appointment={editingAppointment}
          mode={modalType}
        />
      )}
    </div>
  );
}
