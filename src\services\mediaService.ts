import { Media, IMedia } from '../models/Media';
import { uploadBufferToCloudinary, deleteFromCloudinary } from './cloudinaryService';

export class MediaService {
  /**
   * Upload media file
   */
  static async uploadMedia(
    file: Express.Multer.File,
    uploadedBy: string,
    metadata?: {
      alt?: string;
      caption?: string;
      description?: string;
      tags?: string[];
    }
  ): Promise<IMedia> {
    try {
      // Upload to Cloudinary
      const uploadResult = await uploadBufferToCloudinary(file.buffer, {
        folder: 'media-library',
        resource_type: 'auto'
      });

      // Create media record
      const media = new Media({
        filename: uploadResult.public_id,
        originalName: file.originalname,
        mimeType: file.mimetype,
        size: file.size,
        url: uploadResult.secure_url,
        cloudinaryId: uploadResult.public_id,
        uploadedBy,
        alt: metadata?.alt || '',
        caption: metadata?.caption || '',
        description: metadata?.description || '',
        tags: metadata?.tags || [],
        usedIn: []
      });

      return await media.save();
    } catch (error) {
      console.error('Error uploading media:', error);
      throw new Error('Failed to upload media file');
    }
  }

  /**
   * Get media library with pagination and filters
   */
  static async getMediaLibrary(params: {
    page?: number;
    limit?: number;
    search?: string;
    mimeType?: string;
    tags?: string[];
    uploadedBy?: string;
  }) {
    const {
      page = 1,
      limit = 20,
      search,
      mimeType,
      tags,
      uploadedBy
    } = params;

    const filter: any = { isActive: true };

    // Search filter
    if (search) {
      filter.$or = [
        { originalName: { $regex: search, $options: 'i' } },
        { alt: { $regex: search, $options: 'i' } },
        { caption: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // MIME type filter
    if (mimeType) {
      if (mimeType === 'image') {
        filter.mimeType = { $regex: '^image/', $options: 'i' };
      } else if (mimeType === 'video') {
        filter.mimeType = { $regex: '^video/', $options: 'i' };
      } else if (mimeType === 'document') {
        filter.mimeType = { $regex: '^application/', $options: 'i' };
      } else {
        filter.mimeType = mimeType;
      }
    }

    // Tags filter
    if (tags && tags.length > 0) {
      filter.tags = { $in: tags };
    }

    // Uploaded by filter
    if (uploadedBy) {
      filter.uploadedBy = uploadedBy;
    }

    const skip = (page - 1) * limit;

    const [media, total] = await Promise.all([
      Media.find(filter)
        .populate('uploadedBy', 'firstName lastName name email')
        .sort({ uploadedAt: -1 })
        .skip(skip)
        .limit(limit),
      Media.countDocuments(filter)
    ]);

    return {
      media,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    };
  }

  /**
   * Get media by ID
   */
  static async getMediaById(id: string): Promise<IMedia | null> {
    return await Media.findById(id)
      .populate('uploadedBy', 'firstName lastName name email');
  }

  /**
   * Update media metadata
   */
  static async updateMedia(
    id: string,
    updateData: {
      alt?: string;
      caption?: string;
      description?: string;
      tags?: string[];
    }
  ): Promise<IMedia | null> {
    return await Media.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('uploadedBy', 'firstName lastName name email');
  }

  /**
   * Delete media
   */
  static async deleteMedia(id: string): Promise<boolean> {
    try {
      const media = await Media.findById(id);
      if (!media) {
        return false;
      }

      // Delete from Cloudinary
      if (media.cloudinaryId) {
        await deleteFromCloudinary(media.cloudinaryId);
      }

      // Soft delete (mark as inactive)
      await Media.findByIdAndUpdate(id, { isActive: false });
      
      return true;
    } catch (error) {
      console.error('Error deleting media:', error);
      return false;
    }
  }

  /**
   * Track media usage
   */
  static async trackMediaUsage(
    mediaId: string,
    usage: {
      type: 'product' | 'service' | 'branding' | 'other';
      id: string;
      field: string;
    }
  ): Promise<void> {
    await Media.findByIdAndUpdate(
      mediaId,
      {
        $addToSet: {
          usedIn: usage
        }
      }
    );
  }

  /**
   * Remove media usage tracking
   */
  static async removeMediaUsage(
    mediaId: string,
    usage: {
      type: 'product' | 'service' | 'branding' | 'other';
      id: string;
      field: string;
    }
  ): Promise<void> {
    await Media.findByIdAndUpdate(
      mediaId,
      {
        $pull: {
          usedIn: usage
        }
      }
    );
  }

  /**
   * Get media usage statistics
   */
  static async getMediaStats() {
    const stats = await Media.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: null,
          totalFiles: { $sum: 1 },
          totalSize: { $sum: '$size' },
          imageCount: {
            $sum: {
              $cond: [{ $regexMatch: { input: '$mimeType', regex: '^image/' } }, 1, 0]
            }
          },
          videoCount: {
            $sum: {
              $cond: [{ $regexMatch: { input: '$mimeType', regex: '^video/' } }, 1, 0]
            }
          },
          documentCount: {
            $sum: {
              $cond: [{ $regexMatch: { input: '$mimeType', regex: '^application/' } }, 1, 0]
            }
          }
        }
      }
    ]);

    return stats[0] || {
      totalFiles: 0,
      totalSize: 0,
      imageCount: 0,
      videoCount: 0,
      documentCount: 0
    };
  }

  /**
   * Get all unique tags
   */
  static async getAllTags(): Promise<string[]> {
    const result = await Media.aggregate([
      { $match: { isActive: true } },
      { $unwind: '$tags' },
      { $group: { _id: '$tags' } },
      { $sort: { _id: 1 } }
    ]);

    return result.map(item => item._id);
  }

  /**
   * Bulk delete media
   */
  static async bulkDeleteMedia(ids: string[]): Promise<number> {
    let deletedCount = 0;

    for (const id of ids) {
      const success = await this.deleteMedia(id);
      if (success) deletedCount++;
    }

    return deletedCount;
  }
}
