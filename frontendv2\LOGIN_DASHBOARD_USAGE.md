# Login Response Storage and Dashboard API Usage

This guide explains how to handle the login response data and use it to call the dashboard endpoint.

## Updated API Structure

The API utilities have been updated to handle the new login response format that includes:
- User data with additional fields (`_id`, `name`, `notificationPreferences`, etc.)
- Both `token` and `refreshToken`
- Proper storage and retrieval of authentication data

## Key Functions

### 1. `saveLoginResponseData(loginResponse)`
Saves the complete login response to localStorage, including:
- `authToken` - JWT token for API authentication
- `refreshToken` - Refresh token for token renewal
- `currentUser` - Complete user object
- `userEmail` - User's email for quick access
- `userId` - User's ID for quick access

### 2. `dashboardAPI.getUserDashboard()`
Calls the `/dashboard` endpoint using stored authentication token.

### 3. `dashboardAPI.getUserAppointments()`
Calls the `/appointments/user` endpoint with user parameters.

## Usage Example

```typescript
import { saveLoginResponseData, dashboardAPI } from '../utils/api';

// Your login response from the backend
const loginResponse = {
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "notificationPreferences": {
        "email": true,
        "sms": false,
        "push": true
      },
      "_id": "685930e41037604a323737e7",
      "name": "Joshua Olawore",
      "firstName": "Joshua",
      "lastName": "Olawore",
      "email": "<EMAIL>",
      "phone": "+2347049670618",
      "role": "user",
      "isVerified": false,
      "favorites": [],
      "createdAt": "2025-06-23T10:48:04.934Z",
      "updatedAt": "2025-06-27T17:55:03.344Z",
      "__v": 0
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
};

// Step 1: Save the login response data
saveLoginResponseData(loginResponse);

// Step 2: Call the dashboard endpoint
try {
  const dashboardData = await dashboardAPI.getUserDashboard();
  console.log('Dashboard data:', dashboardData);
} catch (error) {
  console.error('Dashboard API error:', error);
}

// Step 3: Call user appointments endpoint
try {
  const appointments = await dashboardAPI.getUserAppointments();
  console.log('User appointments:', appointments);
} catch (error) {
  console.error('Appointments API error:', error);
}
```

## Authentication Flow

1. **Login**: User provides email/password or email-only
2. **Response**: Backend returns user data, token, and refreshToken
3. **Storage**: `saveLoginResponseData()` stores all data in localStorage
4. **API Calls**: Subsequent API calls automatically include the stored token
5. **Dashboard**: Call `dashboardAPI.getUserDashboard()` to get user dashboard data

## Stored Data Structure

After calling `saveLoginResponseData()`, the following items are stored in localStorage:

```
authToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
currentUser: "{\"_id\":\"685930e41037604a323737e7\",\"name\":\"Joshua Olawore\",...}"
userEmail: "<EMAIL>"
userId: "685930e41037604a323737e7"
```

## Utility Functions

- `getCurrentUser()` - Get the stored user object
- `isAuthenticated()` - Check if user is logged in
- `isAdmin()` - Check if user has admin role
- `clearAuthData()` - Clear all stored authentication data
- `getStoredUserData()` - Get email and userId for API calls

## Error Handling

The API functions include proper error handling:
- Network errors
- Authentication failures
- Missing token errors
- API response errors

## Example Component

See `frontendV2/src/components/LoginExample.tsx` for a complete working example that demonstrates:
- Saving login response data
- Calling dashboard API
- Calling user appointments API
- Error handling
- Loading states

## Integration with Existing Components

The `UserDashboard` component has been updated to:
- Use the new API functions
- Handle the updated user data structure
- Properly clear authentication data on logout
- Support both old and new user data formats for backward compatibility

## Testing

To test the implementation:
1. Import and use the `LoginExample` component
2. Click "Save Login Data & Call Dashboard" to simulate the login flow
3. Check browser console for API calls and responses
4. Check localStorage to verify data storage
5. Use other buttons to test individual API calls
