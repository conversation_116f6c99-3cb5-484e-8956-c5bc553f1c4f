/**
 * Request Lock Utility - Prevents duplicate API requests
 * Similar to a mutex in Rust, this locks sensitive operations until completion
 */

class RequestLock {
  constructor() {
    this.locks = new Map(); // Map of operation keys to lock status
    this.timeouts = new Map(); // Map of operation keys to timeout IDs
  }

  /**
   * Generate a unique key for the operation
   * @param {string} operation - Operation type (e.g., 'payment-confirmation', 'appointment-booking')
   * @param {string} identifier - Unique identifier (e.g., appointmentId, userId)
   * @returns {string} Unique lock key
   */
  generateKey(operation, identifier = 'default') {
    return `${operation}:${identifier}`;
  }

  /**
   * Acquire a lock for an operation
   * @param {string} operation - Operation type
   * @param {string} identifier - Unique identifier
   * @param {number} timeout - Lock timeout in milliseconds (default: 30 seconds)
   * @returns {boolean} True if lock acquired, false if already locked
   */
  acquire(operation, identifier = 'default', timeout = 30000) {
    const key = this.generateKey(operation, identifier);
    
    // Check if already locked
    if (this.locks.has(key)) {
      console.warn(`Request lock: Operation ${key} is already in progress`);
      return false;
    }

    // Acquire lock
    this.locks.set(key, {
      timestamp: Date.now(),
      operation,
      identifier
    });

    // Set timeout to auto-release lock
    const timeoutId = setTimeout(() => {
      console.warn(`Request lock: Auto-releasing lock for ${key} due to timeout`);
      this.release(operation, identifier);
    }, timeout);

    this.timeouts.set(key, timeoutId);

    console.log(`Request lock: Acquired lock for ${key}`);
    return true;
  }

  /**
   * Release a lock for an operation
   * @param {string} operation - Operation type
   * @param {string} identifier - Unique identifier
   */
  release(operation, identifier = 'default') {
    const key = this.generateKey(operation, identifier);
    
    // Clear timeout
    const timeoutId = this.timeouts.get(key);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.timeouts.delete(key);
    }

    // Release lock
    if (this.locks.has(key)) {
      this.locks.delete(key);
      console.log(`Request lock: Released lock for ${key}`);
    }
  }

  /**
   * Check if an operation is currently locked
   * @param {string} operation - Operation type
   * @param {string} identifier - Unique identifier
   * @returns {boolean} True if locked, false otherwise
   */
  isLocked(operation, identifier = 'default') {
    const key = this.generateKey(operation, identifier);
    return this.locks.has(key);
  }

  /**
   * Execute a function with lock protection
   * @param {string} operation - Operation type
   * @param {string} identifier - Unique identifier
   * @param {Function} fn - Function to execute
   * @param {number} timeout - Lock timeout in milliseconds
   * @returns {Promise} Promise that resolves with function result or rejects if locked
   */
  async withLock(operation, identifier = 'default', fn, timeout = 30000) {
    // Try to acquire lock
    if (!this.acquire(operation, identifier, timeout)) {
      throw new Error(`Operation ${operation} is already in progress. Please wait for the current request to complete.`);
    }

    try {
      // Execute the function
      const result = await fn();
      return result;
    } finally {
      // Always release lock, even if function throws
      this.release(operation, identifier);
    }
  }

  /**
   * Get all active locks (for debugging)
   * @returns {Array} Array of active lock information
   */
  getActiveLocks() {
    const activeLocks = [];
    for (const [key, lockInfo] of this.locks.entries()) {
      activeLocks.push({
        key,
        ...lockInfo,
        duration: Date.now() - lockInfo.timestamp
      });
    }
    return activeLocks;
  }

  /**
   * Clear all locks (emergency use only)
   */
  clearAllLocks() {
    console.warn('Request lock: Clearing all locks (emergency)');
    
    // Clear all timeouts
    for (const timeoutId of this.timeouts.values()) {
      clearTimeout(timeoutId);
    }
    
    this.locks.clear();
    this.timeouts.clear();
  }

  /**
   * Clean up expired locks (housekeeping)
   * @param {number} maxAge - Maximum age in milliseconds (default: 5 minutes)
   */
  cleanupExpiredLocks(maxAge = 300000) {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, lockInfo] of this.locks.entries()) {
      if (now - lockInfo.timestamp > maxAge) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      const [operation, identifier] = key.split(':');
      console.warn(`Request lock: Cleaning up expired lock for ${key}`);
      this.release(operation, identifier);
    });

    return expiredKeys.length;
  }
}

// Create singleton instance
const requestLock = new RequestLock();

// Periodic cleanup of expired locks (every 5 minutes)
setInterval(() => {
  const cleaned = requestLock.cleanupExpiredLocks();
  if (cleaned > 0) {
    console.log(`Request lock: Cleaned up ${cleaned} expired locks`);
  }
}, 300000);

// Export singleton instance and class for testing
export default requestLock;
export { RequestLock };

// Export common operation types as constants
export const OPERATIONS = {
  PAYMENT_CONFIRMATION: 'payment-confirmation',
  APPOINTMENT_BOOKING: 'appointment-booking',
  ORDER_CREATION: 'order-creation',
  USER_REGISTRATION: 'user-registration',
  USER_LOGIN: 'user-login',
  PASSWORD_RESET: 'password-reset',
  PROFILE_UPDATE: 'profile-update',
  IMAGE_UPLOAD: 'image-upload',
  ADMIN_ACTION: 'admin-action'
};
