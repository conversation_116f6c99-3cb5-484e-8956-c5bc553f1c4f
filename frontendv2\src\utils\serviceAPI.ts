// Service API utilities and interfaces

import { API_CONFIG } from './config';

const API_BASE_URL = API_CONFIG.BASE_URL;

// Service interfaces
export interface Service {
  id: string;
  name: string;
  description: string;
  category: string;
  duration: number;
  price: number;
  isActive: boolean;
  image?: string;
  images?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface ServiceListResponse {
  success: boolean;
  data: {
    services: Service[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface ServiceFilters {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CreateServiceData {
  name: string;
  description: string;
  category: string;
  duration: number;
  price: number;
  isActive?: boolean;
  image?: string;
  images?: string[];
}

export interface UpdateServiceData {
  name?: string;
  description?: string;
  category?: string;
  duration?: number;
  price?: number;
  isActive?: boolean;
  image?: string;
  images?: string[];
}

// Helper function for API requests
async function apiRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
  const token = localStorage.getItem('authToken') || localStorage.getItem('token');
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
  
  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: 'Network error' }));
    throw new Error(error.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// Service API functions
export const serviceAPI = {
  // Get all services with filtering and pagination
  getServices: async (filters: ServiceFilters = {}): Promise<ServiceListResponse> => {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, value.toString());
      }
    });

    return await apiRequest(`/admin/services?${params.toString()}`);
  },

  // Get single service details
  getService: async (id: string): Promise<{ success: boolean; data: Service }> => {
    return await apiRequest(`/admin/services/${id}`);
  },

  // Create new service
  createService: async (serviceData: CreateServiceData): Promise<{ success: boolean; data: Service }> => {
    return await apiRequest('/admin/services', {
      method: 'POST',
      body: JSON.stringify(serviceData),
    });
  },

  // Update service
  updateService: async (id: string, updateData: UpdateServiceData): Promise<{ success: boolean; data: Service }> => {
    return await apiRequest(`/admin/services/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  },

  // Delete service
  deleteService: async (id: string): Promise<{ success: boolean; data: any }> => {
    return await apiRequest(`/admin/services/${id}`, {
      method: 'DELETE',
    });
  },

  // Get service categories
  getCategories: async (): Promise<{ success: boolean; data: string[] }> => {
    return await apiRequest('/services/categories');
  },
};
