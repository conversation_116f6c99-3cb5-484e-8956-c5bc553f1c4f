import { Request, Response } from 'express';
import { Service } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';
import { uploadSingleFile } from '../services/cloudinaryService';

export class ServiceController {
  static async getAllServices(req: Request, res: Response): Promise<void> {
    try {
      const { category, active } = req.query;
      const filter: any = {};

      if (category) {
        filter.category = category;
      }

      if (active !== undefined) {
        filter.isActive = active === 'true';
      } else {
        filter.isActive = true; // Default to active services only
      }

      const services = await Service.find(filter).sort({ name: 1 });

      sendSuccess(res, 'Services retrieved successfully', services);
    } catch (error) {
      console.error('Get all services error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getServiceById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const service = await Service.findById(id);

      if (!service) {
        sendNotFound(res, 'Service not found');
        return;
      }

      sendSuccess(res, 'Service retrieved successfully', service);
    } catch (error) {
      console.error('Get service by ID error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getServiceCategories(req: Request, res: Response): Promise<void> {
    try {
      const categories = await Service.distinct('category', { isActive: true });

      sendSuccess(res, 'Service categories retrieved successfully', categories);
    } catch (error) {
      console.error('Get service categories error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createService(req: Request, res: Response): Promise<void> {
    try {
      const serviceData = req.body;

      const service = await Service.create(serviceData);

      sendSuccess(res, 'Service created successfully', service, 201);
    } catch (error) {
      console.error('Create service error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateService(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const service = await Service.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!service) {
        sendNotFound(res, 'Service not found');
        return;
      }

      sendSuccess(res, 'Service updated successfully', service);
    } catch (error) {
      console.error('Update service error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteService(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const service = await Service.findByIdAndDelete(id);

      if (!service) {
        sendNotFound(res, 'Service not found');
        return;
      }

      sendSuccess(res, 'Service deleted successfully');
    } catch (error) {
      console.error('Delete service error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async uploadServiceImage(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      if (!req.file) {
        sendError(res, 'No image file provided', undefined, 400);
        return;
      }

      // Find the service
      const service = await Service.findById(id);
      if (!service) {
        sendNotFound(res, 'Service not found');
        return;
      }

      // Upload to Cloudinary using the unified uploader
      const imageUrl = await uploadSingleFile(req, 'serviceImage');

      if (!imageUrl) {
        sendError(res, 'Failed to upload image', undefined, 500);
        return;
      }

      // Update service with new image
      const updatedImages = service.images || [];
      updatedImages.push(imageUrl);

      service.images = updatedImages;
      // Set the first image as the main image if not set
      if (!service.image) {
        service.image = imageUrl;
      }

      await service.save();

      sendSuccess(res, 'Service image uploaded successfully', {
        imageUrl: imageUrl,
        service: service
      });
    } catch (error) {
      console.error('Upload service image error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteServiceImage(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { imageUrl } = req.body;

      if (!imageUrl) {
        sendError(res, 'Image URL is required', undefined, 400);
        return;
      }

      // Find the service
      const service = await Service.findById(id);
      if (!service) {
        sendNotFound(res, 'Service not found');
        return;
      }

      // Remove image from service
      if (service.images) {
        service.images = service.images.filter(img => img !== imageUrl);
      }

      // If this was the main image, set a new main image
      if (service.image === imageUrl) {
        service.image = service.images && service.images.length > 0 ? service.images[0] : '';
      }

      await service.save();

      sendSuccess(res, 'Service image deleted successfully', service);
    } catch (error) {
      console.error('Delete service image error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
