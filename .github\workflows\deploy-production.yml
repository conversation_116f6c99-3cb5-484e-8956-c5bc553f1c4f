name: Deploy to Production

on:
  push:
    branches: [main]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install backend dependencies
        run: npm ci
        
      - name: Install frontend dependencies
        run: |
          cd frontendv2
          npm ci
          
      - name: Run backend linting
        run: npm run lint
        
      - name: Run frontend linting
        run: |
          cd frontendv2
          npm run lint
          
      - name: Run type checking
        run: |
          npm run type-check
          cd frontendv2
          npm run type-check
          
      - name: Build backend
        run: npm run build:production
        
      - name: Build frontend
        run: |
          cd frontendv2
          npm run build:production

  build-and-push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
            
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-and-push
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
          
      - name: Add server to known hosts
        run: |
          ssh-keyscan -H ${{ secrets.SERVER_HOST }} >> ~/.ssh/known_hosts
          
      - name: Create deployment directory
        run: |
          ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} "mkdir -p /opt/microlocs"
          
      - name: Copy deployment files
        run: |
          scp docker-compose.production.yml ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/opt/microlocs/
          scp -r nginx/ ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/opt/microlocs/
          scp -r scripts/ ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/opt/microlocs/
          
      - name: Create environment file
        run: |
          ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} "cat > /opt/microlocs/.env << 'EOF'
          NODE_ENV=production
          MONGODB_URI=${{ secrets.MONGODB_URI }}
          JWT_SECRET=${{ secrets.JWT_SECRET }}
          JWT_REFRESH_SECRET=${{ secrets.JWT_REFRESH_SECRET }}
          EMAIL_USER=${{ secrets.EMAIL_USER }}
          EMAIL_PASS=${{ secrets.EMAIL_PASS }}
          ADMIN_EMAIL=${{ secrets.ADMIN_EMAIL }}
          ADMIN_PASSWORD=${{ secrets.ADMIN_PASSWORD }}
          MONGO_ROOT_USERNAME=${{ secrets.MONGO_ROOT_USERNAME }}
          MONGO_ROOT_PASSWORD=${{ secrets.MONGO_ROOT_PASSWORD }}
          MONGO_DB_NAME=${{ secrets.MONGO_DB_NAME }}
          REDIS_PASSWORD=${{ secrets.REDIS_PASSWORD }}
          GRAFANA_PASSWORD=${{ secrets.GRAFANA_PASSWORD }}
          EOF"
          
      - name: Pull latest image and deploy
        run: |
          ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} "
            cd /opt/microlocs
            docker-compose -f docker-compose.production.yml pull
            docker-compose -f docker-compose.production.yml up -d
            docker system prune -f
          "
          
      - name: Wait for deployment
        run: |
          ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} "
            timeout 300 bash -c 'until curl -f http://localhost:3000/api/health; do sleep 5; done'
          "
          
      - name: Run database migrations
        run: |
          ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} "
            cd /opt/microlocs
            docker-compose -f docker-compose.production.yml exec -T app npm run seed:production
          "
          
      - name: Verify deployment
        run: |
          ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} "
            curl -f https://${{ secrets.DOMAIN_NAME }}/api/health
          "

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy]
    if: always()
    
    steps:
      - name: Notify success
        if: needs.deploy.result == 'success'
        run: |
          echo "✅ Deployment successful!"
          # Add notification logic here (Slack, Discord, email, etc.)
          
      - name: Notify failure
        if: needs.deploy.result == 'failure'
        run: |
          echo "❌ Deployment failed!"
          # Add notification logic here (Slack, Discord, email, etc.)
