// User and Appointment types
export interface User {
  id: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone: string;
  role: 'user' | 'admin';
  createdAt: string;
  lastLogin?: string;
}

export interface AddOn {
  id: string;
  name: string;
  price: number;
  duration: number;
}

export interface CustomerInfo {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
}

export interface Appointment {
  id: string;
  userId: string;
  serviceId: string;
  serviceName: string;
  servicePrice: number;
  addOns: AddOn[];
  date: string;
  time: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  customerInfo: CustomerInfo;
  totalPrice: number;
  depositPaid: boolean;
  depositAmount: number;
  balanceDue: number;
  createdAt: string;
  notes?: string;
}

// Note: Demo data and localStorage functions removed - now using API data only
// These functions are deprecated and should not be used
export const getUsers = (): User[] => {
  console.warn('getUsers() is deprecated - use API calls instead');
  return [];
};

export const getAppointments = (): Appointment[] => {
  console.warn('getAppointments() is deprecated - use API calls instead');
  return [];
};

// Note: updateAppointment and deleteAppointment functions removed - use API calls instead

export const logout = () => {
  console.warn('logout() is deprecated - use API calls instead');
  localStorage.removeItem('currentUser');
};

export const getCurrentUser = (): User | null => {
  console.warn('getCurrentUser() is deprecated - use API calls instead');
  const user = localStorage.getItem('currentUser');
  return user ? JSON.parse(user) : null;
};

export const isAuthenticated = (): boolean => {
  console.warn('isAuthenticated() is deprecated - use API calls instead');
  return getCurrentUser() !== null;
};

export const isAdmin = (): boolean => {
  console.warn('isAdmin() is deprecated - use API calls instead');
  const user = getCurrentUser();
  return user?.role === 'admin';
};
