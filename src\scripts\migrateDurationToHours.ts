import mongoose from 'mongoose';
import { Service } from '../models/Service';
import { ServiceAddon } from '../models/ServiceAddon';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Migration script to convert service durations from minutes to hours
 * This script will:
 * 1. Convert all Service durations from minutes to hours (divide by 60)
 * 2. Convert all ServiceAddon durations from minutes to hours (divide by 60)
 * 3. Ensure all values are properly rounded to reasonable precision
 */

async function migrateDurationToHours() {
  try {
    console.log('🚀 Starting duration migration from minutes to hours...');

    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/microlocsbackend';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Migrate Services
    console.log('\n📋 Migrating Service durations...');
    const services = await Service.find({});
    console.log(`Found ${services.length} services to migrate`);

    let servicesUpdated = 0;
    for (const service of services) {
      const originalDuration = service.duration;
      
      // Convert minutes to hours and round to 2 decimal places
      const newDuration = Math.round((originalDuration / 60) * 100) / 100;
      
      // Update the service
      await Service.findByIdAndUpdate(service._id, { duration: newDuration });
      
      console.log(`  ✓ ${service.name}: ${originalDuration} min → ${newDuration} hours`);
      servicesUpdated++;
    }

    // Migrate ServiceAddons
    console.log('\n🔧 Migrating ServiceAddon durations...');
    const serviceAddons = await ServiceAddon.find({});
    console.log(`Found ${serviceAddons.length} service addons to migrate`);

    let addonsUpdated = 0;
    for (const addon of serviceAddons) {
      const originalDuration = addon.duration;
      
      // Convert minutes to hours and round to 2 decimal places
      const newDuration = Math.round((originalDuration / 60) * 100) / 100;
      
      // Update the addon
      await ServiceAddon.findByIdAndUpdate(addon._id, { duration: newDuration });
      
      console.log(`  ✓ ${addon.name}: ${originalDuration} min → ${newDuration} hours`);
      addonsUpdated++;
    }

    console.log('\n✅ Migration completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   - Services updated: ${servicesUpdated}`);
    console.log(`   - Service addons updated: ${addonsUpdated}`);
    console.log(`   - Total records updated: ${servicesUpdated + addonsUpdated}`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  migrateDurationToHours();
}

export { migrateDurationToHours };
