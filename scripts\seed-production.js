#!/usr/bin/env node

/**
 * Production Database Seeding Script
 * This script seeds the database with essential production data
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env.production') });

// Import models
const { User } = require('../dist/models/User');
const { Service } = require('../dist/models/Service');
const { Branding } = require('../dist/models/Branding');
const { SiteSettings } = require('../dist/models/SiteSettings');
const { PaymentSettings } = require('../dist/models/PaymentSettings');
const { ThemeSettings } = require('../dist/models/ThemeSettings');
const { SEO } = require('../dist/models/SEO');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    log('✓ Connected to MongoDB', 'green');
  } catch (error) {
    log('✗ Failed to connect to MongoDB', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

async function createAdminUser() {
  try {
    const existingAdmin = await User.findOne({ role: 'admin' });
    
    if (existingAdmin) {
      log('✓ Admin user already exists', 'yellow');
      return;
    }

    const hashedPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD, 12);
    
    const adminUser = new User({
      firstName: 'Admin',
      lastName: 'User',
      email: process.env.ADMIN_EMAIL,
      password: hashedPassword,
      role: 'admin',
      isVerified: true
    });

    await adminUser.save();
    log('✓ Admin user created successfully', 'green');
  } catch (error) {
    log('✗ Failed to create admin user', 'red');
    log(error.message, 'red');
  }
}

async function createDefaultServices() {
  try {
    const existingServices = await Service.countDocuments();
    
    if (existingServices > 0) {
      log('✓ Services already exist', 'yellow');
      return;
    }

    const defaultServices = [
      {
        name: 'Consultation',
        description: 'Initial consultation to discuss your hair goals and needs',
        price: 0,
        duration: 30,
        category: 'consultation',
        isActive: true
      },
      {
        name: 'Micro Locs Installation',
        description: 'Professional micro locs installation service',
        price: 300,
        duration: 480, // 8 hours
        category: 'installation',
        isActive: true
      },
      {
        name: 'Loc Maintenance',
        description: 'Regular maintenance for existing locs',
        price: 150,
        duration: 180, // 3 hours
        category: 'maintenance',
        isActive: true
      },
      {
        name: 'Loc Retwist',
        description: 'Root maintenance and retwisting service',
        price: 100,
        duration: 120, // 2 hours
        category: 'maintenance',
        isActive: true
      }
    ];

    await Service.insertMany(defaultServices);
    log('✓ Default services created successfully', 'green');
  } catch (error) {
    log('✗ Failed to create default services', 'red');
    log(error.message, 'red');
  }
}

async function createDefaultSettings() {
  try {
    // Site Settings
    const existingSiteSettings = await SiteSettings.findOne();
    if (!existingSiteSettings) {
      const siteSettings = new SiteSettings({
        general: {
          siteName: 'MicroLocs Studio',
          siteDescription: 'Professional hair care and loc services',
          contactEmail: process.env.ADMIN_EMAIL,
          contactPhone: '(*************',
          address: '123 Beauty Street, City, State 12345',
          timezone: 'America/New_York',
          currency: 'USD',
          language: 'en'
        },
        features: {
          enableAppointments: true,
          enableEcommerce: false,
          enableReviews: true,
          enableLoyaltyProgram: false,
          enableGiftCards: false,
          enableWaitlist: true,
          enableReferrals: false
        },
        notifications: {
          emailNotifications: true,
          smsNotifications: false,
          pushNotifications: false,
          appointmentReminders: true,
          marketingEmails: false
        }
      });
      await siteSettings.save();
      log('✓ Site settings created', 'green');
    }

    // Payment Settings
    const existingPaymentSettings = await PaymentSettings.findOne();
    if (!existingPaymentSettings) {
      const paymentSettings = new PaymentSettings({
        methods: {
          cashApp: {
            enabled: true,
            handle: '$MicroLocsStudio'
          },
          zelle: {
            enabled: true,
            email: process.env.ADMIN_EMAIL,
            phone: '(*************'
          },
          venmo: {
            enabled: false,
            handle: ''
          },
          paypal: {
            enabled: false,
            email: ''
          }
        },
        policies: {
          requireDeposit: true,
          depositAmount: 50,
          depositPercentage: 25,
          cancellationPolicy: '24 hours notice required for cancellations',
          refundPolicy: 'Deposits are non-refundable unless cancelled 48 hours in advance'
        }
      });
      await paymentSettings.save();
      log('✓ Payment settings created', 'green');
    }

    // Theme Settings
    const existingThemeSettings = await ThemeSettings.findOne();
    if (!existingThemeSettings) {
      const themeSettings = new ThemeSettings({
        colors: {
          primary: '#8B4513',
          secondary: '#D2691E',
          accent: '#CD853F',
          background: '#FFFFFF',
          text: '#333333'
        },
        fonts: {
          heading: 'Inter, sans-serif',
          body: 'Inter, sans-serif'
        },
        layout: {
          headerStyle: 'modern',
          footerStyle: 'simple',
          sidebarPosition: 'left'
        },
        isActive: true
      });
      await themeSettings.save();
      log('✓ Theme settings created', 'green');
    }

    // SEO Settings
    const existingSEOSettings = await SEO.findOne();
    if (!existingSEOSettings) {
      const seoSettings = new SEO({
        meta: {
          title: 'MicroLocs Studio - Professional Hair Care Services',
          description: 'Professional micro locs installation and maintenance services. Expert hair care for natural hair and locs.',
          keywords: 'micro locs, hair care, natural hair, locs, dreadlocks, hair salon',
          author: 'MicroLocs Studio'
        },
        social: {
          ogTitle: 'MicroLocs Studio - Professional Hair Care',
          ogDescription: 'Expert micro locs and natural hair care services',
          ogImage: '',
          twitterCard: 'summary_large_image'
        },
        analytics: {
          googleAnalyticsId: '',
          facebookPixelId: '',
          googleTagManagerId: ''
        }
      });
      await seoSettings.save();
      log('✓ SEO settings created', 'green');
    }

  } catch (error) {
    log('✗ Failed to create default settings', 'red');
    log(error.message, 'red');
  }
}

async function createDefaultBranding() {
  try {
    const existingBranding = await Branding.findOne();
    
    if (existingBranding) {
      log('✓ Branding data already exists', 'yellow');
      return;
    }

    // Run the existing branding population script
    const { execSync } = require('child_process');
    execSync('node scripts/populateBrandingData.js', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    log('✓ Default branding created successfully', 'green');
  } catch (error) {
    log('✗ Failed to create default branding', 'red');
    log(error.message, 'red');
  }
}

async function main() {
  log('🌱 Starting production database seeding...', 'bright');
  log('==========================================', 'bright');
  
  try {
    await connectDatabase();
    await createAdminUser();
    await createDefaultServices();
    await createDefaultSettings();
    await createDefaultBranding();
    
    log('==========================================', 'bright');
    log('🎉 Production database seeding completed!', 'green');
    log('==========================================', 'bright');
    
  } catch (error) {
    log('==========================================', 'bright');
    log('💥 Production database seeding failed!', 'red');
    log(`Error: ${error.message}`, 'red');
    log('==========================================', 'bright');
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }
}

// Run the seeding process
main();
