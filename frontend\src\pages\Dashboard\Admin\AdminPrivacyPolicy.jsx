import { useState, useEffect } from 'react'
import { FiShield, FiSave, FiEdit, FiEye } from 'react-icons/fi'
import { useToast } from '../../../contexts/ToastContext'
import { brandingService } from '../../../services'

const AdminPrivacyPolicy = ({ branding }) => {
  const { showSuccess, showError } = useToast()
  const [content, setContent] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [lastUpdated, setLastUpdated] = useState(null)

  // Load privacy policy content
  useEffect(() => {
    const loadContent = async () => {
      setIsLoading(true)
      try {
        // Get privacy policy from branding data
        if (branding?.legal?.privacyPolicy) {
          // Handle both string and object formats
          const privacyPolicy = branding.legal.privacyPolicy
          if (typeof privacyPolicy === 'string') {
            setContent(privacyPolicy)
          } else if (privacyPolicy && typeof privacyPolicy === 'object' && privacyPolicy.content) {
            setContent(privacyPolicy.content)
          } else {
            setContent('')
          }
          setLastUpdated(branding.legal.privacyPolicyUpdated)
        } else {
          // Set default content if none exists
          setContent(`# Privacy Policy

## Information We Collect

We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us.

## How We Use Your Information

We use the information we collect to:
- Provide, maintain, and improve our services
- Process transactions and send related information
- Send you technical notices and support messages
- Communicate with you about products, services, and events

## Information Sharing

We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.

## Data Security

We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.

## Contact Us

If you have any questions about this Privacy Policy, please contact us at [<EMAIL>].

*Last updated: ${new Date().toLocaleDateString()}*`)
        }
      } catch (error) {
        console.error('Error loading privacy policy:', error)
        showError('Failed to load privacy policy')
      } finally {
        setIsLoading(false)
      }
    }

    loadContent()
  }, [branding, showError])

  // Save privacy policy content
  const handleSave = async () => {
    if (!content.trim()) {
      showError('Privacy policy content cannot be empty')
      return
    }

    setIsSaving(true)
    try {
      // Update branding with new privacy policy
      const updatedBranding = {
        ...branding,
        legal: {
          ...branding?.legal,
          privacyPolicy: content,
          privacyPolicyUpdated: new Date().toISOString()
        }
      }

      await brandingService.updateBranding(updatedBranding)
      setLastUpdated(new Date().toISOString())
      setIsEditing(false)
      showSuccess('Privacy policy updated successfully!')
    } catch (error) {
      console.error('Error saving privacy policy:', error)
      showError('Failed to save privacy policy')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 flex items-center">
            <FiShield className="w-8 h-8 mr-3 text-blue-600" />
            Privacy Policy Management
          </h2>
          <p className="text-gray-600 mt-1">
            Manage your website's privacy policy content
          </p>
          {lastUpdated && (
            <p className="text-sm text-gray-500 mt-1">
              Last updated: {new Date(lastUpdated).toLocaleString()}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-3">
          {isEditing ? (
            <>
              <button
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                ) : (
                  <FiSave className="w-4 h-4 mr-2" />
                )}
                {isSaving ? 'Saving...' : 'Save Changes'}
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
            >
              <FiEdit className="w-4 h-4 mr-2" />
              Edit Privacy Policy
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        {isEditing ? (
          <div className="p-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Privacy Policy Content
            </label>
            <div className="border border-gray-300 rounded-xl overflow-hidden">
              <div className="bg-gray-50 px-4 py-2 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <span>Rich Text Editor</span>
                  <span>•</span>
                  <span>{typeof content === 'string' ? content.length : 0} characters</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => setContent(content + '\n\n## New Section\n\nContent here...')}
                    className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                  >
                    Add Section
                  </button>
                </div>
              </div>
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="w-full h-96 px-4 py-3 border-0 focus:ring-0 focus:outline-none resize-none text-sm leading-relaxed"
                placeholder="Enter your privacy policy content here..."
                style={{ fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace' }}
              />
            </div>
            <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
              <div className="flex items-center space-x-4">
                <span>💡 Use clear, simple language</span>
                <span>•</span>
                <span>📝 Include all essential privacy sections</span>
              </div>
              <button
                type="button"
                onClick={() => {
                  const template = `# Privacy Policy

## 1. Information We Collect
We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us.

## 2. How We Use Your Information
We use the information we collect to:
- Provide, maintain, and improve our services
- Process transactions and send related information
- Send you technical notices and support messages
- Communicate with you about products, services, and events

## 3. Information Sharing
We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.

## 4. Data Security
We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.

## 5. Your Rights
You have the right to:
- Access your personal information
- Correct inaccurate data
- Request deletion of your data
- Opt-out of communications

## 6. Cookies and Tracking
We use cookies and similar technologies to enhance your experience and analyze usage patterns.

## 7. Contact Us
If you have any questions about this Privacy Policy, please contact us at [<EMAIL>].

*Last updated: ${new Date().toLocaleDateString()}*`
                  setContent(template)
                }}
                className="text-blue-600 hover:text-blue-700 underline"
              >
                Load Template
              </button>
            </div>
          </div>
        ) : (
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <FiEye className="w-5 h-5 mr-2" />
                Preview
              </h3>
              <span className="text-sm text-gray-500">
                {typeof content === 'string' ? content.length : 0} characters
              </span>
            </div>
            <div className="prose max-w-none">
              <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                {typeof content === 'string' ? content : 'No privacy policy content available. Click "Edit Privacy Policy" to add content.'}
              </div>
            </div>
          </div>
        )}
      </div>


    </div>
  )
}

export default AdminPrivacyPolicy
