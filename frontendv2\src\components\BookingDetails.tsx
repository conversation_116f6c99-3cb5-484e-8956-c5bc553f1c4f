import { useState, useEffect, useCallback } from 'react';

interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
}

interface BookingState {
  selectedService: any;
  selectedAddOns: AddOnService[];
  selectedDate: string;
  selectedTime: string;
  step: 'services' | 'datetime' | 'details' | 'checkout';
}

interface BookingDetailsProps {
  booking: BookingState;
  onBack: () => void;
  onContinue: (customerInfo: any) => void;
}

export default function BookingDetails({ booking, onBack, onContinue }: BookingDetailsProps) {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    userId: ''
  });

  const [emailExists, setEmailExists] = useState<boolean | null>(null);
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);

  // Debounced email checking
  const checkEmailExists = useCallback(async (email: string) => {
    if (!email || !email.includes('@')) return;

    setIsCheckingEmail(true);
    try {
      const response = await fetch(`http://localhost:3000/api/v2/auth/check-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();
      setEmailExists(data.exists);

      if (data.exists && data.user) {
        // Pre-fill form with existing user data
        setFormData(prev => ({
          ...prev,
          firstName: data.user.firstName || prev.firstName,
          lastName: data.user.lastName || prev.lastName,
          phone: data.user.phone || prev.phone,
          userId: data.user.id // Store the user ID for later use
        }));
      }
    } catch (error) {
      console.error('Error checking email:', error);
      setEmailExists(null);
    } finally {
      setIsCheckingEmail(false);
    }
  }, []);

  // Debounce email checking
  useEffect(() => {
    const timer = setTimeout(() => {
      if (formData.email) {
        checkEmailExists(formData.email);
      } else {
        setEmailExists(null);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [formData.email, checkEmailExists]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    // Auto-add + prefix for phone numbers
    if (e.target.name === 'phone') {
      // Remove any non-digit characters except +
      value = value.replace(/[^\d+]/g, '');

      // Ensure it starts with + if user enters digits
      if (value.length > 0 && !value.startsWith('+')) {
        value = '+' + value;
      }
    }

    setFormData({
      ...formData,
      [e.target.name]: value
    });
  };

  const handleSubmit = () => {
    if (!formData.firstName || !formData.lastName || !formData.email) {
      alert('Please fill in all required fields.');
      return;
    }

    // Validate phone number starts with +
    if (formData.phone && !formData.phone.startsWith('+')) {
      alert('Phone number must start with + followed by country code (e.g., +1234567890)');
      return;
    }

    onContinue(formData);
  };

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <div className="auth-links">
            <a href="#" className="auth-link">SIGN UP</a>
            <a href="#" className="auth-link">LOG IN</a>
          </div>
        </div>
      </header>

      <main className="main-content">
        <div className="booking-details">
          <div className="booking-header">
            <button className="back-button" onClick={onBack}>
              ← Back to Date & Time
            </button>
            <h1>Your Information</h1>
          </div>

          {/* Appointment Summary */}
          <div className="appointment-summary">
            <div className="appointment-card">
              <div className="appointment-details">
                <h4>{booking.selectedService?.name}</h4>
                <p className="appointment-price">${booking.selectedService?.price}</p>
                <p className="appointment-datetime">
                  {booking.selectedDate ? new Date(booking.selectedDate).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'Date not selected'} at {booking.selectedTime} CDT
                </p>
                
                {booking.selectedAddOns.map(addOn => (
                  <div key={addOn.id} className="addon-line">
                    + {addOn.name}, {addOn.duration} minutes @ ${addOn.price}
                  </div>
                ))}
                
                <p className="appointment-note">*EXISTING CLIENTS ONLY*</p>
              </div>
              <button className="close-button">×</button>
            </div>
          </div>

          {/* Customer Information Form */}
          <div className="customer-info">
            <h3>YOUR INFORMATION</h3>
            
            <div className="form-group">
              <label htmlFor="firstName">FIRST NAME*</label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="lastName">LAST NAME*</label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="phone">PHONE*</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                placeholder="Enter phone number (e.g., +1234567890)"
                required
              />
              <small style={{ color: '#666', fontSize: '0.8rem' }}>
                Please include country code (e.g., +1 for US, +44 for UK)
              </small>
            </div>

            <div className="form-group">
              <label htmlFor="email">EMAIL*</label>
              <div className="email-input-wrapper">
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="Enter your email address"
                  required
                />
                {isCheckingEmail && <span className="email-checking">Checking...</span>}
                {emailExists === true && <span className="email-exists">✓ Account found</span>}
                {emailExists === false && <span className="email-new">New account</span>}
              </div>
            </div>



            <button className="continue-button" onClick={handleSubmit}>
              CONTINUE TO PAYMENT
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
