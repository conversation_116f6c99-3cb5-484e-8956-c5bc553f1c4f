import { Request, Response } from 'express';
import { Review, Product, Service } from '../models';
import { ReviewService } from '../services/reviewService';
import { sendSuccess, sendError, sendNotFound, sendCreated } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class ReviewController {
  static async getProductReviews(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

      // Verify product exists
      const product = await Product.findById(id);
      if (!product) {
        sendNotFound(res, 'Product not found');
        return;
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      // Sort
      const sort: any = {};
      sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

      const [reviews, total] = await Promise.all([
        Review.find({ product: id })
          .populate('user', 'name')
          .sort(sort)
          .skip(skip)
          .limit(limitNum),
        Review.countDocuments({ product: id })
      ]);

      const totalPages = Math.ceil(total / limitNum);

      // Calculate rating statistics
      const ratingStats = await Review.aggregate([
        { $match: { product: product._id } },
        {
          $group: {
            _id: '$rating',
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: -1 } }
      ]);

      const averageRating = await Review.aggregate([
        { $match: { product: product._id } },
        {
          $group: {
            _id: null,
            average: { $avg: '$rating' },
            total: { $sum: 1 }
          }
        }
      ]);

      sendSuccess(res, 'Product reviews retrieved successfully', {
        reviews,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        },
        statistics: {
          averageRating: averageRating[0]?.average || 0,
          totalReviews: averageRating[0]?.total || 0,
          ratingDistribution: ratingStats
        }
      });
    } catch (error) {
      console.error('Get product reviews error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createReview(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { product, service, rating, title, comment } = req.body;

      // Verify product or service exists
      if (product) {
        const productExists = await Product.findById(product);
        if (!productExists) {
          sendNotFound(res, 'Product not found');
          return;
        }
      } else if (service) {
        const serviceExists = await Service.findById(service);
        if (!serviceExists) {
          sendNotFound(res, 'Service not found');
          return;
        }
      } else {
        sendError(res, 'Either product or service must be specified');
        return;
      }

      const review = await ReviewService.createReview({
        user: req.user._id,
        product,
        service,
        rating,
        title,
        comment
      });

      sendCreated(res, 'Review submitted successfully and is pending approval', review);
    } catch (error) {
      console.error('Create review error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateReview(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;
      const { rating, comment } = req.body;

      const review = await Review.findOne({
        _id: id,
        user: req.user._id
      });

      if (!review) {
        sendNotFound(res, 'Review not found');
        return;
      }

      review.rating = rating;
      review.comment = comment;
      await review.save();

      await review.populate('user', 'name');

      // Update product rating
      const reviews = await Review.find({ product: review.product });
      const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = totalRating / reviews.length;

      await Product.findByIdAndUpdate(review.product, {
        rating: Math.round(averageRating * 10) / 10
      });

      sendSuccess(res, 'Review updated successfully', review);
    } catch (error) {
      console.error('Update review error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteReview(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;

      const review = await Review.findOne({
        _id: id,
        user: req.user._id
      });

      if (!review) {
        sendNotFound(res, 'Review not found');
        return;
      }

      const productId = review.product;
      await Review.findByIdAndDelete(id);

      // Update product rating and review count
      const reviews = await Review.find({ product: productId });
      const averageRating = reviews.length > 0 
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
        : 0;

      await Product.findByIdAndUpdate(productId, {
        rating: Math.round(averageRating * 10) / 10,
        reviewCount: reviews.length
      });

      sendSuccess(res, 'Review deleted successfully');
    } catch (error) {
      console.error('Delete review error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // New enhanced methods
  static async getReviews(req: Request, res: Response): Promise<void> {
    try {
      const { product, service, page, limit } = req.query;

      const result = await ReviewService.getReviews({
        product: product as string,
        service: service as string,
        page: page ? parseInt(page as string) : undefined,
        limit: limit ? parseInt(limit as string) : undefined
      });

      sendSuccess(res, 'Reviews retrieved successfully', result);
    } catch (error) {
      console.error('Get reviews error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getReviewStats(req: Request, res: Response): Promise<void> {
    try {
      const { product, service } = req.query;

      const stats = await ReviewService.getReviewStats(
        product as string,
        service as string
      );

      sendSuccess(res, 'Review statistics retrieved successfully', stats);
    } catch (error) {
      console.error('Get review stats error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getUserReviews(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { page, limit } = req.query;

      const result = await ReviewService.getUserReviews(
        req.user._id,
        page ? parseInt(page as string) : undefined,
        limit ? parseInt(limit as string) : undefined
      );

      sendSuccess(res, 'User reviews retrieved successfully', result);
    } catch (error) {
      console.error('Get user reviews error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Admin methods
  static async getAllReviews(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user || req.user.role !== 'admin') {
        sendError(res, 'Admin access required', undefined, 403);
        return;
      }

      const { status, page, limit, search } = req.query;

      const result = await ReviewService.getAllReviews({
        status: status as string,
        page: page ? parseInt(page as string) : undefined,
        limit: limit ? parseInt(limit as string) : undefined,
        search: search as string
      });

      sendSuccess(res, 'All reviews retrieved successfully', result);
    } catch (error) {
      console.error('Get all reviews error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateReviewStatus(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user || req.user.role !== 'admin') {
        sendError(res, 'Admin access required', undefined, 403);
        return;
      }

      const { id } = req.params;
      const { status } = req.body;

      if (!['approved', 'rejected'].includes(status)) {
        sendError(res, 'Invalid status. Must be "approved" or "rejected"', undefined, 400);
        return;
      }

      const review = await ReviewService.updateReviewStatus(id, status);

      if (!review) {
        sendError(res, 'Review not found', undefined, 404);
        return;
      }

      sendSuccess(res, `Review ${status} successfully`, review);
    } catch (error) {
      console.error('Update review status error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async adminDeleteReview(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user || req.user.role !== 'admin') {
        sendError(res, 'Admin access required', undefined, 403);
        return;
      }

      const { id } = req.params;

      const deleted = await ReviewService.deleteReview(id);

      if (!deleted) {
        sendError(res, 'Review not found', undefined, 404);
        return;
      }

      sendSuccess(res, 'Review deleted successfully');
    } catch (error) {
      console.error('Admin delete review error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
