# Goldie Locs By Tina - Professional Hair Care Website

A modern, responsive single-page application for professional locs and natural hair care services.

## 🌟 Features

- **Single Page Application** - No routing, fast navigation with state management
- **Responsive Design** - Mobile-first approach using Tailwind CSS v4
- **Professional Services** - Consultation booking, service catalog, and shop
- **Modern UI/UX** - Clean, professional design with smooth animations
- **Optimized Performance** - Built with Vite for fast loading and development

## 🛠️ Tech Stack

- **React 19** - Latest React with modern hooks
- **Vite 6** - Fast build tool and development server
- **Tailwind CSS v4** - Utility-first CSS framework
- **React Icons** - Beautiful icon library
- **ESLint** - Code linting and formatting

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd goldie-locs-by-tina
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```
Update the `.env` file with your API base URL:
```env
VITE_API_BASE_URL=http://*************:3000/api
```

4. Start development server:
```bash
npm run dev
```

5. Open your browser to `http://localhost:5173`

## 📦 Build & Deployment

### Build for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

### Lint Code
```bash
npm run lint
```

## 🌐 Deployment Options

### Netlify
1. Connect your repository to Netlify
2. Build settings are configured in `netlify.toml`
3. Deploy automatically on push to main branch

### Vercel
1. Connect your repository to Vercel
2. Configuration is in `vercel.json`
3. Automatic deployments on push

### GitHub Pages
1. Enable GitHub Actions in your repository
2. Workflow is configured in `.github/workflows/deploy.yml`
3. Deploys to GitHub Pages on push to main

### Manual Deployment
1. Run `npm run build`
2. Upload the `dist/` folder to your web server
3. Configure your server to serve `index.html` for all routes

## 📁 Project Structure

```
src/
├── components/
│   └── Layout/
│       ├── Navbar.jsx
│       └── Footer.jsx
├── pages/
│   ├── Home.jsx
│   ├── Consultation.jsx
│   ├── Services.jsx
│   ├── Shop/
│   │   ├── Shop.jsx
│   │   ├── ProductDetail.jsx
│   │   └── Cart.jsx
│   └── Auth/
│       ├── Login.jsx
│       └── SignUp.jsx
├── App.jsx
└── main.jsx
```

## 🎨 Customization

### Colors
The application uses an amber/gold color scheme. To customize:
- Primary: `amber-600`
- Secondary: `gray-900`
- Accent: `orange-100`

### Content
Update content in the respective page components:
- Services: `src/pages/Services.jsx`
- Products: `src/pages/Shop/Shop.jsx`
- Contact info: `src/components/Layout/Footer.jsx`

## 🔧 Environment Variables

Create `.env` file from the example:
```bash
cp .env.example .env
```

Required environment variables:
```env
# API Configuration
VITE_API_BASE_URL=http://*************:3000/api

# App Configuration
VITE_APP_TITLE="Goldie Locs By Tina"
VITE_APP_DESCRIPTION="Professional locs and natural hair care services"
VITE_APP_URL="https://yourdomain.com"
```

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is private and proprietary.

## 📞 Support

For support, contact: <EMAIL>
