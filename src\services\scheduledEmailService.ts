import cron from 'node-cron';
import { User, Appointment } from '../models';
import { emailService } from './emailService';

export class ScheduledEmailService {
  private static instance: ScheduledEmailService;
  private isInitialized = false;

  static getInstance(): ScheduledEmailService {
    if (!ScheduledEmailService.instance) {
      ScheduledEmailService.instance = new ScheduledEmailService();
    }
    return ScheduledEmailService.instance;
  }

  // Initialize all scheduled email jobs
  init(): void {
    if (this.isInitialized) {
      console.log('Scheduled email service already initialized');
      return;
    }

    console.log('Initializing scheduled email service...');

    // Daily appointment reminders (runs at 9 AM every day)
    cron.schedule('0 9 * * *', () => {
      this.sendAppointmentReminders();
    });

    // Weekly promotional emails (runs every Monday at 10 AM)
    cron.schedule('0 10 * * 1', () => {
      this.sendWeeklyPromotions();
    });

    // Monthly birthday emails (runs on 1st of every month at 9 AM)
    cron.schedule('0 9 1 * *', () => {
      this.sendBirthdayEmails();
    });

    // Quarterly customer appreciation emails (runs every 3 months on 1st at 10 AM)
    cron.schedule('0 10 1 */3 *', () => {
      this.sendCustomerAppreciationEmails();
    });

    this.isInitialized = true;
    console.log('Scheduled email service initialized successfully');
  }

  // Send appointment reminders for tomorrow's appointments
  private async sendAppointmentReminders(): Promise<void> {
    try {
      console.log('Sending appointment reminders...');
      
      // Get tomorrow's date
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      
      const dayAfterTomorrow = new Date(tomorrow);
      dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 1);

      // Find all appointments for tomorrow
      const appointments = await Appointment.find({
        date: {
          $gte: tomorrow,
          $lt: dayAfterTomorrow
        },
        status: { $in: ['pending', 'confirmed'] }
      }).populate('user').populate('service');

      console.log(`Found ${appointments.length} appointments for tomorrow`);

      // Send reminder emails
      for (const appointment of appointments) {
        try {
          if (appointment.user && (appointment.user as any).email) {
            await emailService.sendAppointmentReminder(appointment.user as any, appointment as any);
            console.log(`Reminder sent to ${(appointment.user as any).email}`);
          }
        } catch (error) {
          console.error(`Failed to send reminder to ${(appointment.user as any)?.email}:`, error);
        }
      }

      console.log('Appointment reminders completed');
    } catch (error) {
      console.error('Error sending appointment reminders:', error);
    }
  }

  // Send weekly promotional emails to active customers
  private async sendWeeklyPromotions(): Promise<void> {
    try {
      console.log('Sending weekly promotional emails...');

      // Get users who have had appointments in the last 3 months
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

      const recentCustomers = await User.aggregate([
        {
          $lookup: {
            from: 'appointments',
            localField: '_id',
            foreignField: 'user',
            as: 'appointments'
          }
        },
        {
          $match: {
            'appointments.date': { $gte: threeMonthsAgo },
            role: 'user'
          }
        },
        {
          $project: {
            firstName: 1,
            lastName: 1,
            name: 1,
            email: 1,
            lastAppointment: { $max: '$appointments.date' }
          }
        }
      ]);

      console.log(`Found ${recentCustomers.length} recent customers for promotions`);

      // Define weekly promotions (rotate through different offers)
      const promotions = [
        {
          title: 'Midweek Makeover Special',
          description: 'Book any service Tuesday-Thursday and save big!',
          discount: '20%',
          validUntil: this.getDateString(7), // Valid for 1 week
          code: 'MIDWEEK20'
        },
        {
          title: 'Pamper Package Deal',
          description: 'Combine any 2 services and get the second one discounted!',
          discount: '30%',
          validUntil: this.getDateString(7),
          code: 'PAMPER30'
        },
        {
          title: 'Bring a Friend Special',
          description: 'Book together with a friend and both save!',
          discount: '25%',
          validUntil: this.getDateString(10),
          code: 'FRIEND25'
        }
      ];

      // Rotate promotion based on week of year
      const weekOfYear = Math.floor((Date.now() - new Date(new Date().getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000));
      const currentPromotion = promotions[weekOfYear % promotions.length];

      // Send promotional emails
      for (const customer of recentCustomers) {
        try {
          await emailService.sendPromotionalEmail(customer, currentPromotion);
          console.log(`Promotion sent to ${customer.email}`);
        } catch (error) {
          console.error(`Failed to send promotion to ${customer.email}:`, error);
        }
      }

      console.log('Weekly promotional emails completed');
    } catch (error) {
      console.error('Error sending weekly promotions:', error);
    }
  }

  // Send birthday emails to users whose birthday is this month
  private async sendBirthdayEmails(): Promise<void> {
    try {
      console.log('Sending birthday emails...');

      const currentMonth = new Date().getMonth() + 1; // getMonth() returns 0-11

      // Find users with birthdays this month
      const birthdayUsers = await User.find({
        role: 'user',
        $expr: {
          $eq: [{ $month: '$dateOfBirth' }, currentMonth]
        }
      });

      console.log(`Found ${birthdayUsers.length} users with birthdays this month`);

      // Send birthday emails
      for (const user of birthdayUsers) {
        try {
          await emailService.sendBirthdayEmail(user);
          console.log(`Birthday email sent to ${user.email}`);
        } catch (error) {
          console.error(`Failed to send birthday email to ${user.email}:`, error);
        }
      }

      console.log('Birthday emails completed');
    } catch (error) {
      console.error('Error sending birthday emails:', error);
    }
  }

  // Send quarterly customer appreciation emails
  private async sendCustomerAppreciationEmails(): Promise<void> {
    try {
      console.log('Sending customer appreciation emails...');

      // Get all active customers (users with at least one appointment)
      const activeCustomers = await User.aggregate([
        {
          $lookup: {
            from: 'appointments',
            localField: '_id',
            foreignField: 'user',
            as: 'appointments'
          }
        },
        {
          $match: {
            'appointments.0': { $exists: true }, // Has at least one appointment
            role: 'user'
          }
        },
        {
          $project: {
            firstName: 1,
            lastName: 1,
            name: 1,
            email: 1,
            appointmentCount: { $size: '$appointments' }
          }
        }
      ]);

      console.log(`Found ${activeCustomers.length} active customers for appreciation emails`);

      // Define seasonal appreciation offers
      const currentQuarter = Math.floor((new Date().getMonth()) / 3) + 1;
      const seasonalOffers = {
        1: { title: 'Spring Renewal Special', discount: '15%', theme: 'spring' },
        2: { title: 'Summer Glow Package', discount: '20%', theme: 'summer' },
        3: { title: 'Fall Transformation Deal', discount: '18%', theme: 'fall' },
        4: { title: 'Winter Warmth Special', discount: '25%', theme: 'winter' }
      };

      const currentOffer = seasonalOffers[currentQuarter as keyof typeof seasonalOffers];

      // Send appreciation emails with seasonal offers
      for (const customer of activeCustomers) {
        try {
          await emailService.sendPromotionalEmail(customer, {
            title: `Thank You! ${currentOffer.title}`,
            description: `As a valued customer, enjoy our exclusive ${currentOffer.theme} offer!`,
            discount: currentOffer.discount,
            validUntil: this.getDateString(30), // Valid for 1 month
            code: `${currentOffer.theme.toUpperCase()}${currentOffer.discount.replace('%', '')}`
          });
          console.log(`Appreciation email sent to ${customer.email}`);
        } catch (error) {
          console.error(`Failed to send appreciation email to ${customer.email}:`, error);
        }
      }

      console.log('Customer appreciation emails completed');
    } catch (error) {
      console.error('Error sending customer appreciation emails:', error);
    }
  }

  // Helper method to get date string for offers
  private getDateString(daysFromNow: number): string {
    const date = new Date();
    date.setDate(date.getDate() + daysFromNow);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  // Manual trigger methods for testing
  async triggerAppointmentReminders(): Promise<void> {
    console.log('Manually triggering appointment reminders...');
    await this.sendAppointmentReminders();
  }

  async triggerWeeklyPromotions(): Promise<void> {
    console.log('Manually triggering weekly promotions...');
    await this.sendWeeklyPromotions();
  }

  async triggerBirthdayEmails(): Promise<void> {
    console.log('Manually triggering birthday emails...');
    await this.sendBirthdayEmails();
  }

  async triggerCustomerAppreciation(): Promise<void> {
    console.log('Manually triggering customer appreciation emails...');
    await this.sendCustomerAppreciationEmails();
  }
}

export const scheduledEmailService = ScheduledEmailService.getInstance();
