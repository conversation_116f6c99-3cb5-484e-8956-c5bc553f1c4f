version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: microlocs-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-secure_password}
      MONGO_INITDB_DATABASE: ${MONGO_DB_NAME:-microlocsbackend_production}
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    ports:
      - "27017:27017"
    networks:
      - microlocs-network
    command: mongod --auth

  # Redis Cache (optional)
  redis:
    image: redis:7.2-alpine
    container_name: microlocs-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-secure_redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - microlocs-network

  # Main Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: microlocs-app
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      MONGODB_URI: mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-secure_password}@mongodb:27017/${MONGO_DB_NAME:-microlocsbackend_production}?authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD:-secure_redis_password}@redis:6379
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      ADMIN_EMAIL: ${ADMIN_EMAIL}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD}
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    ports:
      - "3000:3000"
    depends_on:
      - mongodb
      - redis
    networks:
      - microlocs-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy
  nginx:
    image: nginx:1.25-alpine
    container_name: microlocs-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - app_uploads:/var/www/uploads:ro
    depends_on:
      - app
    networks:
      - microlocs-network

  # Backup Service
  backup:
    image: mongo:7.0
    container_name: microlocs-backup
    restart: "no"
    environment:
      MONGO_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-secure_password}
      MONGO_DB_NAME: ${MONGO_DB_NAME:-microlocsbackend_production}
    volumes:
      - mongodb_backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    depends_on:
      - mongodb
    networks:
      - microlocs-network
    command: /bin/bash -c "chmod +x /backup.sh && /backup.sh"

  # Monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: microlocs-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - microlocs-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    container_name: microlocs-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - microlocs-network

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  mongodb_backups:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  microlocs-network:
    driver: bridge
