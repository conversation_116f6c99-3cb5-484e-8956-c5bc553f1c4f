/* Admin Dashboard Styles */

.appointment-management {
  padding: 20px;
  max-width: 100%;
}

.appointment-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.header-content h2 {
  margin: 0 0 5px 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
}

.header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.page-header h1 {
  margin: 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  text-align: center;
}

.loading-container p,
.error-container p {
  color: #6b7280;
  font-size: 16px;
}

.header-actions .btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-outline {
  background-color: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background-color: #f9fafb;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
}

.btn-danger:hover {
  background-color: #dc2626;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 14px;
}

/* Error Message */
.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-close {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
}

/* Appointment Filters */
.appointment-filters {
  background-color: #f9fafb;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
}

.filters-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filter-group label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 5px;
  font-size: 14px;
}

.search-input-container {
  position: relative;
}

.search-input, .filter-select, .filter-input {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
}

.search-input {
  padding-right: 40px;
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #eff6ff;
  padding: 12px 16px;
  border-radius: 8px;
  margin: 15px 0;
}

.selected-count {
  font-weight: 600;
  color: #1d4ed8;
}

.bulk-actions-buttons {
  position: relative;
}

.bulk-actions-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 180px;
}

.bulk-action-btn {
  display: block;
  width: 100%;
  padding: 10px 16px;
  text-align: left;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.bulk-action-btn:hover {
  background-color: #f3f4f6;
}

.quick-filters {
  display: flex;
  gap: 10px;
  align-items: center;
  margin: 15px 0;
}

.quick-filters-label {
  font-weight: 600;
  color: #374151;
  margin-right: 10px;
}

.quick-filter-btn {
  padding: 6px 12px;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  color: #374151;
}

.quick-filter-btn:hover {
  background-color: #f3f4f6;
}

.results-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  font-size: 14px;
  color: #6b7280;
}

.active-filters {
  display: flex;
  gap: 8px;
}

.filter-tag {
  background-color: #dbeafe;
  color: #1d4ed8;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* Appointment List */
.appointment-list-loading, .appointment-list-empty {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-state {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.appointment-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.appointment-table {
  width: 100%;
  border-collapse: collapse;
}

.appointment-table th {
  background-color: #f9fafb;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.appointment-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.appointment-table th.sortable:hover {
  background-color: #f3f4f6;
}

.appointment-table td {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.appointment-row:hover {
  background-color: #f9fafb;
}

.checkbox-column {
  width: 50px;
}

.customer-info {
  min-width: 200px;
}

.customer-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.customer-email {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 2px;
}

.customer-phone {
  color: #6b7280;
  font-size: 12px;
}

.service-info {
  min-width: 150px;
}

.service-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.service-category {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 2px;
}

.service-duration {
  color: #6b7280;
  font-size: 12px;
}

.datetime-info {
  min-width: 120px;
}

.date {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.time {
  color: #6b7280;
  font-size: 14px;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: none;
  cursor: pointer;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-confirmed {
  background-color: #d1fae5;
  color: #065f46;
}

.status-completed {
  background-color: #e0e7ff;
  color: #3730a3;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #991b1b;
}

.price {
  font-weight: 600;
  color: #059669;
  font-size: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .btn {
  padding: 6px 8px;
  font-size: 12px;
  border-radius: 4px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 12px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-sm { max-width: 400px; }
.modal-md { max-width: 600px; }
.modal-lg { max-width: 800px; }
.modal-xl { max-width: 1200px; }

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.modal-close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.modal-close-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.modal-content {
  padding: 24px;
}

/* Form Styles */
.appointment-form {
  max-width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
  font-size: 14px;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  box-sizing: border-box;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #ef4444;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-section {
  margin: 30px 0;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
}

.form-section h4 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.form-display-value {
  padding: 12px;
  background-color: #f3f4f6;
  border-radius: 6px;
  color: #374151;
  font-size: 14px;
}

.error-text {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 20px 0;
}

.pagination-info {
  color: #6b7280;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.pagination-btn:hover:not(.disabled) {
  background-color: #f3f4f6;
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 4px;
}

.pagination-page {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  min-width: 40px;
  text-align: center;
}

.pagination-page:hover:not(.dots) {
  background-color: #f3f4f6;
}

.pagination-page.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.pagination-page.dots {
  cursor: default;
  border: none;
  background: none;
}

/* Analytics Styles */
.analytics-dashboard {
  padding: 20px;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.date-range-controls {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.quick-ranges {
  display: flex;
  gap: 8px;
}

.quick-range-btn {
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.quick-range-btn:hover {
  background-color: #f3f4f6;
}

.custom-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
}

.metric-icon {
  font-size: 20px;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.metric-change {
  font-size: 14px;
  font-weight: 600;
}

.metric-change.positive { color: #059669; }
.metric-change.negative { color: #dc2626; }
.metric-change.neutral { color: #6b7280; }

.analytics-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.analytics-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.status-card {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background-color: #f9fafb;
}

.status-card.status-pending { background-color: #fef3c7; }
.status-card.status-confirmed { background-color: #d1fae5; }
.status-card.status-completed { background-color: #e0e7ff; }
.status-card.status-cancelled { background-color: #fee2e2; }

.status-count {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.status-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.status-percentage {
  font-size: 12px;
  color: #6b7280;
}

.chart-container {
  margin: 20px 0;
}

.analytics-chart {
  position: relative;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #374151;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.service-stats {
  max-height: 400px;
  overflow-y: auto;
}

.service-stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.service-rank {
  font-weight: 700;
  color: #6b7280;
  min-width: 30px;
}

.service-info {
  flex: 1;
}

.service-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.service-metrics {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #6b7280;
}

.service-bar {
  width: 100px;
  height: 8px;
  background-color: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.service-bar-fill {
  height: 100%;
  background-color: #3b82f6;
  transition: width 0.3s ease;
}

.revenue-insights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.insight-card {
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
  text-align: center;
}

.insight-card h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
}

.insight-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.insight-card p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

/* Services Page Styles */
.service-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 30px;
  border-bottom: 1px solid #e5e7eb;
}

.service-tab {
  padding: 12px 24px;
  border: none;
  background: none;
  color: #6b7280;
  font-weight: 600;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.service-tab:hover {
  color: #374151;
}

.service-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.services-section {
  margin-bottom: 40px;
}

.services-section h2 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.service-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.service-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.service-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 700;
  flex: 1;
}

.service-price {
  color: #059669;
  font-size: 18px;
  font-weight: 700;
}

.service-description {
  color: #6b7280;
  margin-bottom: 16px;
  line-height: 1.5;
}

.service-details {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.service-details span {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.service-category {
  background-color: #f3f4f6;
  color: #374151;
}

.service-duration {
  background-color: #dbeafe;
  color: #1e40af;
}

.service-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.service-status.active {
  background-color: #d1fae5;
  color: #065f46;
}

.service-status.inactive {
  background-color: #fee2e2;
  color: #991b1b;
}

.service-actions {
  display: flex;
  gap: 8px;
}

.addons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 30px;
}

.addon-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.addon-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.addon-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.addon-price {
  color: #059669;
  font-size: 16px;
  font-weight: 700;
}

.addon-details {
  margin-bottom: 16px;
}

.addon-details p {
  margin: 0 0 8px 0;
  color: #6b7280;
  font-size: 14px;
}

.addon-description {
  color: #374151 !important;
  font-style: italic;
}

.addon-actions {
  display: flex;
  gap: 8px;
}

.services-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 40px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-number {
  margin: 0;
  color: #1f2937;
  font-size: 32px;
  font-weight: 700;
}

/* Settings Page Styles */
.settings-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 30px;
  border-bottom: 1px solid #e5e7eb;
}

.settings-tab {
  padding: 12px 24px;
  border: none;
  background: none;
  color: #6b7280;
  font-weight: 600;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.settings-tab:hover {
  color: #374151;
}

.settings-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.settings-section {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.settings-section h2 {
  margin: 0 0 24px 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  color: #374151;
  font-weight: 600;
  font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input:disabled,
.form-group textarea:disabled,
.form-group select:disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.payment-method-group {
  padding: 24px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #f9fafb;
}

.payment-method-group h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.payment-method-group .form-group {
  margin-bottom: 16px;
}

.payment-method-group .form-group:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filters-row {
    flex-direction: column;
    gap: 15px;
  }

  .filter-group {
    min-width: 100%;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .appointment-table-container {
    overflow-x: auto;
  }

  .appointment-table {
    min-width: 800px;
  }

  .analytics-header {
    flex-direction: column;
    align-items: stretch;
  }

  .date-range-controls {
    flex-direction: column;
    gap: 15px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .addons-grid {
    grid-template-columns: 1fr;
  }

  .services-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .service-actions,
  .addon-actions {
    flex-direction: column;
  }

  .service-details {
    flex-direction: column;
    gap: 8px;
  }
}
