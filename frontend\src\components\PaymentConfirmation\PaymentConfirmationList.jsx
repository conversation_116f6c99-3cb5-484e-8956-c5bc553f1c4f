import { useState } from 'react';
import { Fi<PERSON>ye, FiEdit3, FiTrash2, FiCheck, FiX, FiClock, FiExternalLink } from 'react-icons/fi';
import { useToast } from '../../contexts/ToastContext';

const PaymentConfirmationList = ({ 
  confirmations = [], 
  branding,
  onEdit = null,
  onDelete = null,
  onUpdateStatus = null,
  showActions = false,
  isAdmin = false,
  loading = false 
}) => {
  const { showError, showSuccess, toast } = useToast();
  const [selectedImage, setSelectedImage] = useState(null);

  const getStatusColor = (status) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'verified':
        return <FiCheck className="w-4 h-4" />;
      case 'rejected':
        return <FiX className="w-4 h-4" />;
      case 'pending':
      default:
        return <FiClock className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleStatusUpdate = async (confirmationId, status, rejectionReason = '') => {
    try {
      await onUpdateStatus(confirmationId, status, rejectionReason);
      showSuccess(`Payment confirmation ${status} successfully`);
    } catch (error) {
      showError(`Failed to ${status} payment confirmation: ${error.message}`);
    }
  };

  const handleDelete = async (confirmationId) => {
    // Create a custom confirmation toast with buttons
    const confirmToast = toast((t) => (
      <div className="flex flex-col gap-3">
        <div className="font-medium text-gray-900">
          Delete Payment Confirmation
        </div>
        <div className="text-sm text-gray-600">
          Are you sure you want to delete this payment confirmation? This action cannot be undone.
        </div>
        <div className="flex gap-2 justify-end">
          <button
            onClick={() => toast.dismiss(t.id)}
            className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={async () => {
              toast.dismiss(t.id);
              try {
                await onDelete(confirmationId);
                showSuccess('Payment confirmation deleted successfully');
              } catch (error) {
                showError(`Failed to delete payment confirmation: ${error.message}`);
              }
            }}
            className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    ), {
      duration: Infinity,
      position: 'top-center',
      style: {
        background: '#fff',
        color: '#374151',
        border: '1px solid #e5e7eb',
        borderRadius: '12px',
        padding: '20px',
        minWidth: '400px',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      }
    });
  };

  const openImageModal = (imageUrl) => {
    setSelectedImage(imageUrl);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 animate-pulse">
            <div className="flex items-start justify-between mb-4">
              <div className="space-y-2">
                <div className="h-4 bg-gray-300 rounded w-32"></div>
                <div className="h-3 bg-gray-300 rounded w-24"></div>
              </div>
              <div className="h-6 bg-gray-300 rounded w-20"></div>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (confirmations.length === 0) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 text-center">
        <FiClock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No Payment Confirmations
        </h3>
        <p className="text-gray-600">
          No payment confirmations have been submitted yet.
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {confirmations.map((confirmation) => (
          <div key={confirmation._id} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-200">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Payment Confirmation
                  </h3>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(confirmation.status)}`}>
                    {getStatusIcon(confirmation.status)}
                    <span className="ml-1 capitalize">{confirmation.status}</span>
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  Submitted on {formatDate(confirmation.createdAt)}
                </p>
                {isAdmin && confirmation.user && (
                  <p className="text-sm text-gray-600">
                    By: {confirmation.user.firstName} {confirmation.user.lastName} ({confirmation.user.email})
                  </p>
                )}
              </div>

              {/* Actions */}
              {showActions && (
                <div className="flex items-center space-x-2">
                  {isAdmin && confirmation.status === 'pending' && (
                    <>
                      <button
                        onClick={() => handleStatusUpdate(confirmation._id, 'verified')}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200"
                        title="Verify"
                      >
                        <FiCheck className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          // Create a custom input toast for rejection reason
                          let rejectionReason = '';
                          const inputToast = toast((t) => (
                            <div className="flex flex-col gap-3">
                              <div className="font-medium text-gray-900">
                                Reject Payment Confirmation
                              </div>
                              <div className="text-sm text-gray-600">
                                Please provide a reason for rejection (optional):
                              </div>
                              <input
                                type="text"
                                placeholder="Rejection reason..."
                                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-red-500"
                                onChange={(e) => rejectionReason = e.target.value}
                                onKeyPress={(e) => {
                                  if (e.key === 'Enter') {
                                    toast.dismiss(t.id);
                                    handleStatusUpdate(confirmation._id, 'rejected', rejectionReason || '');
                                  }
                                }}
                                autoFocus
                              />
                              <div className="flex gap-2 justify-end">
                                <button
                                  onClick={() => toast.dismiss(t.id)}
                                  className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
                                >
                                  Cancel
                                </button>
                                <button
                                  onClick={() => {
                                    toast.dismiss(t.id);
                                    handleStatusUpdate(confirmation._id, 'rejected', rejectionReason || '');
                                  }}
                                  className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                                >
                                  Reject
                                </button>
                              </div>
                            </div>
                          ), {
                            duration: Infinity,
                            position: 'top-center',
                            style: {
                              background: '#fff',
                              color: '#374151',
                              border: '1px solid #e5e7eb',
                              borderRadius: '12px',
                              padding: '20px',
                              minWidth: '400px',
                              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                            }
                          });
                        }}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                        title="Reject"
                      >
                        <FiX className="w-4 h-4" />
                      </button>
                    </>
                  )}
                  
                  {onEdit && confirmation.status === 'pending' && (
                    <button
                      onClick={() => onEdit(confirmation)}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                      title="Edit"
                    >
                      <FiEdit3 className="w-4 h-4" />
                    </button>
                  )}
                  
                  {onDelete && (
                    <button
                      onClick={() => handleDelete(confirmation._id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                      title="Delete"
                    >
                      <FiTrash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-600">Amount</p>
                <p className="font-medium text-gray-900">${confirmation.amount}</p>
              </div>
              
              <div>
                <p className="text-sm text-gray-600">Payment Method</p>
                <p className="font-medium text-gray-900">{confirmation.paymentMethod}</p>
              </div>
              
              {confirmation.order && (
                <div>
                  <p className="text-sm text-gray-600">Order</p>
                  <p className="font-medium text-gray-900">{confirmation.order.orderNumber}</p>
                </div>
              )}
              
              {confirmation.appointment && (
                <div>
                  <p className="text-sm text-gray-600">Appointment</p>
                  <p className="font-medium text-gray-900">
                    {new Date(confirmation.appointment.date).toLocaleDateString()}
                  </p>
                </div>
              )}
            </div>

            {/* Payment Proof */}
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">Payment Proof</p>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => openImageModal(confirmation.proofImage)}
                  className="inline-flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors duration-200"
                >
                  <FiEye className="w-4 h-4 mr-2" />
                  View Image
                </button>
                <a
                  href={confirmation.proofImage}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                >
                  <FiExternalLink className="w-4 h-4 mr-2" />
                  Open in New Tab
                </a>
              </div>
            </div>

            {/* Notes */}
            {confirmation.notes && (
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-1">Notes</p>
                <p className="text-gray-900 bg-gray-50 rounded-lg p-3">{confirmation.notes}</p>
              </div>
            )}

            {/* Verification Info */}
            {confirmation.status !== 'pending' && (
              <div className="border-t border-gray-200 pt-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    {confirmation.status === 'verified' ? 'Verified' : 'Rejected'} on {formatDate(confirmation.verifiedAt)}
                  </span>
                  {confirmation.verifiedBy && (
                    <span className="text-gray-600">
                      by {confirmation.verifiedBy.firstName} {confirmation.verifiedBy.lastName}
                    </span>
                  )}
                </div>
                {confirmation.rejectionReason && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-600">Rejection Reason:</p>
                    <p className="text-red-700 bg-red-50 rounded-lg p-2 text-sm">{confirmation.rejectionReason}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={closeImageModal}
              className="absolute top-4 right-4 p-2 bg-white rounded-full text-gray-600 hover:text-gray-800 transition-colors duration-200"
            >
              <FiX className="w-6 h-6" />
            </button>
            <img
              src={selectedImage}
              alt="Payment proof"
              className="max-w-full max-h-full object-contain rounded-lg"
            />
          </div>
        </div>
      )}
    </>
  );
};

export default PaymentConfirmationList;
