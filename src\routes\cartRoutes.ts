import { Router } from 'express';
import { CartController } from '../controllers';
import { authenticate, optionalAuth } from '../middleware/auth';
import { validate } from '../middleware/validation';
import {
  addToCartValidation,
  updateCartItemValidation,
  mongoIdValidation
} from '../utils/validation';

const router = Router();

// GET /api/cart - Allow both authenticated and guest users
router.get(
  '/',
  optionalAuth,
  CartController.getCart
);

// POST /api/cart/items - Allow both authenticated and guest users
router.post(
  '/items',
  optionalAuth,
  validate(addToCartValidation),
  CartController.addToCart
);

// PUT /api/cart/items/:itemId - Allow both authenticated and guest users
router.put(
  '/items/:itemId',
  optionalAuth,
  validate(updateCartItemValidation),
  CartController.updateCartItem
);

// DELETE /api/cart/items/:itemId - Allow both authenticated and guest users
router.delete(
  '/items/:itemId',
  optionalAuth,
  validate(mongoIdValidation('itemId')),
  CartController.removeFromCart
);

// DELETE /api/cart - Allow both authenticated and guest users
router.delete(
  '/',
  optionalAuth,
  CartController.clearCart
);

// POST /api/cart/merge - Merge guest cart with user cart after login
router.post(
  '/merge',
  authenticate,
  CartController.mergeGuestCart
);

export default router;
