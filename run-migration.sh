#!/bin/bash

echo "🚀 MongoDB Migration Setup and Execution"
echo "========================================"

echo ""
echo "📦 Installing dependencies..."
cp migration-package.json package.json
npm install

echo ""
echo "🔍 Testing database connections..."
node test-connection.js

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ Connection test failed. Please check your database connections."
    exit 1
fi

echo ""
echo "🚀 Starting migration..."
node migrate-database.js

echo ""
echo "✅ Migration process completed!"
echo "📋 Check migration-report.json for detailed results"
