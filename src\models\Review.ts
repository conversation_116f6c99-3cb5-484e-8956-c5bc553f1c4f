import mongoose, { Schema } from 'mongoose';
import { IReview } from '../types';

const reviewSchema = new Schema<IReview>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required']
  },
  product: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    required: false
  },
  service: {
    type: Schema.Types.ObjectId,
    ref: 'Service',
    required: false
  },
  rating: {
    type: Number,
    required: [true, 'Rating is required'],
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot be more than 5']
  },
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  comment: {
    type: String,
    required: [true, 'Comment is required'],
    trim: true,
    maxlength: [1000, 'Comment cannot be more than 1000 characters']
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  isVerifiedPurchase: {
    type: Boolean,
    default: false
  },
  helpfulVotes: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes for better query performance
reviewSchema.index({ product: 1, status: 1 });
reviewSchema.index({ service: 1, status: 1 });
reviewSchema.index({ user: 1 });
reviewSchema.index({ status: 1 });
reviewSchema.index({ createdAt: -1 });

// Ensure a user can only review a product/service once
reviewSchema.index({ user: 1, product: 1 }, { unique: true, sparse: true });
reviewSchema.index({ user: 1, service: 1 }, { unique: true, sparse: true });

// Virtual for populated user data
reviewSchema.virtual('userData', {
  ref: 'User',
  localField: 'user',
  foreignField: '_id',
  justOne: true
});

// Virtual for populated product data
reviewSchema.virtual('productData', {
  ref: 'Product',
  localField: 'product',
  foreignField: '_id',
  justOne: true
});

// Virtual for populated service data
reviewSchema.virtual('serviceData', {
  ref: 'Service',
  localField: 'service',
  foreignField: '_id',
  justOne: true
});

// Ensure virtuals are included in JSON output
reviewSchema.set('toJSON', { virtuals: true });
reviewSchema.set('toObject', { virtuals: true });

// Custom validation to ensure either product or service is provided
reviewSchema.pre('validate', function(next) {
  if (!this.product && !this.service) {
    next(new Error('Review must be associated with either a product or service'));
  } else if (this.product && this.service) {
    next(new Error('Review cannot be associated with both product and service'));
  } else {
    next();
  }
});

export const Review = mongoose.model<IReview>('Review', reviewSchema);
