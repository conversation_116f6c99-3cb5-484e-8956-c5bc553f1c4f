import { useState, useEffect, useCallback, useRef } from 'react';
import { FiArrowLeft, FiCalendar, FiClock, FiDollarSign, FiUser, FiPhone, FiMail } from 'react-icons/fi';
import { serviceService, appointmentService, authService } from '../services';
import { useService, useAppointmentAvailability, useCreateAppointment } from '../hooks/useQueries';
import { useBranding } from '../contexts/BrandingContext';
import { useToast } from '../contexts/ToastContext';
import { PaymentConfirmationForm } from '../components/PaymentConfirmation';
import Loading from '../components/Loading';

const ServiceBooking = ({ serviceId, onNavigate }) => {
  const { branding } = useBranding();
  const { showSuccess, showError } = useToast();

  const [user, setUser] = useState(null);
  const [step, setStep] = useState(1); // 1: Booking Form, 2: Payment Confirmation
  const [appointmentData, setAppointmentData] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [availableSlots, setAvailableSlots] = useState([]);
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    date: '',
    time: '',
    notes: ''
  });

  // Use React Query to fetch service data
  const {
    data: service,
    isLoading: loading,
    error
  } = useService(serviceId);

  // Use React Query to fetch availability when date is selected
  const {
    data: availabilityData,
    isLoading: loadingSlots,
  } = useAppointmentAvailability(
    { date: formData.date, service: serviceId },
    { enabled: !!(formData.date && serviceId) }
  );

  // Use React Query mutation for creating appointments
  const createAppointmentMutation = useCreateAppointment();

  // Update available slots when availability data changes
  useEffect(() => {
    if (availabilityData?.availableSlots) {
      setAvailableSlots(availabilityData.availableSlots);
    } else {
      setAvailableSlots([]);
    }
  }, [availabilityData]);

  // Load current user if authenticated
  useEffect(() => {
    const loadUser = async () => {
      if (authService.isAuthenticated()) {
        try {
          const currentUser = await authService.getCurrentUser();
          setUser(currentUser);
          // Pre-fill form with user data
          setFormData(prev => ({
            ...prev,
            firstName: currentUser.firstName || '',
            lastName: currentUser.lastName || '',
            email: currentUser.email || '',
            phone: currentUser.phone || ''
          }));
        } catch (error) {
          console.error('Failed to load user:', error);
        }
      }
    };
    loadUser();
  }, []);

  const handleInputChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
    // React Query will automatically fetch availability when date changes
  };

  const handleBookingSubmit = async (e) => {
    e.preventDefault();

    if (!service) return;

    setIsSubmitting(true);
    try {
      const appointmentPayload = {
        service: service._id,
        name: `${formData.firstName} ${formData.lastName}`.trim(),
        email: formData.email,
        phone: formData.phone,
        date: formData.date,
        time: formData.time,
        message: formData.notes
      };

      const response = await createAppointmentMutation.mutateAsync(appointmentPayload);

      setAppointmentData(response);
      setStep(2);
      showSuccess('Appointment booked successfully! Please submit payment confirmation.');
    } catch (error) {
      showError(`Failed to book appointment: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePaymentConfirmationSubmit = async (confirmationData) => {
    try {
      // The PaymentConfirmationForm will handle the API call
      showSuccess('Payment confirmation submitted! We will verify and confirm your appointment soon.');
      onNavigate('user-dashboard');
    } catch (error) {
      throw error; // Let the form handle the error
    }
  };

  const formatDuration = (hours) => {
    if (!hours) return 'Duration not set'

    // Convert hours to a readable format
    if (hours === 1) {
      return '1 hour'
    } else if (hours < 1) {
      const minutes = Math.round(hours * 60)
      return `${minutes} minute${minutes !== 1 ? 's' : ''}`
    } else if (hours % 1 === 0) {
      return `${hours} hours`
    } else {
      return `${hours} hours`
    }
  };

  if (loading) {
    return <Loading />;
  }

  if (error || !service) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Service Not Found</h1>
          <p className="text-gray-600 mb-6">The service you're trying to book doesn't exist or has been removed.</p>
          <button
            onClick={() => onNavigate('services')}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            Back to Services
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center">
            <button
              onClick={() => step === 1 ? onNavigate(`services/${serviceId}`) : setStep(1)}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 mr-4"
            >
              <FiArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
                {step === 1 ? 'Book Service' : 'Payment Confirmation'}
              </h1>
              <p className="text-gray-600 mt-2">{service.name}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {step === 1 ? (
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Book Your Appointment</h2>
                
                <form onSubmit={handleBookingSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        First Name *
                      </label>
                      <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email *
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone *
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Preferred Date *
                      </label>
                      <input
                        type="date"
                        name="date"
                        value={formData.date}
                        onChange={handleInputChange}
                        required
                        min={new Date().toISOString().split('T')[0]}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Preferred Time *
                      </label>
                      <select
                        name="time"
                        value={formData.time}
                        onChange={handleInputChange}
                        required
                        disabled={!formData.date || loadingSlots}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
                      >
                        <option value="">
                          {!formData.date ? 'Select a date first' :
                           loadingSlots ? 'Loading available times...' :
                           'Select a time'}
                        </option>
                        {availableSlots.map((slot) => (
                          <option key={slot} value={slot}>
                            {slot}
                          </option>
                        ))}
                        {formData.date && !loadingSlots && availableSlots.length === 0 && (
                          <option value="" disabled>No available slots for this date</option>
                        )}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Additional Notes
                    </label>
                    <textarea
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      placeholder="Any special requests or information we should know..."
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 focus:ring-4 focus:ring-blue-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? 'Booking...' : 'Book Appointment'}
                  </button>
                </form>
              </div>
            ) : (
              <PaymentConfirmationForm
                onSubmit={handlePaymentConfirmationSubmit}
                branding={branding}
                appointmentData={appointmentData}
                onCancel={() => setStep(1)}
              />
            )}
          </div>

          {/* Service Summary Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 sticky top-8">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Service Summary</h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900">{service.name}</h4>
                  <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                </div>

                <div className="space-y-3 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-gray-600">
                      <FiDollarSign className="w-4 h-4 mr-2" />
                      <span>Price</span>
                    </div>
                    <span className="font-bold text-gray-900">${service.price}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-gray-600">
                      <FiClock className="w-4 h-4 mr-2" />
                      <span>Duration</span>
                    </div>
                    <span className="font-medium text-gray-900">
                      {formatDuration(service.duration)}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-gray-600">
                      <FiUser className="w-4 h-4 mr-2" />
                      <span>Category</span>
                    </div>
                    <span className="font-medium text-gray-900">{service.category}</span>
                  </div>
                </div>

                {step === 2 && appointmentData && (
                  <div className="pt-4 border-t border-gray-200">
                    <h4 className="font-medium text-gray-900 mb-3">Appointment Details</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Date:</span>
                        <span className="font-medium">
                          {new Date(appointmentData.date).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Time:</span>
                        <span className="font-medium">{appointmentData.time}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Reference:</span>
                        <span className="font-medium text-xs">{appointmentData._id}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceBooking;
