# Multi-stage build for production
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Build the backend
FROM base AS backend-builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Build the frontend
FROM base AS frontend-builder
WORKDIR /app/frontendv2
COPY frontendv2/package*.json ./
RUN npm ci
COPY frontendv2/ .
RUN npm run build:production

# Production image
FROM node:18-alpine AS runner
WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nodejs

# Copy built backend
COPY --from=backend-builder --chown=nodejs:nodejs /app/dist ./dist
COPY --from=deps --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --from=backend-builder --chown=nodejs:nodejs /app/package.json ./package.json

# Copy built frontend
COPY --from=frontend-builder --chown=nodejs:nodejs /app/frontendv2/dist ./public

# Create uploads directory
RUN mkdir -p uploads && chown nodejs:nodejs uploads

# Copy production environment file
COPY --chown=nodejs:nodejs .env.production .env

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
CMD ["npm", "start:production"]
