// localStorage utility functions for data persistence

const STORAGE_KEYS = {
  USER_APPOINTMENTS: 'goldie_locs_user_appointments',
  USER_ORDERS: 'goldie_locs_user_orders',
  USER_FAVORITES: 'goldie_locs_user_favorites',
  USER_PROFILE: 'goldie_locs_user_profile',
  ADMIN_APPOINTMENTS: 'goldie_locs_admin_appointments',
  ADMIN_CUSTOMERS: 'goldie_locs_admin_customers',
  ADMIN_ORDERS: 'goldie_locs_admin_orders',
  ADMIN_PRODUCTS: 'goldie_locs_admin_products',
  CART_ITEMS: 'goldie_locs_cart_items',
  USER_SESSION: 'goldie_locs_user_session',
  PENDING_PAYMENT_CONFIRMATIONS: 'goldie_locs_pending_payment_confirmations'
}

// Generic localStorage functions
export const saveToStorage = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (error) {
    console.error('Error saving to localStorage:', error)
  }
}

export const loadFromStorage = (key, defaultValue = null) => {
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error('Error loading from localStorage:', error)
    return defaultValue
  }
}

export const removeFromStorage = (key) => {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error('Error removing from localStorage:', error)
  }
}

// User Dashboard specific functions
export const saveUserAppointments = (appointments) => {
  saveToStorage(STORAGE_KEYS.USER_APPOINTMENTS, appointments)
}

export const loadUserAppointments = () => {
  return loadFromStorage(STORAGE_KEYS.USER_APPOINTMENTS, [])
}

export const saveUserOrders = (orders) => {
  saveToStorage(STORAGE_KEYS.USER_ORDERS, orders)
}

export const loadUserOrders = () => {
  return loadFromStorage(STORAGE_KEYS.USER_ORDERS, [])
}

export const saveUserFavorites = (favorites) => {
  saveToStorage(STORAGE_KEYS.USER_FAVORITES, favorites)
}

export const loadUserFavorites = () => {
  return loadFromStorage(STORAGE_KEYS.USER_FAVORITES, [])
}

export const saveUserProfile = (profile) => {
  saveToStorage(STORAGE_KEYS.USER_PROFILE, profile)
}

export const loadUserProfile = () => {
  return loadFromStorage(STORAGE_KEYS.USER_PROFILE, {
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '(*************',
    address: '123 Main St, Atlanta, GA 30309',
    joinDate: 'March 2024',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    membershipLevel: 'Gold Member',
    totalSpent: 430,
    loyaltyPoints: 850,
    preferences: {
      notifications: true,
      newsletter: true,
      smsReminders: false
    }
  })
}

// Admin Dashboard specific functions
export const saveAdminAppointments = (appointments) => {
  saveToStorage(STORAGE_KEYS.ADMIN_APPOINTMENTS, appointments)
}

export const loadAdminAppointments = () => {
  return loadFromStorage(STORAGE_KEYS.ADMIN_APPOINTMENTS, [])
}

export const saveAdminCustomers = (customers) => {
  saveToStorage(STORAGE_KEYS.ADMIN_CUSTOMERS, customers)
}

export const loadAdminCustomers = () => {
  return loadFromStorage(STORAGE_KEYS.ADMIN_CUSTOMERS, [])
}

export const saveAdminOrders = (orders) => {
  saveToStorage(STORAGE_KEYS.ADMIN_ORDERS, orders)
}

export const loadAdminOrders = () => {
  return loadFromStorage(STORAGE_KEYS.ADMIN_ORDERS, [])
}

export const saveAdminProducts = (products) => {
  saveToStorage(STORAGE_KEYS.ADMIN_PRODUCTS, products)
}

export const loadAdminProducts = () => {
  return loadFromStorage(STORAGE_KEYS.ADMIN_PRODUCTS, [])
}

// Cart functions
export const saveCartItems = (items) => {
  saveToStorage(STORAGE_KEYS.CART_ITEMS, items)
}

export const loadCartItems = () => {
  return loadFromStorage(STORAGE_KEYS.CART_ITEMS, [])
}

// User session functions
export const saveUserSession = (user) => {
  saveToStorage(STORAGE_KEYS.USER_SESSION, user)
}

export const loadUserSession = () => {
  return loadFromStorage(STORAGE_KEYS.USER_SESSION, null)
}

export const clearUserSession = () => {
  removeFromStorage(STORAGE_KEYS.USER_SESSION)
}

// Pending Payment Confirmations functions
export const savePendingPaymentConfirmation = (appointmentId, paymentData, userId = null) => {
  if (!userId) {
    console.warn('No user ID provided for saving payment confirmation')
    return
  }

  const pendingPayments = loadFromStorage(STORAGE_KEYS.PENDING_PAYMENT_CONFIRMATIONS, {})
  const paymentKey = `${userId}_${appointmentId}` // Include user ID in the key

  pendingPayments[paymentKey] = {
    ...paymentData,
    userId,
    appointmentId,
    status: 'pending',
    createdAt: new Date().toISOString()
  }
  saveToStorage(STORAGE_KEYS.PENDING_PAYMENT_CONFIRMATIONS, pendingPayments)
}

export const loadPendingPaymentConfirmation = (appointmentId, userId = null) => {
  if (!userId) {
    return null // No user, no payment data
  }

  const pendingPayments = loadFromStorage(STORAGE_KEYS.PENDING_PAYMENT_CONFIRMATIONS, {})
  const paymentKey = `${userId}_${appointmentId}`
  return pendingPayments[paymentKey] || null
}

export const removePendingPaymentConfirmation = (appointmentId, userId = null) => {
  if (!userId) {
    return // No user, nothing to remove
  }

  const pendingPayments = loadFromStorage(STORAGE_KEYS.PENDING_PAYMENT_CONFIRMATIONS, {})
  const paymentKey = `${userId}_${appointmentId}`
  delete pendingPayments[paymentKey]
  saveToStorage(STORAGE_KEYS.PENDING_PAYMENT_CONFIRMATIONS, pendingPayments)
}

export const loadAllPendingPaymentConfirmations = () => {
  return loadFromStorage(STORAGE_KEYS.PENDING_PAYMENT_CONFIRMATIONS, {})
}

// Clear payment confirmations for a specific user
export const clearUserPendingPaymentConfirmations = (userId = null) => {
  if (!userId) {
    return // No user, nothing to clear
  }

  const pendingPayments = loadFromStorage(STORAGE_KEYS.PENDING_PAYMENT_CONFIRMATIONS, {})
  const userPrefix = `${userId}_`

  // Remove all payment confirmations for this user
  Object.keys(pendingPayments).forEach(key => {
    if (key.startsWith(userPrefix)) {
      delete pendingPayments[key]
    }
  })

  saveToStorage(STORAGE_KEYS.PENDING_PAYMENT_CONFIRMATIONS, pendingPayments)
}

// Clear all data (for logout or reset)
export const clearAllUserData = () => {
  Object.values(STORAGE_KEYS).forEach(key => {
    removeFromStorage(key)
  })
}
