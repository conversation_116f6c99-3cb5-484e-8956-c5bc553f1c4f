import { Router } from 'express';
import { MediaController } from '../controllers/mediaController';
import { authenticate, authorize } from '../middleware/auth';
import { upload } from '../middleware/upload';

const router = Router();

// Public routes
router.get('/library', MediaController.getMediaLibrary);
router.get('/stats', MediaController.getMediaStats);
router.get('/tags', MediaController.getAllTags);
router.get('/:id', MediaController.getMediaById);

// Protected routes (require authentication)
router.post('/upload', authenticate, upload.single('file'), MediaController.uploadMedia);
router.put('/:id', authenticate, MediaController.updateMedia);
router.delete('/:id', authenticate, MediaController.deleteMedia);
router.post('/bulk-delete', authenticate, MediaController.bulkDeleteMedia);

// Usage tracking routes
router.post('/:mediaId/track-usage', authenticate, MediaController.trackMediaUsage);
router.delete('/:mediaId/remove-usage', authenticate, MediaController.removeMediaUsage);

export default router;
