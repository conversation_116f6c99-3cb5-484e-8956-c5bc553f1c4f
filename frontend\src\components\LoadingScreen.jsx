import { useState, useEffect } from 'react'
import { FALLBACK_COLORS } from '../utils/constants'

const LoadingScreen = ({ 
  isLoading = true, 
  error = null, 
  onRetry = null,
  retryCount = 0,
  maxRetries = 3 
}) => {
  const [dots, setDots] = useState('')

  // Animate loading dots
  useEffect(() => {
    if (!isLoading) return

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return ''
        return prev + '.'
      })
    }, 500)

    return () => clearInterval(interval)
  }, [isLoading])

  if (!isLoading && !error) return null

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{
        background: `linear-gradient(135deg, ${FALLBACK_COLORS.primary}10, ${FALLBACK_COLORS.secondary}05, ${FALLBACK_COLORS.accent}08)`,
        backgroundSize: '400% 400%',
        animation: 'gradientShift 15s ease infinite'
      }}
    >
      {/* Background Animation */}
      <style jsx>{`
        @keyframes gradientShift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
        
        @keyframes bounce {
          0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
          40%, 43% { transform: translate3d(0, -30px, 0); }
          70% { transform: translate3d(0, -15px, 0); }
          90% { transform: translate3d(0, -4px, 0); }
        }
      `}</style>

      <div className="text-center p-8 max-w-md mx-auto">
        {error ? (
          // Error State
          <div className="space-y-6">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 flex items-center justify-center">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                Unable to Load Website
              </h2>
              <p className="text-gray-600 mb-4">
                We're having trouble loading the website configuration. This might be due to a network issue.
              </p>
              <p className="text-sm text-gray-500 mb-6">
                {error}
              </p>
            </div>

            {onRetry && (
              <div className="space-y-3">
                <button
                  onClick={onRetry}
                  className="w-full px-6 py-3 text-white rounded-lg font-medium transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5"
                  style={{ backgroundColor: FALLBACK_COLORS.primary }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = FALLBACK_COLORS.accent}
                  onMouseLeave={(e) => e.target.style.backgroundColor = FALLBACK_COLORS.primary}
                >
                  Try Again {retryCount > 0 && `(${retryCount}/${maxRetries})`}
                </button>
                
                <button
                  onClick={() => window.location.reload()}
                  className="w-full px-6 py-3 text-gray-700 bg-gray-100 rounded-lg font-medium transition-all duration-200 hover:bg-gray-200"
                >
                  Refresh Page
                </button>
              </div>
            )}
          </div>
        ) : (
          // Loading State
          <div className="space-y-6">
            {/* Logo/Brand Area */}
            <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center"
                 style={{ backgroundColor: `${FALLBACK_COLORS.primary}20` }}>
              <div 
                className="w-12 h-12 rounded-full border-4 border-transparent animate-spin"
                style={{ 
                  borderTopColor: FALLBACK_COLORS.primary,
                  borderRightColor: FALLBACK_COLORS.accent,
                  animation: 'spin 1s linear infinite'
                }}
              />
            </div>

            {/* Loading Text */}
            <div>
              <h2 className="text-2xl font-bold mb-2" style={{ color: FALLBACK_COLORS.secondary }}>
                Loading Website{dots}
              </h2>
              <p className="text-gray-600">
                Preparing your experience
              </p>
            </div>

            {/* Progress Indicator */}
            <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
              <div 
                className="h-2 rounded-full transition-all duration-300"
                style={{ 
                  backgroundColor: FALLBACK_COLORS.primary,
                  width: retryCount > 0 ? `${((retryCount + 1) / (maxRetries + 1)) * 100}%` : '60%',
                  animation: 'pulse 2s ease-in-out infinite'
                }}
              />
            </div>

            {/* Retry Info */}
            {retryCount > 0 && (
              <p className="text-sm text-gray-500">
                Retrying connection... ({retryCount}/{maxRetries})
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default LoadingScreen
