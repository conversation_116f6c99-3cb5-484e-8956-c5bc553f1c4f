import { useState, useEffect } from 'react'

/**
 * Custom hook for debouncing values
 * @param {any} value - The value to debounce
 * @param {number} delay - The delay in milliseconds (default: 300ms)
 * @returns {any} - The debounced value
 */
export const useDebounce = (value, delay = 300) => {
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Custom hook for debounced search with fast response
 * @param {string} searchTerm - The search term
 * @param {number} delay - The delay in milliseconds (default: 150ms for fast search)
 * @returns {string} - The debounced search term
 */
export const useDebouncedSearch = (searchTerm, delay = 150) => {
  return useDebounce(searchTerm, delay)
}

export default useDebounce
