import { User } from '../models/User';
import { AppointmentService } from './appointmentService';
import { AuthService } from './authService';
import { emailService } from './emailService';
import { generateToken, generateRefreshToken } from '../utils/jwt';
import crypto from 'crypto';

interface ConsultationData {
  name?: string; // For backward compatibility
  firstName?: string;
  lastName?: string;
  email: string;
  phone?: string;
  password?: string; // For guest user authentication
  service: string;
  date: string;
  time: string;
  message?: string;
  isLoggedIn?: boolean;
  userId?: string;
}

interface ConsultationResult {
  success: boolean;
  message: string;
  appointment?: any;
  user?: any;
  token?: string;
  refreshToken?: string;
  isNewUser?: boolean;
  generatedPassword?: string;
}

export class ConsultationService {
  /**
   * Smart consultation booking that handles:
   * 1. Guest users (auto-creates account with generated password)
   * 2. Existing users (uses their data)
   * 3. New users (creates account from form data)
   * 4. Logged-in users (uses authenticated user data)
   */
  static async bookConsultation(data: ConsultationData): Promise<ConsultationResult> {
    try {
      let user: any;
      let isNewUser = false;
      let generatedPassword: string | undefined;
      let token: string | undefined;
      let refreshToken: string | undefined;

      // Case 1: User is already logged in
      if (data.isLoggedIn && data.userId) {
        user = await User.findById(data.userId);
        if (!user) {
          throw new Error('Authenticated user not found');
        }
      } else {
        // Case 2: Check if user exists by email
        const existingUser = await User.findOne({ email: data.email.toLowerCase() }).select('+password');

        if (existingUser) {
          // Existing user - check if password was provided for authentication
          if (data.password) {
            // Verify password for existing user
            const isPasswordValid = await existingUser.comparePassword(data.password);
            if (!isPasswordValid) {
              throw new Error('Invalid password. Please enter the correct password for your account.');
            }

            // Password is valid - authenticate user and generate tokens
            const payload = {
              userId: existingUser._id,
              email: existingUser.email,
              role: existingUser.role
            };

            token = generateToken(payload);
            refreshToken = generateRefreshToken(payload);

            // Remove password from user object
            existingUser.password = undefined as any;
          }

          user = existingUser;
        } else {
          // Case 3: New user - create account
          isNewUser = true;

          // Use provided password or generate one if not provided
          if (data.password && data.password.trim()) {
            generatedPassword = data.password.trim();
          } else {
            // Fallback: Generate a secure password for guest users
            generatedPassword = this.generateSecurePassword();
          }

          // Get firstName and lastName from data or split name
          let firstName: string;
          let lastName: string;

          if (data.firstName && data.lastName) {
            firstName = data.firstName.trim();
            lastName = data.lastName.trim();
          } else if (data.name) {
            // Fallback: split name into firstName and lastName
            const nameParts = data.name.trim().split(' ');
            firstName = nameParts[0] || 'Guest';
            lastName = nameParts.slice(1).join(' ') || 'User';
          } else {
            firstName = 'Guest';
            lastName = 'User';
          }
          
          // Create new user account
          const fullName = `${firstName} ${lastName}`.trim();
          const registrationResult = await AuthService.register({
            firstName,
            lastName,
            name: fullName,
            email: data.email,
            phone: data.phone,
            password: generatedPassword
          });
          
          user = registrationResult.user;
          token = registrationResult.token;
          refreshToken = registrationResult.refreshToken;
        }
      }

      // Create the appointment
      const customerName = data.name || `${data.firstName || user.firstName} ${data.lastName || user.lastName}`.trim() || user.name;
      const appointmentData = {
        user: user._id,
        service: data.service,
        date: new Date(data.date),
        time: data.time,
        customerInfo: {
          name: customerName,
          email: data.email,
          phone: data.phone || user.phone || ''
        },
        message: data.message,
        type: 'consultation' as const
      };

      const appointment = await AppointmentService.createAppointment(appointmentData);

      // Send emails asynchronously (don't wait for them to complete)
      try {
        if (isNewUser) {
          // Send welcome email for new users
          emailService.sendWelcomeEmail(user, generatedPassword).catch(err =>
            console.error('Failed to send welcome email:', err)
          );
        }

        // Send consultation confirmation email
        emailService.sendConsultationConfirmation(user, {
          service: data.service,
          date: data.date,
          time: data.time,
          message: data.message
        }).catch(err =>
          console.error('Failed to send consultation confirmation email:', err)
        );
      } catch (emailError) {
        console.error('Email service error:', emailError);
        // Don't fail the booking if email fails
      }

      // Prepare response message
      let message = 'Consultation booked successfully!';
      if (isNewUser) {
        message += ' An account has been created for you. Please check your email for login details.';
      }

      return {
        success: true,
        message,
        appointment,
        user: {
          _id: user._id,
          name: user.name,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
          role: user.role,
          isVerified: user.isVerified
        },
        token,
        refreshToken,
        isNewUser,
        generatedPassword: isNewUser ? generatedPassword : undefined
      };

    } catch (error) {
      console.error('Consultation booking error:', error);
      throw error;
    }
  }

  /**
   * Generate a secure password for guest users
   */
  private static generateSecurePassword(): string {
    // Generate a secure 12-character password with mix of letters, numbers, and symbols
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charset.length);
      password += charset[randomIndex];
    }
    
    return password;
  }

  /**
   * Get consultation availability (same as appointment availability)
   */
  static async getAvailability(date: string, serviceId?: string): Promise<string[]> {
    return AppointmentService.getAvailableTimeSlots(date, serviceId);
  }
}
