import { Router } from 'express';
import { Request, Response } from 'express';
import { Service } from '../../models';
import { sendSuccess, sendError } from '../../utils/response';

const router = Router();

// GET /api/v2/services - Get all services formatted for frontend v2
router.get('/', async (req: Request, res: Response) => {
  try {
    const services = await Service.find({ isActive: true }).sort({ category: 1, name: 1 });
    
    // Group services by category to match frontend v2 structure
    const groupedServices: { [key: string]: any[] } = {};
    
    services.forEach(service => {
      const categoryKey = service.category.toLowerCase().replace(/\s+/g, '');
      
      if (!groupedServices[categoryKey]) {
        groupedServices[categoryKey] = [];
      }
      
      groupedServices[categoryKey].push({
        id: service._id,
        name: service.name,
        price: service.price.toFixed(2),
        description: service.description,
        duration: service.duration,
        category: service.category,
        image: service.image,
        images: service.images
      });
    });
    
    sendSuccess(res, 'Services retrieved successfully', groupedServices);
  } catch (error) {
    console.error('Get services error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/services/categories - Get service categories
router.get('/categories', async (req: Request, res: Response) => {
  try {
    const categories = await Service.distinct('category', { isActive: true });
    sendSuccess(res, 'Service categories retrieved successfully', categories);
  } catch (error) {
    console.error('Get service categories error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/services/:id - Get service by ID
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const service = await Service.findById(id);
    
    if (!service) {
      sendError(res, 'Service not found', undefined, 404);
      return;
    }
    
    if (!service.isActive) {
      sendError(res, 'Service is not available', undefined, 404);
      return;
    }
    
    const formattedService = {
      id: service._id,
      name: service.name,
      price: service.price.toFixed(2),
      description: service.description,
      duration: service.duration,
      category: service.category,
      image: service.image,
      images: service.images
    };
    
    sendSuccess(res, 'Service retrieved successfully', formattedService);
  } catch (error) {
    console.error('Get service error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/services/category/:category - Get services by category
router.get('/category/:category', async (req: Request, res: Response) => {
  try {
    const { category } = req.params;
    const services = await Service.find({ 
      category: new RegExp(category, 'i'), 
      isActive: true 
    }).sort({ name: 1 });
    
    const formattedServices = services.map(service => ({
      id: service._id,
      name: service.name,
      price: service.price.toFixed(2),
      description: service.description,
      duration: service.duration,
      category: service.category,
      image: service.image,
      images: service.images
    }));
    
    sendSuccess(res, 'Services retrieved successfully', formattedServices);
  } catch (error) {
    console.error('Get services by category error:', error);
    sendError(res, (error as Error).message);
  }
});

export default router;
