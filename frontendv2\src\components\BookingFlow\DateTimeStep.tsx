import { useNavigate, useSearchParams } from 'react-router-dom'
import DateTimeSelection from '../DateTimeSelection'
import { addOnServices, getAddOnsForService, type AddOnService } from '../../config/services'

export default function DateTimeStep() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Get service from URL params
  const serviceParam = searchParams.get('service');
  const addonsParam = searchParams.get('addons');
  
  let selectedService = null;
  let selectedAddOns: AddOnService[] = [];
  
  if (serviceParam) {
    try {
      selectedService = JSON.parse(serviceParam);
    } catch (error) {
      console.error('Error parsing service:', error);
    }
  }
  
  if (addonsParam) {
    try {
      selectedAddOns = JSON.parse(addonsParam);
    } catch (error) {
      console.error('Error parsing addons:', error);
    }
  }

  // Get add-ons based on service type (exclude for consultation)
  const availableAddOns = selectedService ? getAddOnsForService(selectedService.id) : addOnServices;

  const booking = {
    selectedService,
    selectedAddOns,
    selectedDate: '',
    selectedTime: '',
    step: 'datetime' as const
  };

  const handleBack = () => {
    navigate('/');
  };

  const handleSelect = (date: string, time: string) => {
    const params = new URLSearchParams();
    params.set('service', JSON.stringify(selectedService));
    if (selectedAddOns.length > 0) {
      params.set('addons', JSON.stringify(selectedAddOns));
    }
    params.set('date', date);
    params.set('time', time);
    
    navigate(`/booking/details?${params.toString()}`);
  };

  const handleAddOnToggle = (addOn: AddOnService) => {
    const isSelected = selectedAddOns.find(item => item.id === addOn.id);
    let newAddOns;

    if (isSelected) {
      newAddOns = selectedAddOns.filter(item => item.id !== addOn.id);
    } else {
      newAddOns = [...selectedAddOns, addOn];
    }

    // Update URL with new addons
    const params = new URLSearchParams();
    params.set('service', JSON.stringify(selectedService));
    if (newAddOns.length > 0) {
      params.set('addons', JSON.stringify(newAddOns));
    }
    
    navigate(`/booking/datetime?${params.toString()}`, { replace: true });
  };

  if (!selectedService) {
    navigate('/');
    return null;
  }

  return (
    <DateTimeSelection
      booking={booking}
      onBack={handleBack}
      onSelect={handleSelect}
      addOnServices={availableAddOns}
      onAddOnToggle={handleAddOnToggle}
    />
  );
}
