import { useEffect } from 'react'

/**
 * Custom hook to scroll to top of page
 * @param {boolean} trigger - When true, scrolls to top
 * @param {Object} options - Scroll options
 */
export const useScrollToTop = (trigger = true, options = {}) => {
  const defaultOptions = {
    top: 0,
    left: 0,
    behavior: 'smooth',
    ...options
  }

  useEffect(() => {
    if (trigger) {
      window.scrollTo(defaultOptions)
    }
  }, [trigger, defaultOptions.top, defaultOptions.left, defaultOptions.behavior])
}

/**
 * Function to manually scroll to top
 * @param {Object} options - Scroll options
 */
export const scrollToTop = (options = {}) => {
  const defaultOptions = {
    top: 0,
    left: 0,
    behavior: 'smooth',
    ...options
  }
  
  window.scrollTo(defaultOptions)
}

/**
 * Custom hook for scroll to top on route/tab change
 * @param {string} activeRoute - Current active route/tab
 * @param {Object} options - Scroll options
 */
export const useScrollToTopOnChange = (activeRoute, options = {}) => {
  useEffect(() => {
    if (activeRoute) {
      scrollToTop(options)
    }
  }, [activeRoute])
}

export default useScrollToTop
