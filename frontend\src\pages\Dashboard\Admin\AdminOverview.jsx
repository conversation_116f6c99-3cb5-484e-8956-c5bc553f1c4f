import { useState, useEffect } from 'react'
import { useBranding } from '../../../contexts/BrandingContext'
import MultiDayAppointments from '../../../components/Dashboard/MultiDayAppointments'
import EnhancedAnalyticsCards from '../../../components/Dashboard/EnhancedAnalyticsCards'

const AdminOverview = ({
  dashboardStats,
  sectionLoading,
  adminData,
  onNavigateToTab
}) => {
  const { branding } = useBranding()

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="relative overflow-hidden rounded-2xl p-8 shadow-lg border border-white/20"
           style={{
             background: `linear-gradient(135deg, ${branding.colors.primary}90, ${branding.colors.secondary}80, ${branding.colors.accent}70)`,
             backgroundSize: '400% 400%',
             animation: 'gradientShift 8s ease infinite'
           }}>
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-2">Welcome back, {(adminData?.name || adminData?.firstName || 'Admin').replace(/\s+User!?$/i, '')}!</h2>
          <p className="text-white/80 text-lg">Here's your business overview for today</p>
        </div>
      </div>

      {/* Enhanced Analytics Cards */}
      <EnhancedAnalyticsCards
        dashboardStats={dashboardStats}
        isLoading={sectionLoading.overview || sectionLoading.dashboardStats}
      />

      {/* Multi-Day Appointments */}
      <MultiDayAppointments
        dashboardStats={dashboardStats}
        onNavigateToTab={onNavigateToTab}
        isLoading={sectionLoading.overview || sectionLoading.dashboardStats}
      />


    </div>
  )
}

export default AdminOverview
