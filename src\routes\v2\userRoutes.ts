import { Router } from 'express';
import { Request, Response } from 'express';
import { User, Appointment, PaymentConfirmation } from '../../models';
import { authenticate } from '../../middleware/auth';
import { sendSuccess, sendError } from '../../utils/response';
import { AuthenticatedRequest } from '../../types';

const router = Router();

// GET /api/v2/user/profile - Get user profile
router.get('/profile', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const user = await User.findById(req.user._id).select('-password');

    if (!user) {
      sendError(res, 'User not found', undefined, 404);
      return;
    }

    const profileData = {
      id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      isVerified: user.isVerified,
      notificationPreferences: user.notificationPreferences,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    sendSuccess(res, 'User profile retrieved successfully', profileData);
  } catch (error) {
    console.error('Get user profile error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/user/profile - Update user profile
router.put('/profile', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const { firstName, lastName, phone, notificationPreferences } = req.body;

    const user = await User.findById(req.user._id);

    if (!user) {
      sendError(res, 'User not found', undefined, 404);
      return;
    }

    // Update fields
    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;
    if (phone) user.phone = phone;
    if (notificationPreferences) {
      user.notificationPreferences = {
        ...user.notificationPreferences,
        ...notificationPreferences
      };
    }

    await user.save();

    const profileData = {
      id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      isVerified: user.isVerified,
      notificationPreferences: user.notificationPreferences,
      updatedAt: user.updatedAt
    };

    sendSuccess(res, 'User profile updated successfully', profileData);
  } catch (error) {
    console.error('Update user profile error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/user/dashboard - Get user dashboard data
router.get('/dashboard', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    // Get user's recent appointments
    const recentAppointments = await Appointment.find({ user: req.user._id })
      .populate('service', 'name price duration category')
      .sort({ date: -1, time: -1 })
      .limit(5);

    // Get user's payment confirmations
    const paymentConfirmations = await PaymentConfirmation.find({ user: req.user._id })
      .populate([
        { path: 'appointment', populate: { path: 'service', select: 'name' } },
        { path: 'order' }
      ])
      .sort({ createdAt: -1 })
      .limit(5);

    // Get appointment statistics
    const appointmentStats = await Appointment.aggregate([
      { $match: { user: req.user._id } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get payment confirmation statistics
    const paymentStats = await PaymentConfirmation.aggregate([
      { $match: { user: req.user._id } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Format appointments
    const formattedAppointments = recentAppointments.map(appointment => ({
      id: appointment._id,
      serviceId: (appointment.service as any)._id,
      serviceName: (appointment.service as any).name,
      servicePrice: (appointment.service as any).price,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      totalPrice: (appointment.service as any).price,
      notes: appointment.message,
      createdAt: appointment.createdAt
    }));

    // Format payment confirmations
    const formattedPaymentConfirmations = paymentConfirmations.map(confirmation => ({
      id: confirmation._id,
      appointmentId: confirmation.appointment?._id,
      appointmentService: (confirmation.appointment as any)?.service?.name,
      orderId: confirmation.order?._id,
      amount: confirmation.amount,
      paymentMethod: confirmation.paymentMethod,
      status: confirmation.status,
      createdAt: confirmation.createdAt
    }));

    // Format statistics
    const statsData = {
      appointments: appointmentStats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {} as any),
      payments: paymentStats.reduce((acc, stat) => {
        acc[stat._id] = {
          count: stat.count,
          totalAmount: stat.totalAmount
        };
        return acc;
      }, {} as any)
    };

    const dashboardData = {
      user: {
        id: req.user._id,
        firstName: req.user.firstName,
        lastName: req.user.lastName,
        email: req.user.email
      },
      recentAppointments: formattedAppointments,
      recentPaymentConfirmations: formattedPaymentConfirmations,
      statistics: statsData
    };

    sendSuccess(res, 'Dashboard data retrieved successfully', dashboardData);
  } catch (error) {
    console.error('Get dashboard data error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/user/appointments - Get all user appointments with pagination
router.get('/appointments', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;

    const query: any = { user: req.user._id };
    if (status) {
      query.status = status;
    }

    const appointments = await Appointment.find(query)
      .populate('service', 'name price duration category')
      .sort({ date: -1, time: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    const total = await Appointment.countDocuments(query);

    const formattedAppointments = appointments.map(appointment => ({
      id: appointment._id,
      serviceId: (appointment.service as any)._id,
      serviceName: (appointment.service as any).name,
      servicePrice: (appointment.service as any).price,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      totalPrice: (appointment.service as any).price,
      notes: appointment.message,
      createdAt: appointment.createdAt
    }));

    sendSuccess(res, 'User appointments retrieved successfully', {
      appointments: formattedAppointments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get user appointments error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/user/payment-confirmations - Get all user payment confirmations with pagination
router.get('/payment-confirmations', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;

    const query: any = { user: req.user._id };
    if (status) {
      query.status = status;
    }

    const paymentConfirmations = await PaymentConfirmation.find(query)
      .populate([
        { path: 'appointment', populate: { path: 'service', select: 'name' } },
        { path: 'order' }
      ])
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    const total = await PaymentConfirmation.countDocuments(query);

    const formattedConfirmations = paymentConfirmations.map(confirmation => ({
      id: confirmation._id,
      appointmentId: confirmation.appointment?._id,
      appointmentService: (confirmation.appointment as any)?.service?.name,
      orderId: confirmation.order?._id,
      amount: confirmation.amount,
      paymentMethod: confirmation.paymentMethod,
      proofImage: confirmation.proofImage,
      notes: confirmation.notes,
      status: confirmation.status,
      verifiedAt: confirmation.verifiedAt,
      rejectionReason: confirmation.rejectionReason,
      createdAt: confirmation.createdAt
    }));

    sendSuccess(res, 'User payment confirmations retrieved successfully', {
      paymentConfirmations: formattedConfirmations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get user payment confirmations error:', error);
    sendError(res, (error as Error).message);
  }
});

export default router;
