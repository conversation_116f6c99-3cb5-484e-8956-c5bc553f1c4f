import { useState, useEffect } from 'react'
import { FiArrowUp } from 'react-icons/fi'

const ScrollToTop = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  // Ensure component is mounted (for SSR compatibility)
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Show button when page is scrolled down
  useEffect(() => {
    // Check if we're in browser environment and component is mounted
    if (typeof window === 'undefined' || !isMounted) {
      return
    }

    const toggleVisibility = () => {
      try {
        // Use multiple fallbacks for scroll position
        const scrolled = window.scrollY ||
                        window.pageYOffset ||
                        document.documentElement.scrollTop ||
                        document.body.scrollTop || 0

        setIsVisible(scrolled > 300)
      } catch (error) {
        console.error('ScrollToTop: Error in toggleVisibility:', error)
      }
    }

    // Check initial scroll position
    toggleVisibility()

    // Use passive listener for better performance
    window.addEventListener('scroll', toggleVisibility, { passive: true })

    // Cleanup
    return () => {
      window.removeEventListener('scroll', toggleVisibility)
    }
  }, [isMounted])

  // Scroll to top smoothly
  const scrollToTop = () => {
    if (typeof window === 'undefined' || !isMounted) return

    try {
      // Try modern smooth scrolling first
      if ('scrollTo' in window && 'behavior' in document.documentElement.style) {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      } else {
        // Fallback for older browsers
        window.scrollTo(0, 0)
      }
    } catch (error) {
      // Ultimate fallback
      try {
        document.documentElement.scrollTop = 0
        document.body.scrollTop = 0
      } catch (e) {
        console.error('ScrollToTop: All scroll methods failed:', e)
      }
    }
  }

  // Don't render anything until component is mounted (prevents SSR issues)
  if (!isMounted) {
    return null
  }

  // Show button based on scroll position
  const showButton = isVisible

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '24px',
        right: '24px',
        zIndex: 9999,
        display: showButton ? 'block' : 'none',
        pointerEvents: showButton ? 'auto' : 'none'
      }}
    >
      <button
        onClick={scrollToTop}
        style={{
          backgroundColor: '#eab308',
          color: 'white',
          padding: '12px',
          borderRadius: '50%',
          border: 'none',
          cursor: 'pointer',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          width: '48px',
          height: '48px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'all 0.3s ease'
        }}
        onMouseEnter={(e) => {
          e.target.style.backgroundColor = '#ca8a04'
          e.target.style.transform = 'scale(1.1)'
        }}
        onMouseLeave={(e) => {
          e.target.style.backgroundColor = '#eab308'
          e.target.style.transform = 'scale(1)'
        }}
        aria-label="Scroll to top"
        title="Scroll to top"
      >
        <FiArrowUp style={{ width: '24px', height: '24px' }} />
        {/* Fallback text if icon doesn't load */}
        <span style={{ display: 'none' }}>↑</span>
      </button>
    </div>
  )
}

export default ScrollToTop
