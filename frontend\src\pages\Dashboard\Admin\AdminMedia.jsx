import { useState, useEffect } from 'react'
import { 
  FiImage, FiUpload, FiTrash2, FiEdit3, <PERSON><PERSON><PERSON>, FiGrid, FiList, 
  FiSearch, FiFilter, FiDownload, FiPlus, FiRefreshCw 
} from 'react-icons/fi'
import MediaLibraryModal from '../../../components/Modals/MediaLibraryModal'
import { useMediaLibrary, useMediaOperations } from '../../../hooks/useMediaLibrary'
import mediaService from '../../../services/mediaService'
import { useApiWithToast } from '../../../utils/apiWithToast'

const AdminMedia = ({ branding }) => {
  const [media, setMedia] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [viewMode, setViewMode] = useState('grid')
  const [selectedItems, setSelectedItems] = useState([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [stats, setStats] = useState({})

  const { executeWithToast } = useApiWithToast()
  const { mediaLibraryProps, openMediaLibrary } = useMediaLibrary()
  const { uploadMedia, deleteMedia } = useMediaOperations()

  // Fetch media library
  const fetchMedia = async (page = 1, search = '', mimeType = '') => {
    setLoading(true)
    try {
      const params = {
        page,
        limit: 20,
        ...(search && { search }),
        ...(mimeType && { mimeType })
      }

      const response = await mediaService.getMediaLibrary(params)
      
      if (response.success) {
        setMedia(response.data.media)
        setTotalPages(response.data.pagination.totalPages)
        setCurrentPage(response.data.pagination.currentPage)
      }
    } catch (error) {
      console.error('Error fetching media:', error)
    } finally {
      setLoading(false)
    }
  }

  // Fetch media stats
  const fetchStats = async () => {
    try {
      const response = await mediaService.getMediaStats()
      if (response.success) {
        setStats(response.data)
      }
    } catch (error) {
      console.error('Error fetching media stats:', error)
    }
  }

  // Load data on mount and when filters change
  useEffect(() => {
    const mimeTypeFilter = filterType === 'all' ? '' : 
      filterType === 'image' ? 'image/' :
      filterType === 'video' ? 'video/' :
      filterType === 'document' ? 'application/' : ''
    
    fetchMedia(1, searchTerm, mimeTypeFilter)
  }, [searchTerm, filterType])

  useEffect(() => {
    fetchStats()
  }, [])

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return
    
    if (!confirm(`Are you sure you want to delete ${selectedItems.length} item(s)?`)) return

    await executeWithToast(
      () => mediaService.bulkDeleteMedia(selectedItems.map(item => item._id)),
      {
        loadingMessage: 'Deleting media...',
        successMessage: `Successfully deleted ${selectedItems.length} item(s)`,
        errorMessage: 'Failed to delete media',
        onSuccess: () => {
          setSelectedItems([])
          fetchMedia(currentPage, searchTerm, filterType === 'all' ? '' : filterType)
          fetchStats()
        }
      }
    )
  }

  // Handle single delete
  const handleSingleDelete = async (mediaId) => {
    if (!confirm('Are you sure you want to delete this media item?')) return

    await executeWithToast(
      () => mediaService.deleteMedia(mediaId),
      {
        loadingMessage: 'Deleting media...',
        successMessage: 'Media deleted successfully',
        errorMessage: 'Failed to delete media',
        onSuccess: () => {
          fetchMedia(currentPage, searchTerm, filterType === 'all' ? '' : filterType)
          fetchStats()
        }
      }
    )
  }

  // Handle item selection
  const handleItemSelect = (item) => {
    setSelectedItems(prev => {
      const isSelected = prev.some(selected => selected._id === item._id)
      if (isSelected) {
        return prev.filter(selected => selected._id !== item._id)
      } else {
        return [...prev, item]
      }
    })
  }

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Get file type icon
  const getFileTypeIcon = (mimeType) => {
    if (mimeType.startsWith('image/')) return FiImage
    return FiImage // Default to image icon
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Media Library</h2>
          <p className="text-gray-600 mt-1">Manage all your media files and assets</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => openMediaLibrary({ allowUpload: true, multiple: true })}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
          >
            <FiUpload className="w-4 h-4 mr-2" />
            Upload Media
          </button>
          <button
            onClick={() => fetchMedia(currentPage, searchTerm, filterType === 'all' ? '' : filterType)}
            className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <FiRefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100">
                <FiImage className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Files</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalFiles || 0}</p>
              </div>
            </div>
          </div>
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100">
                <FiImage className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Images</p>
                <p className="text-2xl font-bold text-gray-900">{stats.images || 0}</p>
              </div>
            </div>
          </div>
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100">
                <FiImage className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Videos</p>
                <p className="text-2xl font-bold text-gray-900">{stats.videos || 0}</p>
              </div>
            </div>
          </div>
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-100">
                <FiImage className="w-6 h-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Storage Used</p>
                <p className="text-2xl font-bold text-gray-900">{formatFileSize(stats.totalSize || 0)}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Toolbar */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/20">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search media..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
              />
            </div>

            {/* Filter */}
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Media</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
              <option value="document">Documents</option>
            </select>
          </div>

          <div className="flex items-center space-x-3">
            {/* Bulk Actions */}
            {selectedItems.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">
                  {selectedItems.length} selected
                </span>
                <button
                  onClick={handleBulkDelete}
                  className="flex items-center px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors duration-200"
                >
                  <FiTrash2 className="w-3 h-3 mr-1" />
                  Delete
                </button>
              </div>
            )}

            {/* View Mode */}
            <div className="flex border border-gray-300 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
              >
                <FiGrid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
              >
                <FiList className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Media Grid/List */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        ) : media.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <FiImage className="w-16 h-16 mb-4" />
            <p className="text-lg font-medium">No media found</p>
            <p className="text-sm">Upload some files to get started</p>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="p-6">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {media.map((item) => {
                const isSelected = selectedItems.some(selected => selected._id === item._id)
                const FileIcon = getFileTypeIcon(item.mimeType)
                
                return (
                  <div
                    key={item._id}
                    className={`relative group cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                      isSelected 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleItemSelect(item)}
                  >
                    <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                      {item.mimeType.startsWith('image/') ? (
                        <img
                          src={item.url}
                          alt={item.alt || item.originalName}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <FileIcon className="w-8 h-8 text-gray-400" />
                        </div>
                      )}
                    </div>
                    
                    {/* File info */}
                    <div className="p-2">
                      <p className="text-xs text-gray-600 truncate" title={item.originalName}>
                        {item.originalName}
                      </p>
                      <p className="text-xs text-gray-400">
                        {formatFileSize(item.size)}
                      </p>
                    </div>
                    
                    {/* Actions */}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleSingleDelete(item._id)
                        }}
                        className="bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors duration-200"
                      >
                        <FiTrash2 className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        ) : (
          <div className="p-6">
            <div className="space-y-2">
              {media.map((item) => {
                const isSelected = selectedItems.some(selected => selected._id === item._id)
                const FileIcon = getFileTypeIcon(item.mimeType)
                
                return (
                  <div
                    key={item._id}
                    className={`flex items-center p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                      isSelected 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleItemSelect(item)}
                  >
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                      {item.mimeType.startsWith('image/') ? (
                        <img
                          src={item.url}
                          alt={item.alt || item.originalName}
                          className="w-full h-full object-cover rounded-lg"
                        />
                      ) : (
                        <FileIcon className="w-6 h-6 text-gray-400" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{item.originalName}</p>
                      <p className="text-sm text-gray-500">
                        {formatFileSize(item.size)} • {new Date(item.uploadedAt).toLocaleDateString()}
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleSingleDelete(item._id)
                        }}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => fetchMedia(currentPage - 1, searchTerm, filterType === 'all' ? '' : filterType)}
              disabled={currentPage === 1}
              className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Previous
            </button>
            <span className="px-4 py-2 text-sm text-gray-600">
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => fetchMedia(currentPage + 1, searchTerm, filterType === 'all' ? '' : filterType)}
              disabled={currentPage === totalPages}
              className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Media Library Modal */}
      <MediaLibraryModal
        {...mediaLibraryProps}
        branding={branding}
        onSelect={(selectedMedia) => {
          // Handle media selection if needed
          console.log('Selected media:', selectedMedia)
          // Refresh the media list
          fetchMedia(currentPage, searchTerm, filterType === 'all' ? '' : filterType)
          fetchStats()
        }}
      />
    </div>
  )
}

export default AdminMedia
