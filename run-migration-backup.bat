@echo off
echo 🚀 MongoDB Migration via Export/Import
echo =====================================

echo.
echo 📦 Step 1: Exporting data from source database...
node export-data.js

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Export failed. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo ✅ Export completed successfully!
echo.
echo 📥 Step 2: Importing data to target database...
node import-data.js

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Import failed. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo 🎉 Migration completed successfully!
echo 📋 Check the reports in mongodb-export folder for details
pause
