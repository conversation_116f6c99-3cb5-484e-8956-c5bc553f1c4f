import { useState, useRef } from 'react'
import { FiShoppingCart, FiStar, FiHeart, FiArrowLeft, FiCheck, FiTruck, FiAlertCircle } from 'react-icons/fi'
import { productService, userService } from '../../services'

import { useProduct } from '../../hooks/useQueries'
import { useBranding } from '../../contexts/BrandingContext'
import { useCart } from '../../contexts/CartContext'
import { useFavorites } from '../../contexts/FavoritesContext'
import Loading from '../../components/Loading'
import { Reviews } from '../../components/Reviews'

const ProductDetail = ({ productId, onNavigate }) => {
  const { branding } = useBranding()
  const { addToCart, isLoading: cartLoading } = useCart()
  const { addToFavorites, isFavorite, toggleFavorite } = useFavorites()
  const [quantity, setQuantity] = useState(1)
  const [selectedImage, setSelectedImage] = useState(0)
  const [addingToCart, setAddingToCart] = useState(false)
  const [addingToFavorites, setAddingToFavorites] = useState(false)



  // Use React Query to fetch product data
  const {
    data: product,
    isLoading,
    error
  } = useProduct(productId)

  // For now, we'll use empty arrays for reviews and related products
  // TODO: Add separate queries for reviews and related products if needed
  const reviews = []
  const relatedProducts = []

  // Calculate stock status
  const isInStock = product?.stock > 0
  const isLowStock = product?.stock > 0 && product?.stock <= 5

  const handleAddToCart = async () => {
    if (!product) return

    try {
      setAddingToCart(true)
      await addToCart(product, quantity)
    } catch (error) {
      console.error('Error adding to cart:', error)
    } finally {
      setAddingToCart(false)
    }
  }

  const handleAddToFavorites = async () => {
    if (!product) return

    try {
      setAddingToFavorites(true)
      toggleFavorite(product)
    } catch (error) {
      console.error('Error toggling favorites:', error)
    } finally {
      setAddingToFavorites(false)
    }
  }



  // Show loading state
  if (isLoading) {
    return <Loading message="Loading product details..." />
  }

  // Show error state
  if (error || !product) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <button
            onClick={() => onNavigate('shop')}
            className="inline-flex items-center text-amber-600 hover:text-amber-700 mb-8 transition-colors duration-200"
          >
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Back to Shop
          </button>

          <div className="text-center py-12">
            <FiAlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {error || 'Product not found'}
            </h2>
            <p className="text-gray-600 mb-6">
              The product you're looking for doesn't exist or has been removed.
            </p>
            <button
              onClick={() => onNavigate('shop')}
              className="bg-amber-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-amber-700 transition-colors duration-200"
            >
              Continue Shopping
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <button
            onClick={() => onNavigate('shop')}
            className="hover:text-amber-600 transition-colors duration-200"
          >
            Shop
          </button>
          <span>/</span>
          <span className="text-gray-900">{product.name}</span>
        </div>

        {/* Back Button */}
        <button
          onClick={() => onNavigate('shop')}
          className="inline-flex items-center text-amber-600 hover:text-amber-700 mb-8 transition-colors duration-200"
        >
          <FiArrowLeft className="w-4 h-4 mr-2" />
          Back to Shop
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Product Images */}
          <div>
            <div className="aspect-square bg-gray-200 rounded-2xl overflow-hidden mb-4">
              {(product.images?.[selectedImage] || product.image) ? (
                <img
                  src={product.images?.[selectedImage] || product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.style.display = 'none'
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400">No image available</span>
                </div>
              )}
            </div>
            {product.images && product.images.length > 1 && (
              <div className="grid grid-cols-4 gap-2">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`aspect-square bg-gray-200 rounded-lg overflow-hidden border-2 transition-colors duration-200 ${
                      selectedImage === index ? 'border-amber-600' : 'border-transparent'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.style.display = 'none'
                      }}
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div>
            <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              {product.name}
            </h1>

            {(product.rating || product.reviewCount || reviews.length > 0) && (
              <div className="flex items-center mb-6">
                <div className="flex text-amber-400 mr-3">
                  {[...Array(5)].map((_, i) => (
                    <FiStar
                      key={i}
                      className={`w-5 h-5 ${
                        i < Math.floor(product.rating || 0) ? 'fill-current' : ''
                      }`}
                    />
                  ))}
                </div>
                <span className="text-gray-600">
                  {product.rating?.toFixed(1) || '0.0'} ({product.reviewCount || product.reviews || reviews.length || 0} reviews)
                </span>
              </div>
            )}

            <div className="text-3xl font-bold text-gray-900 mb-6">
              ${product.price?.toFixed(2) || '0.00'}
            </div>

            <p className="text-gray-600 mb-8 leading-relaxed">
              {product.description}
            </p>

            {/* Features */}
            {product.features && product.features.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Features</h3>
                <div className="grid grid-cols-2 gap-2">
                  {product.features.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <FiCheck className="w-4 h-4 text-green-500 mr-2" />
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Quantity and Add to Cart */}
            <div className="flex items-center space-x-4 mb-8">
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-3 py-2 text-gray-600 hover:text-gray-900"
                >
                  -
                </button>
                <span className="px-4 py-2 border-x border-gray-300">{quantity}</span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="px-3 py-2 text-gray-600 hover:text-gray-900"
                >
                  +
                </button>
              </div>
              <button
                onClick={handleAddToCart}
                disabled={addingToCart || !isInStock}
                className="flex-1 text-white py-3 px-6 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ backgroundColor: branding.colors.secondary }}
                onMouseEnter={(e) => !addingToCart && (e.target.style.backgroundColor = branding.colors.accent)}
                onMouseLeave={(e) => !addingToCart && (e.target.style.backgroundColor = branding.colors.secondary)}
              >
                {addingToCart ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Adding...
                  </>
                ) : (
                  <>
                    <FiShoppingCart className="w-5 h-5 mr-2" />
                    {isInStock ? 'Add to Cart' : 'Out of Stock'}
                  </>
                )}
              </button>
              <button
                onClick={handleAddToFavorites}
                disabled={addingToFavorites}
                className={`p-3 border rounded-lg transition-colors duration-200 disabled:opacity-50 ${
                  isFavorite(product?.id || product?._id)
                    ? 'border-red-300 bg-red-50 hover:bg-red-100'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                {addingToFavorites ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-600"></div>
                ) : (
                  <FiHeart
                    className={`w-5 h-5 ${
                      isFavorite(product?.id || product?._id)
                        ? 'text-red-600 fill-current'
                        : 'text-gray-600'
                    }`}
                  />
                )}
              </button>
            </div>

            {/* Shipping Info */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-8">
              <div className="flex items-center text-green-700">
                <FiTruck className="w-5 h-5 mr-2" />
                <span className="font-medium">Free shipping on orders over $50</span>
              </div>
            </div>

            {/* Stock Status */}
            <div className={`flex items-center mb-8 ${isInStock ? (isLowStock ? 'text-yellow-600' : 'text-green-600') : 'text-red-600'}`}>
              <FiCheck className="w-5 h-5 mr-2" />
              <span className="font-medium">
                {isInStock
                  ? (isLowStock ? `Low Stock - Only ${product.stock} left` : 'In Stock - Ready to Ship')
                  : 'Out of Stock'
                }
              </span>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        {(product.howToUse || product.ingredients) && (
          <div className="bg-white rounded-2xl p-8 mb-16 shadow-lg">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {product.howToUse && product.howToUse.length > 0 && (
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">How to Use</h3>
                  <ol className="space-y-2">
                    {product.howToUse.map((step, index) => (
                      <li key={index} className="flex items-start">
                        <span className="flex-shrink-0 w-6 h-6 bg-amber-100 text-amber-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                          {index + 1}
                        </span>
                        <span className="text-gray-600">{step}</span>
                      </li>
                    ))}
                  </ol>
                </div>
              )}
              {product.ingredients && (
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Ingredients</h3>
                  <p className="text-gray-600 leading-relaxed">{product.ingredients}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Customer Reviews */}
        <div className="mb-16">
          <Reviews
            productId={productId}
            branding={branding}
            showWriteReview={true}
          />
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">You Might Also Like</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <button
                  key={relatedProduct.id}
                  onClick={() => {
                    // Navigate to the related product
                    window.location.reload() // Simple way to reload with new product
                    // In a real app, you'd update the productId prop or use routing
                  }}
                  className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 text-left"
                >
                  <div className="aspect-square bg-gray-200">
                    {(relatedProduct.image || relatedProduct.images?.[0]) ? (
                      <img
                        src={relatedProduct.image || relatedProduct.images?.[0]}
                        alt={relatedProduct.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.style.display = 'none'
                        }}
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-400 text-xs">No image</span>
                      </div>
                    )}
                  </div>
                  <div className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {relatedProduct.name}
                    </h3>
                    <span className="text-xl font-bold text-gray-900">
                      ${relatedProduct.price?.toFixed(2) || '0.00'}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ProductDetail
