import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  // Environment variables configuration
  envPrefix: 'VITE_',

  // Development server configuration
  server: {
    port: 5174,
    host: true,
    open: true
  },

  // Build configuration
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})
