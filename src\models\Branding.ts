import mongoose, { Schema, Document } from 'mongoose';

export interface IBranding extends Document {
  _id: string;
  global: {
    siteName: string;
    tagline: string;
    logo: string;
    favicon: string;
    phone: string;
    email: string;
    address: string;
    instagram: string;
    facebook: string;
    twitter: string;
    youtube: string;
  };
  home: {
    heroTitle: string;
    heroSubtitle: string;
    heroImage: string;
    aboutTitle: string;
    aboutText: string;
    featuredServices: Array<{
      title: string;
      description: string;
      image: string;
    }>;
    testimonialHeading: string;
  };
  services: {
    pageTitle: string;
    pageSubtitle: string;
    pageDescription: string;
    serviceCategories: Array<{
      name: string;
      description: string;
    }>;
    // Individual service content
    serviceLocMaintenance: string;
    serviceLocMaintenanceDesc: string;
    serviceStarterLocs: string;
    serviceStarterLocsDesc: string;
    serviceLocStyling: string;
    serviceLocStylingDesc: string;
    serviceNaturalHairCare: string;
    serviceNaturalHairCareDesc: string;
  };
  shop: {
    pageTitle: string;
    pageSubtitle: string;
    pageDescription: string;
    featuredCollectionTitle: string;
  };
  consultation: {
    pageTitle: string;
    pageSubtitle: string;
    pageDescription: string;
    formTitle: string;
    formSubtitle: string;
  };
  login: {
    pageTitle: string;
    pageSubtitle: string;
    signInButton: string;
    signingInText: string;
    noAccountText: string;
    signUpLink: string;
    forgotPasswordLink: string;
  };
  signup: {
    pageTitle: string;
    pageSubtitle: string;
    createAccountButton: string;
    creatingAccountText: string;
    haveAccountText: string;
    signInLink: string;
    agreeTermsText: string;
    termsLinkText: string;
    andText: string;
    privacyLinkText: string;
  };
  cart: {
    pageTitle: string;
    emptyCartMessage: string;
    freeShippingThreshold: string;
    shippingCalculated: string;
  };
  productDetail: {
    addToCartButton: string;
    quantityLabel: string;
    overviewTab: string;
    ingredientsTab: string;
    reviewsTab: string;
  };
  reviews: {
    sectionTitle: string;
    writeReviewButton: string;
    noReviewsMessage: string;
    formTitle: string;
    formSubtitle: string;
    ratingLabel: string;
    titleLabel: string;
    titlePlaceholder: string;
    commentLabel: string;
    commentPlaceholder: string;
    submitButton: string;
    submittingText: string;
    successMessage: string;
    pendingApprovalMessage: string;
    editReviewTitle: string;
    updateButton: string;
    updatingText: string;
    deleteConfirmMessage: string;
    verifiedPurchaseLabel: string;
    helpfulButton: string;
    reportButton: string;
    sortByLabel: string;
    sortNewest: string;
    sortOldest: string;
    sortHighestRated: string;
    sortLowestRated: string;
    filterByRating: string;
    allRatings: string;
    showMoreButton: string;
    showLessButton: string;
  };
  footer: {
    description: string;
    copyrightText: string;
    quickLinks: Array<{name: string; url: string}>;
    contact: string;
    followUs: string;
  };
  // Dashboard content
  dashboard: {
    welcomeMessage: string;
    overviewTitle: string;
    appointmentsTitle: string;
    ordersTitle: string;
    favoritesTitle: string;
    profileTitle: string;
    nextAppointment: string;
    recentOrders: string;
    loyaltyTitle: string;
  };
  // Buttons and UI elements
  buttons: {
    bookNow: string;
    shopNow: string;
    learnMore: string;
    viewAll: string;
    continueShopping: string;
    proceedToCheckout: string;
    addToCart: string;
    scheduleConsultation: string;
    writeReview: string;
  };
  // Navigation
  navigation: {
    home: string;
    services: string;
    shop: string;
    consultation: string;
    contact: string;
    login: string;
    signup: string;
    dashboard: string;
  };
  // Contact page
  contact: {
    pageTitle: string;
    pageSubtitle: string;
    pageDescription: string;
    phoneLabel: string;
    emailLabel: string;
    businessHoursLabel: string;
    businessHours: string;
    addressLabel: string;
    address: string;
    getInTouchTitle: string;
    getInTouchSubtitle: string;
  };
  // Payment confirmation
  paymentConfirmation: {
    pageTitle: string;
    pageSubtitle: string;
    pageDescription: string;
    uploadTitle: string;
    uploadSubtitle: string;
    uploadInstructions: string;
    dragDropText: string;
    browseFilesText: string;
    supportedFormats: string;
    maxFileSize: string;
    orderReferenceLabel: string;
    appointmentReferenceLabel: string;
    amountLabel: string;
    paymentMethodLabel: string;
    notesLabel: string;
    notesPlaceholder: string;
    submitButton: string;
    submittingText: string;
    successMessage: string;
    uploadingText: string;
    previewTitle: string;
    removeImageButton: string;
    retryUploadButton: string;
  };
  // Testimonials and reviews
  testimonials: {
    title: string;
    subtitle: string;
  };
  // Messages
  messages: {
    loading: string;
    error: string;
    notFound: string;
    comingSoon: string;
    cartShipping: string;
  };
  // Business information (stored in branding for admin editing)
  business: {
    name: string;
    tagline: string;
    description: string;
    phone: string;
    email: string;
    address: {
      street: string;
      city: string;
      state: string;
      zip: string;
      full: string;
    };
    social: {
      instagram: string;
      facebook: string;
      twitter: string;
    };
    hours: {
      monday: string;
      tuesday: string;
      wednesday: string;
      thursday: string;
      friday: string;
      saturday: string;
      sunday: string;
    };
  };
  // Theme settings (stored in branding for admin editing)
  theme: {
    colors: {
      primary: string;
      secondary: string;
      accent: string;
      background: string;
      text: string;
      textSecondary: string;
    };
    fonts: {
      primary: string;
      secondary: string;
      heading: string;
    };
  };
  // SEO and site settings (stored in branding for admin editing)
  site: {
    seo: {
      title: string;
      description: string;
      keywords: string;
    };
    features: {
      onlineBooking: boolean;
      ecommerce: boolean;
      loyaltyProgram: boolean;
      giftCards: boolean;
      reviews: boolean;
      blog: boolean;
    };
  };
  // Legal content (Privacy Policy and Terms of Service)
  legal: {
    privacyPolicy: {
      title: string;
      content: string;
      lastUpdated: Date;
    };
    termsOfService: {
      title: string;
      content: string;
      lastUpdated: Date;
    };
    copyrightText: string;
    companyName: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const brandingSchema = new Schema<IBranding>({
  global: {
    siteName: { type: String },
    tagline: { type: String },
    logo: { type: String },
    favicon: { type: String },
    phone: { type: String },
    email: { type: String },
    address: { type: String },
    instagram: { type: String },
    facebook: { type: String },
    twitter: { type: String },
    youtube: { type: String }
  },
  home: {
    heroTitle: { type: String },
    heroSubtitle: { type: String },
    heroImage: { type: String },
    aboutTitle: { type: String },
    aboutText: { type: String },
    featuredServices: [{
      title: { type: String },
      description: { type: String },
      image: { type: String }
    }],
    testimonialHeading: { type: String }
  },
  services: {
    pageTitle: { type: String },
    pageSubtitle: { type: String },
    pageDescription: { type: String },
    serviceCategories: [{
      name: { type: String },
      description: { type: String }
    }],
    serviceLocMaintenance: { type: String },
    serviceLocMaintenanceDesc: { type: String },
    serviceStarterLocs: { type: String },
    serviceStarterLocsDesc: { type: String },
    serviceLocStyling: { type: String },
    serviceLocStylingDesc: { type: String },
    serviceNaturalHairCare: { type: String },
    serviceNaturalHairCareDesc: { type: String }
  },
  shop: {
    pageTitle: { type: String },
    pageSubtitle: { type: String },
    pageDescription: { type: String },
    featuredCollectionTitle: { type: String }
  },
  consultation: {
    pageTitle: { type: String },
    pageSubtitle: { type: String },
    pageDescription: { type: String },
    formTitle: { type: String },
    formSubtitle: { type: String }
  },
  login: {
    pageTitle: { type: String },
    pageSubtitle: { type: String },
    signInButton: { type: String },
    signingInText: { type: String },
    noAccountText: { type: String },
    signUpLink: { type: String },
    forgotPasswordLink: { type: String }
  },
  signup: {
    pageTitle: { type: String },
    pageSubtitle: { type: String },
    createAccountButton: { type: String },
    creatingAccountText: { type: String },
    haveAccountText: { type: String },
    signInLink: { type: String },
    agreeTermsText: { type: String },
    termsLinkText: { type: String },
    andText: { type: String },
    privacyLinkText: { type: String }
  },
  cart: {
    pageTitle: { type: String },
    emptyCartMessage: { type: String },
    freeShippingThreshold: { type: String },
    shippingCalculated: { type: String }
  },
  productDetail: {
    addToCartButton: { type: String },
    quantityLabel: { type: String },
    overviewTab: { type: String },
    ingredientsTab: { type: String },
    reviewsTab: { type: String }
  },
  footer: {
    description: { type: String },
    copyrightText: { type: String },
    quickLinks: {
      type: [{
        name: { type: String },
        url: { type: String }
      }],
      required: true
    },
    contact: { type: String },
    followUs: { type: String }
  },
  // Dashboard content
  dashboard: {
    welcomeMessage: { type: String },
    overviewTitle: { type: String },
    appointmentsTitle: { type: String },
    ordersTitle: { type: String },
    favoritesTitle: { type: String },
    profileTitle: { type: String },
    nextAppointment: { type: String },
    recentOrders: { type: String },
    loyaltyTitle: { type: String }
  },
  // Buttons and UI elements
  buttons: {
    bookNow: { type: String },
    shopNow: { type: String },
    learnMore: { type: String },
    viewAll: { type: String },
    continueShopping: { type: String },
    proceedToCheckout: { type: String },
    addToCart: { type: String },
    scheduleConsultation: { type: String },
    writeReview: { type: String }
  },
  // Navigation
  navigation: {
    home: { type: String },
    services: { type: String },
    shop: { type: String },
    consultation: { type: String },
    contact: { type: String },
    login: { type: String },
    signup: { type: String },
    dashboard: { type: String }
  },
  // Contact page
  contact: {
    pageTitle: { type: String },
    pageSubtitle: { type: String },
    pageDescription: { type: String },
    phoneLabel: { type: String },
    emailLabel: { type: String },
    businessHoursLabel: { type: String },
    businessHours: { type: String },
    addressLabel: { type: String },
    address: { type: String },
    getInTouchTitle: { type: String },
    getInTouchSubtitle: { type: String }
  },
  // Testimonials and reviews
  testimonials: {
    title: { type: String },
    subtitle: { type: String }
  },
  reviews: {
    sectionTitle: { type: String },
    writeReviewButton: { type: String },
    noReviewsMessage: { type: String },
    formTitle: { type: String },
    formSubtitle: { type: String },
    ratingLabel: { type: String },
    titleLabel: { type: String },
    titlePlaceholder: { type: String },
    commentLabel: { type: String },
    commentPlaceholder: { type: String },
    submitButton: { type: String },
    submittingText: { type: String },
    successMessage: { type: String },
    pendingApprovalMessage: { type: String },
    editReviewTitle: { type: String },
    updateButton: { type: String },
    updatingText: { type: String },
    deleteConfirmMessage: { type: String },
    verifiedPurchaseLabel: { type: String },
    helpfulButton: { type: String },
    reportButton: { type: String },
    sortByLabel: { type: String },
    sortNewest: { type: String },
    sortOldest: { type: String },
    sortHighestRated: { type: String },
    sortLowestRated: { type: String },
    filterByRating: { type: String },
    allRatings: { type: String },
    showMoreButton: { type: String },
    showLessButton: { type: String }
  },
  // Payment confirmation
  paymentConfirmation: {
    pageTitle: { type: String },
    pageSubtitle: { type: String },
    pageDescription: { type: String },
    uploadTitle: { type: String },
    uploadSubtitle: { type: String },
    uploadInstructions: { type: String },
    dragDropText: { type: String },
    browseFilesText: { type: String },
    supportedFormats: { type: String },
    maxFileSize: { type: String },
    orderReferenceLabel: { type: String },
    appointmentReferenceLabel: { type: String },
    amountLabel: { type: String },
    paymentMethodLabel: { type: String },
    notesLabel: { type: String },
    notesPlaceholder: { type: String },
    submitButton: { type: String },
    submittingText: { type: String },
    successMessage: { type: String },
    uploadingText: { type: String },
    previewTitle: { type: String },
    removeImageButton: { type: String },
    retryUploadButton: { type: String }
  },
  // Messages
  messages: {
    loading: { type: String },
    error: { type: String },
    notFound: { type: String },
    comingSoon: { type: String },
    cartShipping: { type: String }
  },
  // Business information (stored in branding for admin editing)
  business: {
    name: { type: String },
    tagline: { type: String },
    description: { type: String },
    phone: { type: String },
    email: { type: String },
    address: {
      street: { type: String },
      city: { type: String },
      state: { type: String },
      zip: { type: String },
      full: { type: String }
    },
    social: {
      instagram: { type: String },
      facebook: { type: String },
      twitter: { type: String }
    },
    hours: {
      monday: { type: String },
      tuesday: { type: String },
      wednesday: { type: String },
      thursday: { type: String },
      friday: { type: String },
      saturday: { type: String },
      sunday: { type: String }
    }
  },
  // Theme settings (stored in branding for admin editing)
  theme: {
    colors: {
      primary: {
        type: String,
        validate: {
          validator: function(v: string) {
            return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
          },
          message: 'Primary color must be a valid hex color (e.g., #008000 or #080)'
        }
      },
      secondary: {
        type: String,
        validate: {
          validator: function(v: string) {
            return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
          },
          message: 'Secondary color must be a valid hex color (e.g., #f3d016 or #f30)'
        }
      },
      accent: {
        type: String,
        validate: {
          validator: function(v: string) {
            return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
          },
          message: 'Accent color must be a valid hex color (e.g., #006600 or #060)'
        }
      },
      background: {
        type: String,
        validate: {
          validator: function(v: string) {
            return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
          },
          message: 'Background color must be a valid hex color (e.g., #ffffff or #fff)'
        }
      },
      text: {
        type: String,
        validate: {
          validator: function(v: string) {
            return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
          },
          message: 'Text color must be a valid hex color (e.g., #000000 or #000)'
        }
      },
      textSecondary: {
        type: String,
        validate: {
          validator: function(v: string) {
            return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(v);
          },
          message: 'Text secondary color must be a valid hex color (e.g., #666666 or #666)'
        }
      }
    },
    fonts: {
      primary: { type: String },
      secondary: { type: String },
      heading: { type: String }
    }
  },
  // SEO and site settings (stored in branding for admin editing)
  site: {
    seo: {
      title: { type: String },
      description: { type: String },
      keywords: { type: String }
    },
    features: {
      onlineBooking: { type: Boolean },
      ecommerce: { type: Boolean },
      loyaltyProgram: { type: Boolean },
      giftCards: { type: Boolean },
      reviews: { type: Boolean },
      blog: { type: Boolean }
    }
  },
  // Legal content (Privacy Policy and Terms of Service)
  legal: {
    privacyPolicy: {
      title: { type: String },
      content: { type: String },
      lastUpdated: { type: Date }
    },
    termsOfService: {
      title: { type: String },
      content: { type: String },
      lastUpdated: { type: Date }
    },
    copyrightText: { type: String },
    companyName: { type: String }
  }
}, {
  timestamps: true
});

export const Branding = mongoose.model<IBranding>('Branding', brandingSchema);
