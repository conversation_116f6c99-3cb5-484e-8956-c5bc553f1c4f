
import { FiInstagram, FiFacebook, FiTwitter, FiMail, FiPhone, FiMapPin } from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'

const Footer = ({ onNavigate }) => {
  const { branding, isLoading } = useBranding()

  // Debug logging (temporarily disabled)
  // console.log('Footer branding.address:', branding.address, typeof branding.address)

  // Helper function to safely render address
  const renderAddress = (address) => {
    try {
      if (typeof address === 'string') {
        return address;
      }
      if (address && typeof address === 'object') {
        // Use the full address if available, otherwise construct it
        if (address.full) {
          return address.full;
        }
        // Construct address from parts
        const parts = [
          address.street,
          address.city,
          address.state,
          address.zip
        ].filter(Boolean); // Remove empty/null/undefined values

        return parts.length > 0 ? parts.join(', ') : '123 Beauty Street, Atlanta, GA 30309';
      }
      return '123 Beauty Street, Atlanta, GA 30309'; // Fallback
    } catch (error) {
      console.error('Error rendering address:', error);
      return '123 Beauty Street, Atlanta, GA 30309'; // Safe fallback
    }
  }

  // Show loading state if branding is not available
  if (isLoading || !branding) {
    return (
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center text-gray-300">
            <div className="animate-pulse">Loading footer content...</div>
          </div>
        </div>
      </footer>
    )
  }

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="col-span-1 lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="text-2xl font-bold" style={{ color: branding.colors.secondary }}>
                {branding.businessName}
              </div>
              {branding.tagline && (
                <div className="text-sm text-gray-300">
                  {branding.tagline}
                </div>
              )}
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              {branding.description}
            </p>
            <div className="flex space-x-4">
              {branding.social?.instagram && (
                <a
                  href={branding.social.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 transition-colors duration-200"
                  style={{ color: branding.colors.secondary }}
                  onMouseEnter={(e) => e.target.style.color = branding.colors.accent}
                  onMouseLeave={(e) => e.target.style.color = branding.colors.secondary}
                >
                  <FiInstagram className="w-6 h-6" />
                </a>
              )}
              {branding.social?.facebook && (
                <a
                  href={branding.social.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 transition-colors duration-200"
                  style={{ color: branding.colors.secondary }}
                  onMouseEnter={(e) => e.target.style.color = branding.colors.accent}
                  onMouseLeave={(e) => e.target.style.color = branding.colors.secondary}
                >
                  <FiFacebook className="w-6 h-6" />
                </a>
              )}
              {branding.social?.twitter && (
                <a
                  href={branding.social.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 transition-colors duration-200"
                  style={{ color: branding.colors.secondary }}
                  onMouseEnter={(e) => e.target.style.color = branding.colors.accent}
                  onMouseLeave={(e) => e.target.style.color = branding.colors.secondary}
                >
                  <FiTwitter className="w-6 h-6" />
                </a>
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4" style={{ color: branding.colors.secondary }}>Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <button
                  onClick={() => onNavigate('home')}
                  className="text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Home
                </button>
              </li>
              <li>
                <button
                  onClick={() => onNavigate('services')}
                  className="text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Services
                </button>
              </li>
              <li>
                <button
                  onClick={() => onNavigate('consultation')}
                  className="text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Book Consultation
                </button>
              </li>
              <li>
                <button
                  onClick={() => onNavigate('shop')}
                  className="text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Shop
                </button>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4" style={{ color: branding.colors.secondary }}>Contact</h3>
            <ul className="space-y-3">
              <li className="flex items-center space-x-2">
                <FiPhone className="w-4 h-4" style={{ color: branding.colors.secondary }} />
                <span className="text-gray-300">{branding.phone}</span>
              </li>
              <li className="flex items-center space-x-2">
                <FiMail className="w-4 h-4" style={{ color: branding.colors.secondary }} />
                <span className="text-gray-300">{branding.email}</span>
              </li>
              <li className="flex items-start space-x-2">
                <FiMapPin className="w-4 h-4 mt-1" style={{ color: branding.colors.secondary }} />
                <span className="text-gray-300">
                  {renderAddress(branding.address)}
                </span>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              {branding.legal?.copyrightText || `© 2024 ${branding.business?.name || branding.businessName}. All rights reserved.`}
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <button
                onClick={() => onNavigate('privacy-policy')}
                className="text-gray-400 hover:text-white text-sm transition-colors duration-200"
              >
                Privacy Policy
              </button>
              <button
                onClick={() => onNavigate('terms-of-service')}
                className="text-gray-400 hover:text-white text-sm transition-colors duration-200"
              >
                Terms of Service
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
