const { MongoClient } = require('mongodb');

// Database connection strings
const SOURCE_URI = 'mongodb+srv://inspiredolajosh:<EMAIL>/MicrolocsHq?retryWrites=true&w=majority';
const TARGET_URI = 'mongodb+srv://dammyspicy4:<EMAIL>/MicrolocsHq?retryWrites=true&w=majority';

// Collections to migrate (only the important ones with data)
const COLLECTIONS_TO_MIGRATE = [
  'users',
  'services', 
  'appointments',
  'products',
  'categories',
  'paymentconfirmations',
  'brandings',
  'emailtemplates',
  'reviews',
  'carts'
];

async function migrateDatabase() {
  let sourceClient, targetClient;
  
  try {
    console.log('🚀 Starting MongoDB Migration');
    console.log('==============================');
    
    // Connect to source database with timeout and retry options
    console.log('\n🔗 Connecting to source database...');
    sourceClient = new MongoClient(SOURCE_URI, {
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
      socketTimeoutMS: 10000,
      maxPoolSize: 1
    });
    
    await sourceClient.connect();
    console.log('✅ Connected to source database');
    
    // Connect to target database
    console.log('\n🔗 Connecting to target database...');
    targetClient = new MongoClient(TARGET_URI, {
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
      socketTimeoutMS: 10000,
      maxPoolSize: 1
    });
    
    await targetClient.connect();
    console.log('✅ Connected to target database');
    
    const sourceDb = sourceClient.db('MicrolocsHq');
    const targetDb = targetClient.db('MicrolocsHq');
    
    // Get available collections from source
    const collections = await sourceDb.listCollections().toArray();
    const availableCollections = collections.map(col => col.name);
    
    console.log(`\n📋 Found ${availableCollections.length} collections in source database`);
    
    // Filter collections to migrate
    const collectionsToMigrate = COLLECTIONS_TO_MIGRATE.filter(col => 
      availableCollections.includes(col)
    );
    
    console.log(`\n📦 Will migrate ${collectionsToMigrate.length} collections:`);
    
    let totalDocuments = 0;
    let migratedCollections = 0;
    
    // Migrate each collection
    for (const collectionName of collectionsToMigrate) {
      try {
        console.log(`\n📄 Processing: ${collectionName}`);
        
        const sourceCollection = sourceDb.collection(collectionName);
        const targetCollection = targetDb.collection(collectionName);
        
        // Get document count
        const count = await sourceCollection.countDocuments();
        console.log(`   📊 Documents: ${count}`);
        
        if (count === 0) {
          console.log(`   ⚠️  Empty collection, skipping...`);
          continue;
        }
        
        // Get all documents
        const documents = await sourceCollection.find({}).toArray();
        
        // Clear target collection
        await targetCollection.deleteMany({});
        
        // Insert documents in batches
        if (documents.length > 0) {
          const batchSize = 100;
          for (let i = 0; i < documents.length; i += batchSize) {
            const batch = documents.slice(i, i + batchSize);
            await targetCollection.insertMany(batch, { ordered: false });
          }
        }
        
        console.log(`   ✅ Migrated ${documents.length} documents`);
        totalDocuments += documents.length;
        migratedCollections++;
        
      } catch (error) {
        console.log(`   ❌ Failed to migrate ${collectionName}: ${error.message}`);
      }
    }
    
    // Create essential indexes
    console.log('\n🔧 Creating indexes...');
    try {
      await targetDb.collection('users').createIndex({ email: 1 }, { unique: true });
      await targetDb.collection('appointments').createIndex({ user: 1 });
      await targetDb.collection('appointments').createIndex({ service: 1 });
      await targetDb.collection('products').createIndex({ name: 1 });
      console.log('✅ Indexes created');
    } catch (error) {
      console.log('⚠️  Some indexes may already exist');
    }
    
    console.log('\n🎉 Migration Summary:');
    console.log('====================');
    console.log(`✅ Collections migrated: ${migratedCollections}`);
    console.log(`📄 Total documents: ${totalDocuments}`);
    console.log(`🎯 Target database: MicrolocsHq`);
    
    console.log('\n✅ Migration completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    
    if (error.message.includes('ESERVFAIL') || error.message.includes('ENOTFOUND')) {
      console.log('\n💡 Troubleshooting tips:');
      console.log('   1. Check your internet connection');
      console.log('   2. Verify MongoDB Atlas cluster is running');
      console.log('   3. Check if IP address is whitelisted in Atlas');
      console.log('   4. Verify database credentials are correct');
    }
    
    process.exit(1);
  } finally {
    // Close connections
    if (sourceClient) {
      await sourceClient.close();
      console.log('🔌 Disconnected from source database');
    }
    if (targetClient) {
      await targetClient.close();
      console.log('🔌 Disconnected from target database');
    }
  }
}

// Run migration
console.log('MongoDB Database Migration Tool');
console.log('===============================');
console.log('This will copy data from your current database to the new one.');
console.log('');

migrateDatabase().catch(console.error);
