import { useState, useEffect, useCallback } from 'react';
import { FiPlus, FiFilter } from 'react-icons/fi';
import ReviewForm from './ReviewForm';
import ReviewList from './ReviewList';
import ReviewStats from './ReviewStats';
import { authService, apiService } from '../../services';
import { useToast } from '../../contexts/ToastContext';

const Reviews = ({
  productId = null,
  serviceId = null,
  branding,
  showWriteReview = true
}) => {
  const { showError } = useToast();
  const [user, setUser] = useState(null);
  
  const [reviews, setReviews] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingReview, setEditingReview] = useState(null);
  const [sortBy, setSortBy] = useState('newest');
  const [filterRating, setFilterRating] = useState('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);

  // Load current user
  useEffect(() => {
    const loadUser = async () => {
      if (authService.isAuthenticated()) {
        try {
          const currentUser = await authService.getCurrentUser();
          setUser(currentUser);
        } catch (error) {
          console.error('Failed to load user:', error);
        }
      }
    };
    loadUser();
  }, []);

  // Fetch reviews
  const fetchReviews = useCallback(async (resetPage = false) => {
    try {
      setLoading(true);
      const currentPage = resetPage ? 1 : page;

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10'
      });

      if (productId) params.append('product', productId);
      if (serviceId) params.append('service', serviceId);

      const response = await apiService.get(`/reviews?${params}`);

      if (response.success) {
        if (resetPage) {
          setReviews(response.data.reviews);
          setPage(1);
        } else {
          setReviews(prev => [...prev, ...response.data.reviews]);
        }
        setHasMore(response.data.hasMore);
      }
    } catch (error) {
      showError('Failed to load reviews');
    } finally {
      setLoading(false);
    }
  }, [productId, serviceId, page, showError]);

  // Fetch review statistics
  const fetchStats = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (productId) params.append('product', productId);
      if (serviceId) params.append('service', serviceId);
      
      const response = await apiService.get(`/reviews/stats?${params}`);
      
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Failed to load review stats:', error);
    }
  }, [productId, serviceId]);

  useEffect(() => {
    fetchReviews(true);
    fetchStats();
  }, [fetchReviews, fetchStats]);

  // Handle review submission
  const handleSubmitReview = async (reviewData) => {
    try {
      const response = await apiService.post('/reviews', reviewData);
      
      if (response.success) {
        setShowForm(false);
        fetchReviews(true);
        fetchStats();
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to submit review');
    }
  };

  // Handle review update
  const handleUpdateReview = async (reviewData) => {
    try {
      const response = await apiService.put(`/reviews/${editingReview._id}`, reviewData);
      
      if (response.success) {
        setEditingReview(null);
        fetchReviews(true);
        fetchStats();
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update review');
    }
  };

  // Handle review deletion
  const handleDeleteReview = async (reviewId) => {
    try {
      const response = await apiService.delete(`/reviews/${reviewId}`);
      
      if (response.success) {
        fetchReviews(true);
        fetchStats();
      }
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete review');
    }
  };

  // Load more reviews
  const loadMore = () => {
    if (hasMore && !loading) {
      setPage(prev => prev + 1);
      fetchReviews();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">
          {branding?.reviews?.sectionTitle || 'Customer Reviews'}
        </h2>
        
        {showWriteReview && user && !showForm && !editingReview && (
          <button
            onClick={() => setShowForm(true)}
            className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-xl hover:from-blue-700 hover:to-purple-700 focus:ring-4 focus:ring-blue-300 transition-all duration-200"
          >
            <FiPlus className="w-4 h-4 mr-2" />
            {branding?.reviews?.writeReviewButton || 'Write a Review'}
          </button>
        )}
      </div>

      {/* Review Stats */}
      <ReviewStats stats={stats} branding={branding} />

      {/* Review Form */}
      {(showForm || editingReview) && user && (
        <ReviewForm
          onSubmit={editingReview ? handleUpdateReview : handleSubmitReview}
          onCancel={() => {
            setShowForm(false);
            setEditingReview(null);
          }}
          branding={branding}
          productId={productId}
          serviceId={serviceId}
          initialData={editingReview}
          isEditing={!!editingReview}
        />
      )}

      {/* Filters and Sorting */}
      {reviews.length > 0 && (
        <div className="flex items-center justify-between bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <FiFilter className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                {branding?.reviews?.sortByLabel || 'Sort by:'}
              </span>
            </div>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="newest">{branding?.reviews?.sortNewest || 'Newest'}</option>
              <option value="oldest">{branding?.reviews?.sortOldest || 'Oldest'}</option>
              <option value="highest">{branding?.reviews?.sortHighestRated || 'Highest Rated'}</option>
              <option value="lowest">{branding?.reviews?.sortLowestRated || 'Lowest Rated'}</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700">
              {branding?.reviews?.filterByRating || 'Filter by Rating:'}
            </span>
            <select
              value={filterRating}
              onChange={(e) => setFilterRating(e.target.value)}
              className="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">{branding?.reviews?.allRatings || 'All Ratings'}</option>
              <option value="5">5 Stars</option>
              <option value="4">4 Stars</option>
              <option value="3">3 Stars</option>
              <option value="2">2 Stars</option>
              <option value="1">1 Star</option>
            </select>
          </div>
        </div>
      )}

      {/* Review List */}
      <ReviewList
        reviews={reviews}
        branding={branding}
        currentUser={user}
        onEditReview={setEditingReview}
        onDeleteReview={handleDeleteReview}
        showActions={true}
        loading={loading && page === 1}
      />

      {/* Load More Button */}
      {hasMore && !loading && (
        <div className="text-center">
          <button
            onClick={loadMore}
            className="px-6 py-3 bg-white/80 backdrop-blur-sm text-gray-700 font-medium rounded-xl hover:bg-white/90 focus:ring-4 focus:ring-blue-300 transition-all duration-200 shadow-lg border border-white/20"
          >
            {branding?.reviews?.showMoreButton || 'Show More Reviews'}
          </button>
        </div>
      )}

      {/* Loading indicator for pagination */}
      {loading && page > 1 && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        </div>
      )}
    </div>
  );
};

export default Reviews;
