import { useState, useEffect } from 'react';
import { type AdminAppointment } from '../../utils/api';
import Modal from '../Modal';

interface AppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
  appointment?: AdminAppointment | null;
  mode: 'create' | 'edit' | 'view';
}

export default function AppointmentModal({
  isOpen,
  onClose,
  onSave,
  appointment,
  mode
}: AppointmentModalProps) {
  const [formData, setFormData] = useState({
    userId: '',
    service: '',
    date: '',
    time: '',
    customerInfo: {
      name: '',
      email: '',
      phone: ''
    },
    message: '',
    status: 'pending'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  // Initialize form data when appointment changes
  useEffect(() => {
    if (appointment && (mode === 'edit' || mode === 'view')) {
      setFormData({
        userId: appointment.user?.id || '',
        service: appointment.service?.id || '',
        date: appointment.date.split('T')[0], // Convert to YYYY-MM-DD format
        time: appointment.time,
        customerInfo: {
          name: appointment.customerInfo.name || appointment.user?.name || '',
          email: appointment.customerInfo.email || appointment.user?.email || '',
          phone: appointment.customerInfo.phone || appointment.user?.phone || ''
        },
        message: appointment.message || '',
        status: appointment.status
      });
    } else if (mode === 'create') {
      // Reset form for create mode
      setFormData({
        userId: '',
        service: '',
        date: '',
        time: '',
        customerInfo: {
          name: '',
          email: '',
          phone: ''
        },
        message: '',
        status: 'pending'
      });
    }
    setErrors({});
  }, [appointment, mode]);

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('customerInfo.')) {
      const customerField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        customerInfo: {
          ...prev.customerInfo,
          [customerField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.service) {
      newErrors.service = 'Service is required';
    }

    if (!formData.date) {
      newErrors.date = 'Date is required';
    }

    if (!formData.time) {
      newErrors.time = 'Time is required';
    }

    if (!formData.customerInfo.name) {
      newErrors['customerInfo.name'] = 'Customer name is required';
    }

    if (!formData.customerInfo.email) {
      newErrors['customerInfo.email'] = 'Customer email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.customerInfo.email)) {
      newErrors['customerInfo.email'] = 'Please enter a valid email address';
    }

    if (!formData.customerInfo.phone) {
      newErrors['customerInfo.phone'] = 'Customer phone is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (mode === 'view') {
      onClose();
      return;
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const submitData = {
        ...formData,
        userId: formData.userId || undefined
      };

      await onSave(submitData);
    } catch (error) {
      console.error('Error saving appointment:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get modal title
  const getModalTitle = () => {
    switch (mode) {
      case 'create': return 'Create New Appointment';
      case 'edit': return 'Edit Appointment';
      case 'view': return 'Appointment Details';
      default: return 'Appointment';
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const isReadOnly = mode === 'view';

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={getModalTitle()}>
      <form onSubmit={handleSubmit} className="appointment-form">
        {/* Service Selection */}
        <div className="form-group">
          <label htmlFor="service">Service *</label>
          {isReadOnly ? (
            <div className="form-display-value">
              {appointment?.service?.name || 'Service Unavailable'} - ${appointment?.service?.price || '0'}
            </div>
          ) : (
            <select
              id="service"
              value={formData.service}
              onChange={(e) => handleInputChange('service', e.target.value)}
              className={`form-input ${errors.service ? 'error' : ''}`}
              required
            >
              <option value="">Select a service</option>
              {/* TODO: Load services from API */}
              <option value="service1">Hair Styling - $50</option>
              <option value="service2">Makeup - $75</option>
              <option value="service3">Nail Art - $30</option>
            </select>
          )}
          {errors.service && <span className="error-text">{errors.service}</span>}
        </div>

        {/* Date and Time */}
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="date">Date *</label>
            {isReadOnly ? (
              <div className="form-display-value">
                {appointment && formatDate(appointment.date)}
              </div>
            ) : (
              <input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                className={`form-input ${errors.date ? 'error' : ''}`}
                min={new Date().toISOString().split('T')[0]}
                required
              />
            )}
            {errors.date && <span className="error-text">{errors.date}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="time">Time *</label>
            {isReadOnly ? (
              <div className="form-display-value">{appointment?.time}</div>
            ) : (
              <input
                id="time"
                type="time"
                value={formData.time}
                onChange={(e) => handleInputChange('time', e.target.value)}
                className={`form-input ${errors.time ? 'error' : ''}`}
                required
              />
            )}
            {errors.time && <span className="error-text">{errors.time}</span>}
          </div>
        </div>

        {/* Customer Information */}
        <div className="form-section">
          <h4>Customer Information</h4>
          
          <div className="form-group">
            <label htmlFor="customerName">Name *</label>
            {isReadOnly ? (
              <div className="form-display-value">{appointment?.customerInfo.name}</div>
            ) : (
              <input
                id="customerName"
                type="text"
                value={formData.customerInfo.name}
                onChange={(e) => handleInputChange('customerInfo.name', e.target.value)}
                className={`form-input ${errors['customerInfo.name'] ? 'error' : ''}`}
                placeholder="Enter customer name"
                required
              />
            )}
            {errors['customerInfo.name'] && <span className="error-text">{errors['customerInfo.name']}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="customerEmail">Email *</label>
            {isReadOnly ? (
              <div className="form-display-value">{appointment?.customerInfo.email}</div>
            ) : (
              <input
                id="customerEmail"
                type="email"
                value={formData.customerInfo.email}
                onChange={(e) => handleInputChange('customerInfo.email', e.target.value)}
                className={`form-input ${errors['customerInfo.email'] ? 'error' : ''}`}
                placeholder="Enter customer email"
                required
              />
            )}
            {errors['customerInfo.email'] && <span className="error-text">{errors['customerInfo.email']}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="customerPhone">Phone *</label>
            {isReadOnly ? (
              <div className="form-display-value">{appointment?.customerInfo.phone}</div>
            ) : (
              <input
                id="customerPhone"
                type="tel"
                value={formData.customerInfo.phone}
                onChange={(e) => handleInputChange('customerInfo.phone', e.target.value)}
                className={`form-input ${errors['customerInfo.phone'] ? 'error' : ''}`}
                placeholder="Enter customer phone"
                required
              />
            )}
            {errors['customerInfo.phone'] && <span className="error-text">{errors['customerInfo.phone']}</span>}
          </div>
        </div>

        {/* Status (for edit mode) */}
        {mode === 'edit' && (
          <div className="form-group">
            <label htmlFor="status">Status</label>
            <select
              id="status"
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value)}
              className="form-input"
            >
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        )}

        {/* Message/Notes */}
        <div className="form-group">
          <label htmlFor="message">Notes</label>
          {isReadOnly ? (
            <div className="form-display-value">
              {appointment?.message || 'No notes provided'}
            </div>
          ) : (
            <textarea
              id="message"
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              className="form-textarea"
              placeholder="Enter any additional notes or special requests"
              rows={3}
            />
          )}
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          <button
            type="button"
            onClick={onClose}
            className="btn btn-outline"
          >
            {mode === 'view' ? 'Close' : 'Cancel'}
          </button>
          
          {!isReadOnly && (
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? 'Saving...' : mode === 'create' ? 'Create Appointment' : 'Update Appointment'}
            </button>
          )}
        </div>
      </form>
    </Modal>
  );
}
