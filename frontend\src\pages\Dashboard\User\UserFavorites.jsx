import { useState } from 'react'
import { FiHeart, FiShoppingCart, FiEye, FiTrash2, FiGrid, FiList, FiFilter, FiSearch } from 'react-icons/fi'
import { useBranding } from '../../../contexts/BrandingContext'

const UserFavorites = ({
  favoriteProducts = [],
  sectionLoading = { favorites: false },
  setViewingItem = () => {},
  setModalType = () => {},
  setEditingItem = () => {},
  handleRemoveFromFavorites = () => {},
  handleAddToCart = () => {}
}) => {
  const { branding } = useBranding()
  const [viewMode, setViewMode] = useState('grid') // 'grid' or 'list'
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')

  // Ensure favoriteProducts is always an array
  const safeProducts = Array.isArray(favoriteProducts) ? favoriteProducts : []

  const categories = [...new Set(safeProducts.map(product => product.category).filter(Boolean))]

  const filteredProducts = safeProducts.filter(product => {
    const matchesSearch = !searchTerm || 
      (product.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (product.description || '').toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter
    
    return matchesSearch && matchesCategory
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">My Favorites</h2>
          <p className="text-gray-600 mt-1">Products you've saved for later</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors duration-200 ${
                viewMode === 'grid' ? 'bg-white shadow-sm text-gray-900' : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <FiGrid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors duration-200 ${
                viewMode === 'list' ? 'bg-white shadow-sm text-gray-900' : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <FiList className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search your favorites..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            />
          </div>
          {categories.length > 0 && (
            <div className="relative">
              <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent appearance-none bg-white"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Favorites Count */}
      <div className="bg-pink-50 border border-pink-200 rounded-xl p-4">
        <div className="flex items-center">
          <FiHeart className="w-5 h-5 text-pink-600 mr-2" />
          <span className="text-pink-800 font-medium">
            {filteredProducts.length} favorite{filteredProducts.length !== 1 ? 's' : ''} found
            {searchTerm || categoryFilter !== 'all' ? ` (filtered from ${favoriteProducts.length} total)` : ''}
          </span>
        </div>
      </div>

      {/* Products Display */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        {sectionLoading.favorites ? (
          <div className="p-6">
            <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
              {[...Array(6)].map((_, index) => (
                <div key={index} className={`animate-pulse ${viewMode === 'grid' ? 'space-y-4' : 'flex items-center space-x-4 p-4 bg-gray-50 rounded-xl'}`}>
                  <div className={`bg-gray-300 rounded-lg ${viewMode === 'grid' ? 'h-48 w-full' : 'w-20 h-20'}`}></div>
                  <div className={`space-y-2 ${viewMode === 'list' ? 'flex-1' : ''}`}>
                    <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-300 rounded w-1/4"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : filteredProducts.length > 0 ? (
          <div className="p-6">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredProducts.map((product, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-200 group">
                    <div className="relative">
                      <div className="aspect-w-16 aspect-h-12 bg-gray-100">
                        {product.image ? (
                          <img 
                            src={product.image} 
                            alt={product.name}
                            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
                          />
                        ) : (
                          <div className="w-full h-48 flex items-center justify-center bg-gray-100">
                            <FiHeart className="w-12 h-12 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <button
                        onClick={() => handleRemoveFromFavorites(product.id || index)}
                        className="absolute top-3 right-3 p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white transition-colors duration-200 group"
                      >
                        <FiHeart className="w-4 h-4 text-pink-600 fill-current" />
                      </button>
                    </div>
                    
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{product.name}</h3>
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.description}</p>
                      
                      <div className="flex items-center justify-between mb-4">
                        <span className="text-lg font-bold text-gray-900">${product.price?.toFixed(2) || '0.00'}</span>
                        {product.category && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                            {product.category}
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleAddToCart(product)}
                          className="flex-1 flex items-center justify-center px-3 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 cursor-pointer text-sm"
                        >
                          <FiShoppingCart className="w-4 h-4 mr-1" />
                          Add to Cart
                        </button>
                        <button
                          onClick={() => {
                            setViewingItem(product)
                            setEditingItem(product)
                            setModalType('view')
                          }}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        >
                          <FiEye className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredProducts.map((product, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-xl hover:shadow-md transition-all duration-200">
                    <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                      {product.image ? (
                        <img 
                          src={product.image} 
                          alt={product.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <FiHeart className="w-8 h-8 text-gray-400" />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">{product.name}</h3>
                      <p className="text-sm text-gray-600 mb-2 line-clamp-1">{product.description}</p>
                      <div className="flex items-center space-x-4">
                        <span className="text-lg font-bold text-gray-900">${product.price?.toFixed(2) || '0.00'}</span>
                        {product.category && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                            {product.category}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleAddToCart(product)}
                        className="flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 cursor-pointer text-sm"
                      >
                        <FiShoppingCart className="w-4 h-4 mr-1" />
                        Add to Cart
                      </button>
                      <button
                        onClick={() => {
                          setViewingItem(product)
                          setEditingItem(product)
                          setModalType('view')
                        }}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                      >
                        <FiEye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleRemoveFromFavorites(product.id || index)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-12">
            <FiHeart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || categoryFilter !== 'all' ? 'No matching favorites' : 'No favorites yet'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || categoryFilter !== 'all' 
                ? 'Try adjusting your search or filter criteria.'
                : 'Start browsing products and save your favorites here.'
              }
            </p>
            {(!searchTerm && categoryFilter === 'all') && (
              <button className="inline-flex items-center px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors duration-200 cursor-pointer">
                <FiHeart className="w-4 h-4 mr-2" />
                Browse Products
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default UserFavorites