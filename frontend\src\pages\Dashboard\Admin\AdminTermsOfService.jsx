import { useState, useEffect } from 'react'
import { FiFileText, FiSave, FiEdit, FiEye } from 'react-icons/fi'
import { useToast } from '../../../contexts/ToastContext'
import { brandingService } from '../../../services'

const AdminTermsOfService = ({ branding }) => {
  const { showSuccess, showError } = useToast()
  const [content, setContent] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [lastUpdated, setLastUpdated] = useState(null)

  // Load terms of service content
  useEffect(() => {
    const loadContent = async () => {
      setIsLoading(true)
      try {
        // Get terms of service from branding data
        if (branding?.legal?.termsOfService) {
          // Handle both string and object formats
          const termsOfService = branding.legal.termsOfService
          if (typeof termsOfService === 'string') {
            setContent(termsOfService)
          } else if (termsOfService && typeof termsOfService === 'object' && termsOfService.content) {
            setContent(termsOfService.content)
          } else {
            setContent('')
          }
          setLastUpdated(branding.legal.termsOfServiceUpdated)
        } else {
          // Set default content if none exists
          setContent(`# Terms of Service

## Acceptance of Terms

By accessing and using our services, you accept and agree to be bound by the terms and provision of this agreement.

## Use License

Permission is granted to temporarily download one copy of our materials for personal, non-commercial transitory viewing only.

## Disclaimer

The materials on our website are provided on an 'as is' basis. We make no warranties, expressed or implied, and hereby disclaim and negate all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.

## Limitations

In no event shall our company or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use our materials.

## Accuracy of Materials

The materials appearing on our website could include technical, typographical, or photographic errors. We do not warrant that any of the materials on its website are accurate, complete, or current.

## Links

We have not reviewed all of the sites linked to our website and are not responsible for the contents of any such linked site.

## Modifications

We may revise these terms of service at any time without notice. By using this website, you are agreeing to be bound by the then current version of these terms of service.

## Governing Law

These terms and conditions are governed by and construed in accordance with the laws of [Your Jurisdiction] and you irrevocably submit to the exclusive jurisdiction of the courts in that state or location.

## Contact Information

If you have any questions about these Terms of Service, please contact us at [<EMAIL>].

*Last updated: ${new Date().toLocaleDateString()}*`)
        }
      } catch (error) {
        console.error('Error loading terms of service:', error)
        showError('Failed to load terms of service')
      } finally {
        setIsLoading(false)
      }
    }

    loadContent()
  }, [branding, showError])

  // Save terms of service content
  const handleSave = async () => {
    if (!content.trim()) {
      showError('Terms of service content cannot be empty')
      return
    }

    setIsSaving(true)
    try {
      // Update branding with new terms of service
      const updatedBranding = {
        ...branding,
        legal: {
          ...branding?.legal,
          termsOfService: content,
          termsOfServiceUpdated: new Date().toISOString()
        }
      }

      await brandingService.updateBranding(updatedBranding)
      setLastUpdated(new Date().toISOString())
      setIsEditing(false)
      showSuccess('Terms of service updated successfully!')
    } catch (error) {
      console.error('Error saving terms of service:', error)
      showError('Failed to save terms of service')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 flex items-center">
            <FiFileText className="w-8 h-8 mr-3 text-green-600" />
            Terms of Service Management
          </h2>
          <p className="text-gray-600 mt-1">
            Manage your website's terms of service content
          </p>
          {lastUpdated && (
            <p className="text-sm text-gray-500 mt-1">
              Last updated: {new Date(lastUpdated).toLocaleString()}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-3">
          {isEditing ? (
            <>
              <button
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                ) : (
                  <FiSave className="w-4 h-4 mr-2" />
                )}
                {isSaving ? 'Saving...' : 'Save Changes'}
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200"
            >
              <FiEdit className="w-4 h-4 mr-2" />
              Edit Terms of Service
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        {isEditing ? (
          <div className="p-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Terms of Service Content
            </label>
            <div className="border border-gray-300 rounded-xl overflow-hidden">
              <div className="bg-gray-50 px-4 py-2 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <span>Rich Text Editor</span>
                  <span>•</span>
                  <span>{typeof content === 'string' ? content.length : 0} characters</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => setContent(content + '\n\n## New Section\n\nContent here...')}
                    className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                  >
                    Add Section
                  </button>
                </div>
              </div>
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="w-full h-96 px-4 py-3 border-0 focus:ring-0 focus:outline-none resize-none text-sm leading-relaxed"
                placeholder="Enter your terms of service content here..."
                style={{ fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace' }}
              />
            </div>
            <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
              <div className="flex items-center space-x-4">
                <span>💡 Use clear, simple language</span>
                <span>•</span>
                <span>📝 Include all essential legal sections</span>
              </div>
              <button
                type="button"
                onClick={() => {
                  const template = `# Terms of Service

## 1. Acceptance of Terms
By accessing and using our services, you accept and agree to be bound by the terms and provision of this agreement.

## 2. Description of Services
[Describe your services here]

## 3. User Responsibilities
Users are responsible for:
- Providing accurate information
- Maintaining account security
- Complying with applicable laws

## 4. Payment Terms
[Include payment terms if applicable]

## 5. Intellectual Property Rights
All content and materials are protected by intellectual property laws.

## 6. Limitation of Liability
[Include liability limitations]

## 7. Termination
We reserve the right to terminate accounts for violations of these terms.

## 8. Governing Law
These terms are governed by [Your Jurisdiction] law.

## 9. Contact Information
For questions about these terms, contact us at [<EMAIL>].

*Last updated: ${new Date().toLocaleDateString()}*`
                  setContent(template)
                }}
                className="text-green-600 hover:text-green-700 underline"
              >
                Load Template
              </button>
            </div>
          </div>
        ) : (
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <FiEye className="w-5 h-5 mr-2" />
                Preview
              </h3>
              <span className="text-sm text-gray-500">
                {typeof content === 'string' ? content.length : 0} characters
              </span>
            </div>
            <div className="prose max-w-none">
              <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                {typeof content === 'string' ? content : 'No terms of service content available. Click "Edit Terms of Service" to add content.'}
              </div>
            </div>
          </div>
        )}
      </div>


    </div>
  )
}

export default AdminTermsOfService
