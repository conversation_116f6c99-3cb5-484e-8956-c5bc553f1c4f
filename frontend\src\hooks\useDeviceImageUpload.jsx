import { useCallback, useRef } from 'react'
import { isMobileDevice, getImageInputAccept, getImageInputCapture } from '../utils/constants'
import { useToast } from '../contexts/ToastContext'

/**
 * Custom hook for device-specific image upload handling
 * - Mobile: Direct camera/gallery access with instant upload
 * - Desktop: Media library modal with instant upload on selection
 */
export const useDeviceImageUpload = ({
  onUpload,
  onError,
  multiple = true,
  maxFileSize = 5 * 1024 * 1024, // 5MB default
  allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
}) => {
  const { showError, showSuccess } = useToast()
  const fileInputRef = useRef(null)
  const isMobile = isMobileDevice()

  // Validate file before upload
  const validateFile = useCallback((file) => {
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      const error = `Invalid file type. Please select: ${allowedTypes.join(', ')}`
      showError(error)
      if (onError) onError(error)
      return false
    }

    // Check file size
    if (file.size > maxFileSize) {
      const error = `File size must be less than ${Math.round(maxFileSize / (1024 * 1024))}MB`
      showError(error)
      if (onError) onError(error)
      return false
    }

    return true
  }, [allowedTypes, maxFileSize, showError, onError])

  // Handle file selection and instant upload
  const handleFileSelection = useCallback(async (files) => {
    if (!files || files.length === 0) return

    const fileArray = Array.from(files)
    const validFiles = fileArray.filter(validateFile)

    if (validFiles.length === 0) return

    try {
      // Show loading state
      showSuccess(`Uploading ${validFiles.length} image(s)...`)

      // Call the upload handler
      if (onUpload) {
        await onUpload(validFiles)
      }

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }

    } catch (error) {
      console.error('Error uploading files:', error)
      const errorMessage = error.message || 'Failed to upload images'
      showError(errorMessage)
      if (onError) onError(errorMessage)
    }
  }, [validateFile, onUpload, onError, showSuccess, showError])

  // Trigger file input for mobile devices (gallery access)
  const triggerMobileImageSelection = useCallback(() => {
    if (!fileInputRef.current) return

    // Create a new file input with mobile-optimized attributes
    const input = fileInputRef.current
    input.accept = getImageInputAccept()
    input.multiple = multiple

    // Remove capture attribute to allow gallery access
    input.removeAttribute('capture')

    // Trigger file selection
    input.click()
  }, [multiple])

  // Trigger camera access for mobile devices
  const triggerMobileCameraAccess = useCallback(() => {
    if (!fileInputRef.current) return

    // Create a new file input with camera capture
    const input = fileInputRef.current
    input.accept = getImageInputAccept()
    input.multiple = multiple

    // Add capture attribute for camera access
    input.setAttribute('capture', 'environment')

    // Trigger file selection
    input.click()
  }, [multiple])

  // Handle input change event
  const handleInputChange = useCallback((event) => {
    const files = event.target.files
    handleFileSelection(files)
  }, [handleFileSelection])

  // Get button props for mobile/desktop
  const getImageSelectionProps = useCallback(() => {
    if (isMobile) {
      return {
        onClick: triggerMobileImageSelection,
        children: 'Take Photo / Select Images'
      }
    } else {
      return {
        children: 'Select Images'
      }
    }
  }, [isMobile, triggerMobileImageSelection])

  // Create hidden file input for mobile
  const createHiddenFileInput = useCallback(() => {
    if (!isMobile) return null

    return (
      <input
        ref={fileInputRef}
        type="file"
        accept={getImageInputAccept()}
        multiple={multiple}
        onChange={handleInputChange}
        style={{ display: 'none' }}
        capture={getImageInputCapture()}
      />
    )
  }, [isMobile, multiple, handleInputChange])

  return {
    isMobile,
    triggerMobileImageSelection,
    triggerMobileCameraAccess,
    handleFileSelection,
    handleInputChange,
    getImageSelectionProps,
    createHiddenFileInput,
    fileInputRef,
    validateFile
  }
}

export default useDeviceImageUpload
