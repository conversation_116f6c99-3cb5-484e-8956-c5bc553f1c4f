@echo off
echo 🚀 MongoDB Migration Setup and Execution
echo ========================================

echo.
echo 📦 Installing dependencies...
copy migration-package.json package.json
npm install

echo.
echo 🔍 Testing database connections...
node test-connection.js

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Connection test failed. Please check your database connections.
    pause
    exit /b 1
)

echo.
echo 🚀 Starting migration...
node migrate-database.js

echo.
echo ✅ Migration process completed!
echo 📋 Check migration-report.json for detailed results
pause
