import { Request, Response } from 'express';
import { Cart, Product } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class CartController {
  static async getCart(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        // For guest users, return empty cart structure
        sendSuccess(res, 'Guest cart retrieved successfully', {
          items: [],
          total: 0,
          isGuest: true
        });
        return;
      }

      let cart = await Cart.findOne({ user: req.user._id })
        .populate('items.product', 'name price images stock isActive');

      if (!cart) {
        cart = await Cart.create({ user: req.user._id, items: [] });
      }

      sendSuccess(res, 'Cart retrieved successfully', cart);
    } catch (error) {
      console.error('Get cart error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async addToCart(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { productId, quantity } = req.body;

      // Verify product exists and is active
      const product = await Product.findById(productId);
      if (!product || !product.isActive) {
        sendNotFound(res, 'Product not found or inactive');
        return;
      }

      // Check stock availability
      if (product.stock < quantity) {
        sendError(res, 'Insufficient stock available');
        return;
      }

      if (!req.user) {
        // For guest users, return success with product info for frontend to handle
        sendSuccess(res, 'Product added to guest cart', {
          product: {
            _id: product._id,
            name: product.name,
            price: product.price,
            images: product.images,
            stock: product.stock
          },
          quantity,
          isGuest: true
        });
        return;
      }

      let cart = await Cart.findOne({ user: req.user._id });

      if (!cart) {
        cart = await Cart.create({
          user: req.user._id,
          items: [{
            product: productId,
            quantity,
            price: product.price
          }]
        });
      } else {
        // Check if product already exists in cart
        const existingItemIndex = cart.items.findIndex(
          item => item.product.toString() === productId
        );

        if (existingItemIndex > -1) {
          // Update quantity
          const newQuantity = cart.items[existingItemIndex].quantity + quantity;

          if (product.stock < newQuantity) {
            sendError(res, 'Insufficient stock available');
            return;
          }

          cart.items[existingItemIndex].quantity = newQuantity;
          cart.items[existingItemIndex].price = product.price; // Update price
        } else {
          // Add new item
          cart.items.push({
            product: productId,
            quantity,
            price: product.price
          });
        }

        await cart.save();
      }

      await cart.populate('items.product', 'name price images stock isActive');

      sendSuccess(res, 'Item added to cart successfully', cart);
    } catch (error) {
      console.error('Add to cart error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateCartItem(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { itemId } = req.params;
      const { quantity } = req.body;

      const cart = await Cart.findOne({ user: req.user._id });

      if (!cart) {
        sendNotFound(res, 'Cart not found');
        return;
      }

      const itemIndex = cart.items.findIndex(
        item => item.product.toString() === itemId
      );

      if (itemIndex === -1) {
        sendNotFound(res, 'Item not found in cart');
        return;
      }

      // Verify product stock
      const product = await Product.findById(itemId);
      if (!product || product.stock < quantity) {
        sendError(res, 'Insufficient stock available');
        return;
      }

      cart.items[itemIndex].quantity = quantity;
      cart.items[itemIndex].price = product.price; // Update price
      await cart.save();

      await cart.populate('items.product', 'name price images stock isActive');

      sendSuccess(res, 'Cart item updated successfully', cart);
    } catch (error) {
      console.error('Update cart item error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async removeFromCart(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { itemId } = req.params;

      const cart = await Cart.findOne({ user: req.user._id });

      if (!cart) {
        sendNotFound(res, 'Cart not found');
        return;
      }

      cart.items = cart.items.filter(
        item => item.product.toString() !== itemId
      );

      await cart.save();
      await cart.populate('items.product', 'name price images stock isActive');

      sendSuccess(res, 'Item removed from cart successfully', cart);
    } catch (error) {
      console.error('Remove from cart error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async clearCart(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const cart = await Cart.findOne({ user: req.user._id });

      if (!cart) {
        sendNotFound(res, 'Cart not found');
        return;
      }

      cart.items = [];
      await cart.save();

      sendSuccess(res, 'Cart cleared successfully', cart);
    } catch (error) {
      console.error('Clear cart error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async mergeGuestCart(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { items } = req.body;

      if (!items || !Array.isArray(items) || items.length === 0) {
        sendSuccess(res, 'No guest cart items to merge', { items: [] });
        return;
      }

      let cart = await Cart.findOne({ user: req.user._id });

      if (!cart) {
        cart = await Cart.create({ user: req.user._id, items: [] });
      }

      // Merge guest cart items with user cart
      for (const guestItem of items) {
        const { product: productId, quantity } = guestItem;

        // Verify product exists and is active
        const product = await Product.findById(productId);
        if (!product || !product.isActive) {
          continue; // Skip invalid products
        }

        // Check if product already exists in user cart
        const existingItemIndex = cart.items.findIndex(
          item => item.product.toString() === productId
        );

        if (existingItemIndex > -1) {
          // Update quantity (don't exceed stock)
          const newQuantity = Math.min(
            cart.items[existingItemIndex].quantity + quantity,
            product.stock
          );
          cart.items[existingItemIndex].quantity = newQuantity;
          cart.items[existingItemIndex].price = product.price; // Update price
        } else {
          // Add new item (don't exceed stock)
          const finalQuantity = Math.min(quantity, product.stock);
          cart.items.push({
            product: productId,
            quantity: finalQuantity,
            price: product.price
          });
        }
      }

      await cart.save();
      await cart.populate('items.product', 'name price images stock isActive');

      sendSuccess(res, 'Guest cart merged successfully', cart);
    } catch (error) {
      console.error('Merge guest cart error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
