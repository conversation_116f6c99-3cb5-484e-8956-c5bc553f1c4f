/**
 * Toast-based confirmation dialog utility
 * Replaces browser confirm() with a custom toast confirmation
 */

/**
 * Show a confirmation dialog using toast
 * @param {Object} toast - Toast instance from useToast
 * @param {Object} options - Configuration options
 * @param {string} options.title - Dialog title
 * @param {string} options.message - Dialog message
 * @param {Function} options.onConfirm - Function to call when confirmed
 * @param {string} options.confirmText - Text for confirm button (default: "Confirm")
 * @param {string} options.cancelText - Text for cancel button (default: "Cancel")
 * @param {string} options.confirmColor - Color for confirm button (default: "red")
 * @param {string} options.position - Toast position (default: "top-center")
 */
export const showConfirmDialog = (toast, options = {}) => {
  const {
    title = 'Confirm Action',
    message = 'Are you sure you want to proceed?',
    onConfirm,
    confirmText = 'Confirm',
    cancelText = 'Cancel',
    confirmColor = 'red',
    position = 'top-center'
  } = options;

  const confirmToast = toast((t) => (
    <div className="flex flex-col gap-3">
      <div className="font-medium text-gray-900">
        {title}
      </div>
      <div className="text-sm text-gray-600">
        {message}
      </div>
      <div className="flex gap-2 justify-end">
        <button
          onClick={() => toast.dismiss(t.id)}
          className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
        >
          {cancelText}
        </button>
        <button
          onClick={async () => {
            toast.dismiss(t.id);
            if (onConfirm) {
              await onConfirm();
            }
          }}
          className={`px-3 py-1 text-sm text-white rounded transition-colors ${
            confirmColor === 'red' 
              ? 'bg-red-500 hover:bg-red-600' 
              : confirmColor === 'blue'
              ? 'bg-blue-500 hover:bg-blue-600'
              : confirmColor === 'green'
              ? 'bg-green-500 hover:bg-green-600'
              : 'bg-gray-500 hover:bg-gray-600'
          }`}
        >
          {confirmText}
        </button>
      </div>
    </div>
  ), {
    duration: Infinity,
    position,
    style: {
      background: '#fff',
      color: '#374151',
      border: '1px solid #e5e7eb',
      borderRadius: '12px',
      padding: '20px',
      minWidth: '400px',
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    }
  });

  return confirmToast;
};

/**
 * Show an input dialog using toast
 * @param {Object} toast - Toast instance from useToast
 * @param {Object} options - Configuration options
 * @param {string} options.title - Dialog title
 * @param {string} options.message - Dialog message
 * @param {string} options.placeholder - Input placeholder
 * @param {string} options.inputType - Input type (default: "text")
 * @param {Function} options.onSubmit - Function to call with input value
 * @param {string} options.submitText - Text for submit button (default: "Submit")
 * @param {string} options.cancelText - Text for cancel button (default: "Cancel")
 * @param {Function} options.validator - Function to validate input (optional)
 * @param {string} options.position - Toast position (default: "top-center")
 */
export const showInputDialog = (toast, options = {}) => {
  const {
    title = 'Input Required',
    message = 'Please enter a value:',
    placeholder = '',
    inputType = 'text',
    onSubmit,
    submitText = 'Submit',
    cancelText = 'Cancel',
    validator,
    position = 'top-center'
  } = options;

  let inputValue = '';

  const inputToast = toast((t) => (
    <div className="flex flex-col gap-3">
      <div className="font-medium text-gray-900">
        {title}
      </div>
      <div className="text-sm text-gray-600">
        {message}
      </div>
      <input
        type={inputType}
        placeholder={placeholder}
        className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        onChange={(e) => inputValue = e.target.value}
        onKeyPress={(e) => {
          if (e.key === 'Enter' && inputValue) {
            const isValid = validator ? validator(inputValue) : true;
            if (isValid) {
              toast.dismiss(t.id);
              if (onSubmit) {
                onSubmit(inputValue);
              }
            }
          }
        }}
        autoFocus
      />
      <div className="flex gap-2 justify-end">
        <button
          onClick={() => toast.dismiss(t.id)}
          className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
        >
          {cancelText}
        </button>
        <button
          onClick={() => {
            const isValid = validator ? validator(inputValue) : true;
            if (inputValue && isValid) {
              toast.dismiss(t.id);
              if (onSubmit) {
                onSubmit(inputValue);
              }
            }
          }}
          className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          {submitText}
        </button>
      </div>
    </div>
  ), {
    duration: Infinity,
    position,
    style: {
      background: '#fff',
      color: '#374151',
      border: '1px solid #e5e7eb',
      borderRadius: '12px',
      padding: '20px',
      minWidth: '400px',
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    }
  });

  return inputToast;
};

/**
 * Show a delete confirmation dialog
 * @param {Object} toast - Toast instance from useToast
 * @param {Object} options - Configuration options
 * @param {string} options.itemName - Name of item being deleted
 * @param {Function} options.onConfirm - Function to call when confirmed
 */
export const showDeleteConfirmation = (toast, options = {}) => {
  const { itemName = 'item', onConfirm } = options;
  
  return showConfirmDialog(toast, {
    title: `Delete ${itemName}`,
    message: `Are you sure you want to delete this ${itemName.toLowerCase()}? This action cannot be undone.`,
    onConfirm,
    confirmText: 'Delete',
    confirmColor: 'red'
  });
};

export default {
  showConfirmDialog,
  showInputDialog,
  showDeleteConfirmation
};
