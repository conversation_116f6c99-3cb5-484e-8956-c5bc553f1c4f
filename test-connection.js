const { MongoClient } = require('mongodb');

// Database connection strings
const SOURCE_URI = 'mongodb+srv://inspiredolajosh:<EMAIL>/MicrolocsHq?retryWrites=true&w=majority';
const TARGET_URI = 'mongodb+srv://dammyspicy4:<EMAIL>/microlocs?retryWrites=true&w=majority';

async function testConnections() {
  console.log('🔍 Testing database connections...\n');

  // Test source database
  console.log('📍 Testing source database connection...');
  try {
    const sourceClient = new MongoClient(SOURCE_URI);
    await sourceClient.connect();
    
    const sourceDb = sourceClient.db('MicrolocsHq');
    await sourceDb.admin().ping();
    
    // Get collections and document counts
    const collections = await sourceDb.listCollections().toArray();
    console.log('✅ Source database connected successfully');
    console.log(`📊 Found ${collections.length} collections:`);
    
    for (const col of collections) {
      const count = await sourceDb.collection(col.name).countDocuments();
      console.log(`   - ${col.name}: ${count} documents`);
    }
    
    await sourceClient.close();
  } catch (error) {
    console.error('❌ Source database connection failed:', error.message);
    return false;
  }

  console.log('\n📍 Testing target database connection...');
  try {
    const targetClient = new MongoClient(TARGET_URI);
    await targetClient.connect();
    
    const targetDb = targetClient.db('microlocs');
    await targetDb.admin().ping();
    
    // Get collections and document counts
    const collections = await targetDb.listCollections().toArray();
    console.log('✅ Target database connected successfully');
    console.log(`📊 Found ${collections.length} collections:`);
    
    for (const col of collections) {
      const count = await targetDb.collection(col.name).countDocuments();
      console.log(`   - ${col.name}: ${count} documents`);
    }
    
    await targetClient.close();
  } catch (error) {
    console.error('❌ Target database connection failed:', error.message);
    return false;
  }

  console.log('\n🎉 Both database connections are working!');
  return true;
}

// Run test if this file is executed directly
if (require.main === module) {
  testConnections()
    .then(success => {
      if (success) {
        console.log('\n✅ Ready to run migration!');
        console.log('Run: npm run migrate');
      } else {
        console.log('\n❌ Fix connection issues before running migration');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = testConnections;
