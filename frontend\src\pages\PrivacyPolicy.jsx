import { useState, useEffect } from 'react'
import { FiArrowLeft, FiCalendar } from 'react-icons/fi'
import { useBranding } from '../contexts/BrandingContext'

const PrivacyPolicy = ({ onNavigate }) => {
  const { branding } = useBranding()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate loading time for branding data
    const timer = setTimeout(() => {
      setLoading(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  const handleGoBack = () => {
    if (onNavigate) {
      onNavigate('home')
    } else {
      window.history.back()
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  const privacyPolicy = branding?.legal?.privacyPolicy
  const companyName = branding?.global?.siteName || 'Our Company'
  const lastUpdated = privacyPolicy?.lastUpdated ? new Date(privacyPolicy.lastUpdated).toLocaleDateString() : new Date().toLocaleDateString()

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-white/20 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={handleGoBack}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
            >
              <FiArrowLeft className="w-5 h-5 mr-2" />
              Back
            </button>
            <div className="flex items-center text-sm text-gray-500">
              <FiCalendar className="w-4 h-4 mr-2" />
              Last updated: {lastUpdated}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8">
          {/* Title */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {privacyPolicy?.title || 'Privacy Policy'}
            </h1>
            <p className="text-lg text-gray-600">
              {companyName} - Effective {lastUpdated}
            </p>
          </div>

          {/* Content */}
          <div className="prose prose-lg max-w-none">
            {privacyPolicy?.content ? (
              <div 
                dangerouslySetInnerHTML={{ __html: privacyPolicy.content }}
                className="text-gray-700 leading-relaxed"
              />
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <FiCalendar className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  Privacy Policy Not Available
                </h3>
                <p className="text-gray-500">
                  The privacy policy content is currently being updated. Please check back later.
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="text-center text-sm text-gray-500">
              <p>
                If you have any questions about this Privacy Policy, please contact us at{' '}
                <a 
                  href={`mailto:${branding?.global?.email || '<EMAIL>'}`}
                  className="text-blue-600 hover:text-blue-800 transition-colors duration-200"
                >
                  {branding?.global?.email || '<EMAIL>'}
                </a>
              </p>
              <p className="mt-2">
                © {new Date().getFullYear()} {companyName}. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PrivacyPolicy
