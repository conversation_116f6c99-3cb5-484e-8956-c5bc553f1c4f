import { useState, useEffect } from 'react';
import { adminAPI, type AppointmentAnalytics } from '../../utils/api';
import LoadingSpinner from '../LoadingSpinner';
import AnalyticsChart from './AnalyticsChart';

export default function Analytics() {
  const [analytics, setAnalytics] = useState<AppointmentAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    endDate: new Date().toISOString().split('T')[0] // today
  });

  // Load analytics data
  const loadAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await adminAPI.getAnalytics(dateRange.startDate, dateRange.endDate);
      
      if (response.success) {
        setAnalytics(response.data);
      } else {
        setError('Failed to load analytics data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  // Load analytics on mount and date range changes
  useEffect(() => {
    loadAnalytics();
  }, [dateRange.startDate, dateRange.endDate]);

  // Handle date range change
  const handleDateRangeChange = (field: 'startDate' | 'endDate', value: string) => {
    setDateRange(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Quick date range presets
  const setQuickRange = (days: number) => {
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    setDateRange({ startDate, endDate });
  };

  // Calculate percentage change (mock for now)
  const getPercentageChange = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  };

  if (loading) {
    return <LoadingSpinner message="Loading analytics..." />;
  }

  if (error) {
    return (
      <div className="analytics-error">
        <div className="error-message">
          <h3>Error Loading Analytics</h3>
          <p>{error}</p>
          <button onClick={loadAnalytics} className="btn btn-primary">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="analytics-empty">
        <h3>No Analytics Data Available</h3>
        <p>Please check back later or adjust your date range.</p>
      </div>
    );
  }

  return (
    <div className="analytics-dashboard">
      <div className="analytics-header">
        <div className="header-content">
          <h2>Analytics Dashboard</h2>
          <p>Comprehensive insights into your business performance</p>
        </div>
        
        <div className="date-range-controls">
          <div className="quick-ranges">
            <button onClick={() => setQuickRange(7)} className="quick-range-btn">
              Last 7 Days
            </button>
            <button onClick={() => setQuickRange(30)} className="quick-range-btn">
              Last 30 Days
            </button>
            <button onClick={() => setQuickRange(90)} className="quick-range-btn">
              Last 90 Days
            </button>
          </div>
          
          <div className="custom-range">
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => handleDateRangeChange('startDate', e.target.value)}
              className="date-input"
            />
            <span>to</span>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => handleDateRangeChange('endDate', e.target.value)}
              className="date-input"
            />
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="metrics-grid">
        <div className="metric-card">
          <div className="metric-header">
            <h3>Total Appointments</h3>
            <span className="metric-icon">📅</span>
          </div>
          <div className="metric-value">{analytics.totalAppointments}</div>
          <div className="metric-change positive">
            +{getPercentageChange(analytics.totalAppointments, analytics.totalAppointments * 0.8).toFixed(1)}%
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>Total Revenue</h3>
            <span className="metric-icon">💰</span>
          </div>
          <div className="metric-value">${analytics.revenueStats.totalRevenue.toFixed(2)}</div>
          <div className="metric-change positive">
            +{getPercentageChange(analytics.revenueStats.totalRevenue, analytics.revenueStats.totalRevenue * 0.85).toFixed(1)}%
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>Average Revenue</h3>
            <span className="metric-icon">📊</span>
          </div>
          <div className="metric-value">${analytics.revenueStats.averageRevenue.toFixed(2)}</div>
          <div className="metric-change neutral">
            {getPercentageChange(analytics.revenueStats.averageRevenue, analytics.revenueStats.averageRevenue * 0.95).toFixed(1)}%
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>Today's Appointments</h3>
            <span className="metric-icon">🗓️</span>
          </div>
          <div className="metric-value">{analytics.todayAppointments}</div>
          <div className="metric-change positive">
            +{getPercentageChange(analytics.todayAppointments, Math.max(1, analytics.todayAppointments - 2)).toFixed(1)}%
          </div>
        </div>
      </div>

      {/* Status Overview */}
      <div className="analytics-section">
        <h3>Appointment Status Overview</h3>
        <div className="status-overview">
          <div className="status-grid">
            {Object.entries(analytics.statusStats).map(([status, count]) => (
              <div key={status} className={`status-card status-${status}`}>
                <div className="status-count">{count}</div>
                <div className="status-label">{status.charAt(0).toUpperCase() + status.slice(1)}</div>
                <div className="status-percentage">
                  {((count / analytics.totalAppointments) * 100).toFixed(1)}%
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Daily Trends Chart */}
      <div className="analytics-section">
        <h3>Daily Appointment Trends</h3>
        <div className="chart-container">
          <AnalyticsChart 
            data={analytics.dailyStats}
            type="line"
            title="Appointments Over Time"
          />
        </div>
      </div>

      {/* Service Popularity */}
      <div className="analytics-section">
        <h3>Most Popular Services</h3>
        <div className="service-stats">
          {analytics.serviceStats.slice(0, 10).map((service, index) => (
            <div key={service._id} className="service-stat-item">
              <div className="service-rank">#{index + 1}</div>
              <div className="service-info">
                <div className="service-name">{service.serviceName}</div>
                <div className="service-metrics">
                  <span className="service-count">{service.count} appointments</span>
                  <span className="service-revenue">${service.revenue.toFixed(2)} revenue</span>
                </div>
              </div>
              <div className="service-bar">
                <div 
                  className="service-bar-fill"
                  style={{ 
                    width: `${(service.count / analytics.serviceStats[0]?.count * 100) || 0}%` 
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Revenue Breakdown */}
      <div className="analytics-section">
        <h3>Revenue Insights</h3>
        <div className="revenue-insights">
          <div className="insight-card">
            <h4>Total Revenue</h4>
            <div className="insight-value">${analytics.revenueStats.totalRevenue.toFixed(2)}</div>
            <p>From {analytics.revenueStats.totalAppointments} completed appointments</p>
          </div>
          
          <div className="insight-card">
            <h4>Average Per Appointment</h4>
            <div className="insight-value">${analytics.revenueStats.averageRevenue.toFixed(2)}</div>
            <p>Average revenue per completed appointment</p>
          </div>
          
          <div className="insight-card">
            <h4>Projected Monthly</h4>
            <div className="insight-value">
              ${(analytics.revenueStats.totalRevenue * (30 / Math.max(1, analytics.dailyStats.length))).toFixed(2)}
            </div>
            <p>Based on current daily average</p>
          </div>
        </div>
      </div>
    </div>
  );
}
