import { Router } from 'express';
import { ServiceController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { mongoIdValidation } from '../utils/validation';
import { upload } from '../services/cloudinaryService';

const router = Router();

// GET /api/services
router.get(
  '/',
  ServiceController.getAllServices
);

// GET /api/services/categories
router.get(
  '/categories',
  ServiceController.getServiceCategories
);

// GET /api/services/:id
router.get(
  '/:id',
  validate(mongoIdValidation()),
  ServiceController.getServiceById
);

// Admin routes
// POST /api/services (admin only)
router.post(
  '/',
  authenticate,
  authorize('admin'),
  ServiceController.createService
);

// PUT /api/services/:id (admin only)
router.put(
  '/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  ServiceController.updateService
);

// DELETE /api/services/:id (admin only)
router.delete(
  '/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  ServiceController.deleteService
);

// POST /api/services/:id/upload-image (admin only)
router.post(
  '/:id/upload-image',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  upload.single('file'),
  ServiceController.uploadServiceImage
);

// DELETE /api/services/:id/delete-image (admin only)
router.delete(
  '/:id/delete-image',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  ServiceController.deleteServiceImage
);

export default router;
