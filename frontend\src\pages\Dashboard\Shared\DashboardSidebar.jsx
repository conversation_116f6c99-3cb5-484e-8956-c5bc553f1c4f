import { FiLogOut } from 'react-icons/fi'
import { useBranding } from '../../../contexts/BrandingContext'

const DashboardSidebar = ({
  userType,
  activeTab,
  setActiveTab,
  userData,
  onLogout,
  sidebarItems
}) => {
  const { branding, isLoading } = useBranding()

  // Show loading sidebar if branding is not available
  if (isLoading || !branding) {
    return (
      <div className="w-80 h-screen bg-white/90 backdrop-blur-sm border-r border-white/20 shadow-xl sticky top-0">
        <div className="flex flex-col h-full">
          <div className="p-6 border-b border-gray-200">
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-32 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-24"></div>
            </div>
          </div>
          <div className="flex-1 p-4">
            <div className="animate-pulse space-y-3">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-12 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-80 h-screen bg-white/90 backdrop-blur-sm border-r border-white/20 shadow-xl sticky top-0">
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
              <span className="text-white font-bold text-lg">
                {((userData?.name || userData?.firstName || 'U').replace(/\s+User!?$/i, '')).charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-900">
                {(userData?.name || `${userData?.firstName || ''} ${userData?.lastName || ''}`.trim() || 'User').replace(/\s+User!?$/i, '')}
              </h2>
              <p className="text-sm text-gray-600">
                {userType === 'admin' ? 'Administrator' : 'Customer'}
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto custom-scrollbar p-4">
          <nav className="space-y-2">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center px-4 py-3 rounded-xl transition-all duration-200 group ${
                  activeTab === item.id
                    ? 'bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border border-blue-200 shadow-sm'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <div className={`p-2 rounded-lg mr-3 transition-all duration-200 ${
                  activeTab === item.id
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-gray-100 text-gray-600 group-hover:bg-gray-200'
                }`}>
                  <item.icon className="w-5 h-5" />
                </div>
                <div className="text-left">
                  <p className="font-medium">{item.label}</p>
                  {item.description && (
                    <p className="text-xs text-gray-500 mt-0.5">{item.description}</p>
                  )}
                </div>
                {item.badge && (
                  <span className="ml-auto px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium">
                    {item.badge}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <button
            onClick={onLogout}
            className="w-full flex items-center px-4 py-3 rounded-xl text-red-600 hover:bg-red-50 transition-all duration-200 group"
          >
            <div className="p-2 rounded-lg mr-3 bg-red-100 text-red-600 group-hover:bg-red-200">
              <FiLogOut className="w-5 h-5" />
            </div>
            <span className="font-medium">Logout</span>
          </button>
        </div>

        {/* Branding Footer */}
        <div className="p-4 border-t border-gray-100">
          <div className="text-center">
            <p className="text-xs text-gray-500">
              © 2024 {branding.businessName || 'Your Business'}. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardSidebar
