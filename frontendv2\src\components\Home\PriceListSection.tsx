import { useState, useEffect } from 'react';
import { API_CONFIG } from '../../utils/config';

interface PriceListSectionProps {
  onBookService: (service: any) => void;
}

interface Service {
  _id: string;
  name: string;
  price: number;
  duration: number;
  category: string;
  description: string;
  isActive: boolean;
  type: string;
}

export default function PriceListSection({ onBookService }: PriceListSectionProps) {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/services`);
      if (response.ok) {
        const data = await response.json();
        // The API returns grouped services, so we need to flatten them
        const groupedServices = data.data || {};
        const flatServices: Service[] = [];

        // Flatten the grouped services into a single array
        Object.values(groupedServices).forEach((categoryServices: any) => {
          if (Array.isArray(categoryServices)) {
            categoryServices.forEach((service: any) => {
              flatServices.push({
                _id: service.id,
                name: service.name,
                price: parseFloat(service.price),
                duration: service.duration,
                category: service.category,
                description: service.description,
                isActive: true,
                type: service.type || 'service'
              });
            });
          }
        });

        setServices(flatServices);
      }
    } catch (error) {
      console.error('Error fetching services:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="info-card price-list-card">
        <h3 className="info-title">Price list</h3>
        <div className="info-content">
          <p>Loading services...</p>
        </div>
      </div>
    );
  }

  // Group retie services (services that contain "retie" or "retightening")
  const retieServices = services.filter(service =>
    service.name.toLowerCase().includes('retightening') ||
    service.type === 'retightening'
  );

  const otherServices = services.filter(service =>
    !service.name.toLowerCase().includes('retightening') &&
    service.type !== 'retightening'
  );

  const groupedRetieServices = retieServices.reduce((acc: Record<string, Service[]>, service) => {
    const category = service.category || 'Other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(service);
    return acc;
  }, {});

  const renderRetieItem = (service: Service) => (
    <div
      key={service._id}
      className="retie-item clickable"
      onClick={() => onBookService({
        id: service._id,
        name: service.name,
        price: service.price,
        duration: service.duration,
        category: service.category,
        description: service.description
      })}
    >
      <span className="retie-duration">
        {service.name.includes('Extra Small') ? 'Extra Small' :
         service.name.includes('Small') && !service.name.includes('Extra') ? 'Small' : 'Medium'}
      </span>
      <span className="retie-dots">.............</span>
      <span className="retie-price">${service.price}</span>
      <button className="retie-book-btn">BOOK</button>
    </div>
  );

  const renderOtherService = (service: Service) => (
    <div
      key={service._id}
      className="service-item clickable"
      onClick={() => onBookService({
        id: service._id,
        name: service.name,
        price: service.price,
        duration: service.duration,
        category: service.category,
        description: service.description
      })}
    >
      <div className="service-info">
        <span className="service-name">{service.name}</span>
        <span className="service-dots">.............</span>
        <span className="service-price">${service.price}</span>
      </div>
      <button className="retie-book-btn">BOOK</button>
    </div>
  );

  return (
    <div className="info-card price-list-card">
      <h3 className="info-title">Price list</h3>
      <div className="info-content">
        <h4 className="reties-title">Reties</h4>
        <div className="reties-pricing">
          {Object.entries(groupedRetieServices).map(([category, services]) => (
            <div key={category} className="retie-period">
              <h5 className="retie-period-title">{category}</h5>
              {services.map(renderRetieItem)}
            </div>
          ))}
        </div>

        {/* Other Services Section */}
        {otherServices.length > 0 && (
          <div className="other-services">
            <h4 className="other-services-title">Other Services</h4>
            {otherServices.map(renderOtherService)}
          </div>
        )}
      </div>
    </div>
  )
}
