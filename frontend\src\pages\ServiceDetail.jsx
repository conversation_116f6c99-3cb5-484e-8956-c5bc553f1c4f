import { useState, useEffect, useCallback, useRef } from 'react';
import { FiClock, FiDollarSign, FiCalendar, FiStar, FiArrowLeft, FiCheck } from 'react-icons/fi';
import { serviceService, apiService } from '../services';
import { useService, useAppointmentAvailability } from '../hooks/useQueries';
import { useBranding } from '../contexts/BrandingContext';
import Loading from '../components/Loading';
import { Reviews } from '../components/Reviews';

const ServiceDetail = ({ serviceId, onNavigate }) => {
  const { branding } = useBranding();

  // State for booking availability
  const [selectedDate, setSelectedDate] = useState('');
  const [showAvailability, setShowAvailability] = useState(false);

  // Helper function to format duration from hours to readable format
  const formatDuration = (hours) => {
    if (!hours) return 'Varies'

    if (hours === 1) {
      return '1 hour'
    } else if (hours < 1) {
      const minutes = Math.round(hours * 60)
      return `${minutes} minutes`
    } else if (hours % 1 === 0) {
      return `${hours} hours`
    } else {
      return `${hours} hours`
    }
  }



  // Use React Query to fetch service data
  const {
    data: service,
    isLoading: loading,
    error,
    refetch
  } = useService(serviceId);

  // Use React Query to fetch availability when date is selected
  const {
    data: availabilityData,
    isLoading: loadingSlots,
  } = useAppointmentAvailability(
    { date: selectedDate, service: serviceId },
    { enabled: !!(selectedDate && serviceId) }
  );

  const availableSlots = availabilityData?.availableSlots || [];

  // Handle date selection
  const handleDateChange = (date) => {
    setSelectedDate(date);
  };

  // Get minimum date (today)
  const getMinDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, [serviceId]);

  if (loading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Service Not Found</h1>
          <p className="text-gray-600 mb-6">The service you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => onNavigate('services')}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            Back to Services
          </button>
        </div>
      </div>
    );
  }

  if (!service) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center mb-4">
            <button
              onClick={() => onNavigate('services')}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 mr-4"
            >
              <FiArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
              {service.name}
            </h1>
          </div>
          
          {service.description && (
            <p className="text-xl text-gray-600 max-w-3xl">
              {service.description}
            </p>
          )}
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Service Details */}
          <div className="lg:col-span-2 space-y-8">
            {/* Service Image */}
            {service.image && (
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg border border-white/20">
                <img
                  src={service.image}
                  alt={service.name}
                  className="w-full h-64 md:h-80 object-cover"
                />
              </div>
            )}

            {/* Service Information */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Service Details</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <FiDollarSign className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Price</p>
                    <p className="text-xl font-bold text-gray-900">
                      ${service.price?.toFixed(2) || 'Contact for pricing'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <FiClock className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Duration</p>
                    <p className="text-xl font-bold text-gray-900">
                      {formatDuration(service.duration)}
                    </p>
                  </div>
                </div>
              </div>

              {/* Features */}
              {service.features && service.features.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">What's Included:</h3>
                  <ul className="space-y-3">
                    {service.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-gray-700">
                        <FiStar className="w-4 h-4 mr-3 text-yellow-400" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Long Description */}
              {service.longDescription && (
                <div className="mt-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">About This Service</h3>
                  <div className="prose prose-gray max-w-none">
                    <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                      {service.longDescription}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Customer Reviews */}
            <div>
              <Reviews 
                serviceId={serviceId}
                branding={branding}
                showWriteReview={true}
              />
            </div>
          </div>

          {/* Booking Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 sticky top-8">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Book This Service</h3>
              
              <div className="space-y-4 mb-8">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Service Price:</span>
                  <span className="text-2xl font-bold text-gray-900">
                    ${service.price?.toFixed(2) || 'TBD'}
                  </span>
                </div>
                
                {service.duration && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Duration:</span>
                    <span className="font-medium text-gray-900">{formatDuration(service.duration)}</span>
                  </div>
                )}
              </div>

              {/* Check Availability Section */}
              <div className="mb-6">
                <button
                  onClick={() => setShowAvailability(!showAvailability)}
                  className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 px-6 rounded-xl font-medium hover:from-green-700 hover:to-blue-700 focus:ring-4 focus:ring-green-300 transition-all duration-200 flex items-center justify-center mb-4"
                >
                  <FiClock className="w-5 h-5 mr-2" />
                  Check Availability
                </button>

                {showAvailability && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Select Date
                      </label>
                      <input
                        type="date"
                        value={selectedDate}
                        onChange={(e) => handleDateChange(e.target.value)}
                        min={getMinDate()}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    {selectedDate && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Available Time Slots
                        </label>
                        {loadingSlots ? (
                          <div className="flex items-center justify-center py-4">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                          </div>
                        ) : availableSlots.length > 0 ? (
                          <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                            {availableSlots.map((slot) => (
                              <div
                                key={slot}
                                className="px-3 py-2 bg-green-50 border border-green-200 rounded-lg text-center text-sm font-medium text-green-700 flex items-center justify-center"
                              >
                                <FiCheck className="w-4 h-4 mr-1" />
                                {slot}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-4 text-gray-500 text-sm">
                            No available slots for this date
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>

              <button
                onClick={() => onNavigate(`services/${serviceId}/booking`)}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 focus:ring-4 focus:ring-blue-300 transition-all duration-200 flex items-center justify-center"
              >
                <FiCalendar className="w-5 h-5 mr-2" />
                Book This Service
              </button>

              <p className="text-sm text-gray-600 text-center mt-4">
                Book a consultation to discuss your needs and schedule your appointment.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceDetail;
