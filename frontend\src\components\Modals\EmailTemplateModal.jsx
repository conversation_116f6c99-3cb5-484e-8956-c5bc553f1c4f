import React, { useState, useEffect } from 'react'
import { FiX, FiMail, FiSave, FiEye, FiCode, FiTag } from 'react-icons/fi'
import { useToast } from '../../contexts/ToastContext'

const EmailTemplateModal = ({
  isOpen,
  onClose,
  onSave,
  template,
  loading = false
}) => {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    type: '',
    subject: '',
    content: '',
    variables: [],
    isActive: true
  })
  const [previewMode, setPreviewMode] = useState(false)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (template) {
      setFormData({
        type: template.type || '',
        subject: template.subject || '',
        content: template.content || '',
        variables: template.variables || [],
        isActive: template.isActive !== undefined ? template.isActive : true
      })
    }
  }, [template])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setSaving(true)
    try {
      await onSave(formData)
      onClose()
    } catch (error) {
      console.error('Error saving email template:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleVariableAdd = () => {
    // Create a custom input toast for variable name
    let variableName = '';
    const inputToast = toast((t) => (
      <div className="flex flex-col gap-3">
        <div className="font-medium text-gray-900">
          Add Variable
        </div>
        <div className="text-sm text-gray-600">
          Enter variable name (without {'{{}}'})
        </div>
        <input
          type="text"
          placeholder="customerName"
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          onChange={(e) => variableName = e.target.value}
          onKeyPress={(e) => {
            if (e.key === 'Enter' && variableName && !formData.variables.includes(variableName)) {
              toast.dismiss(t.id);
              setFormData(prev => ({
                ...prev,
                variables: [...prev.variables, variableName]
              }));
            }
          }}
          autoFocus
        />
        <div className="flex gap-2 justify-end">
          <button
            onClick={() => toast.dismiss(t.id)}
            className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={() => {
              if (variableName && !formData.variables.includes(variableName)) {
                toast.dismiss(t.id);
                setFormData(prev => ({
                  ...prev,
                  variables: [...prev.variables, variableName]
                }));
              }
            }}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Add
          </button>
        </div>
      </div>
    ), {
      duration: Infinity,
      position: 'top-center',
      style: {
        background: '#fff',
        color: '#374151',
        border: '1px solid #e5e7eb',
        borderRadius: '12px',
        padding: '20px',
        minWidth: '400px',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      }
    });
  }

  const handleVariableRemove = (index) => {
    setFormData(prev => ({
      ...prev,
      variables: prev.variables.filter((_, i) => i !== index)
    }))
  }

  const getTemplateDisplayName = (type) => {
    return type.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[80] p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <FiMail className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                Edit Email Template
              </h2>
              <p className="text-gray-600">
                {template ? getTemplateDisplayName(template.type) : 'Email Template'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setPreviewMode(!previewMode)}
              className={`px-3 py-2 rounded-lg transition-colors duration-200 flex items-center space-x-2 ${
                previewMode 
                  ? 'bg-gray-100 text-gray-700' 
                  : 'bg-blue-50 text-blue-700 hover:bg-blue-100'
              }`}
            >
              {previewMode ? <FiCode className="w-4 h-4" /> : <FiEye className="w-4 h-4" />}
              <span>{previewMode ? 'Edit' : 'Preview'}</span>
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              <FiX className="w-6 h-6 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {previewMode ? (
            /* Preview Mode */
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">Email Preview</h3>
                <div className="bg-white border rounded-lg p-4">
                  <div className="border-b pb-2 mb-4">
                    <p className="text-sm text-gray-600">Subject:</p>
                    <p className="font-semibold">{formData.subject}</p>
                  </div>
                  <div 
                    className="prose max-w-none"
                    dangerouslySetInnerHTML={{ __html: formData.content }}
                  />
                </div>
              </div>
              
              {formData.variables.length > 0 && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-2">Available Variables:</h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.variables.map((variable, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 text-sm rounded">
                        {`{{${variable}}}`}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            /* Edit Mode */
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Template Type (Read-only) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Type
                </label>
                <input
                  type="text"
                  value={getTemplateDisplayName(formData.type)}
                  disabled
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                />
              </div>

              {/* Subject */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Subject *
                </label>
                <input
                  type="text"
                  value={formData.subject}
                  onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter email subject..."
                  required
                />
              </div>

              {/* Content */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Content (HTML) *
                </label>
                <textarea
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  rows={12}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                  placeholder="Enter email content (HTML)..."
                  required
                />
              </div>

              {/* Variables */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Template Variables
                  </label>
                  <button
                    type="button"
                    onClick={handleVariableAdd}
                    className="px-3 py-1 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors duration-200 text-sm flex items-center space-x-1"
                  >
                    <FiTag className="w-3 h-3" />
                    <span>Add Variable</span>
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.variables.map((variable, index) => (
                    <div key={index} className="flex items-center bg-blue-50 text-blue-700 px-2 py-1 rounded text-sm">
                      <span>{`{{${variable}}}`}</span>
                      <button
                        type="button"
                        onClick={() => handleVariableRemove(index)}
                        className="ml-2 text-blue-500 hover:text-blue-700"
                      >
                        <FiX className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
                {formData.variables.length === 0 && (
                  <p className="text-gray-500 text-sm">No variables defined. Click "Add Variable" to add template variables.</p>
                )}
              </div>

              {/* Active Status */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                  Template is active
                </label>
              </div>
            </form>
          )}
        </div>

        {/* Footer */}
        {!previewMode && (
          <div className="flex justify-end space-x-4 p-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={saving || loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50"
            >
              <FiSave className="w-4 h-4" />
              <span>{saving ? 'Saving...' : 'Save Template'}</span>
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default EmailTemplateModal
