import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import { type User } from '../utils/api'
import { settingsAPI, type AllSettings } from '../utils/settingsAPI'

interface SettingsPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function SettingsPage({ currentUser, onLogout }: SettingsPageProps) {
  const navigate = useNavigate();
  const [settings, setSettings] = useState<AllSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'general' | 'hours' | 'payment'>('general');

  useEffect(() => {
    if (!currentUser || currentUser.role !== 'admin') {
      navigate('/');
      return;
    }

    fetchSettings();
  }, [currentUser, navigate]);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await settingsAPI.getAllSettings();
      if (response.success) {
        setSettings(response.data);
      } else {
        console.error('Failed to fetch settings');
        setError('Failed to load settings. Please try again.');
        // Set default settings if fetch fails
        setSettings({
          site: {
            general: {
              siteName: '',
              siteDescription: '',
              contactPhone: '',
              contactEmail: '',
              address: '',
              timezone: 'America/New_York',
              currency: 'USD',
              language: 'en'
            },
            features: {
              enableAppointments: true,
              enableEcommerce: false,
              enableReviews: false,
              enableLoyaltyProgram: false,
              enableGiftCards: false,
              enableWaitlist: false,
              enableReferrals: false
            },
            notifications: {
              emailNotifications: true,
              smsNotifications: false,
              pushNotifications: false,
              appointmentReminders: true,
              marketingEmails: false
            }
          },
          payment: {
            methods: {
              cashApp: { enabled: false, handle: '' },
              zelle: { enabled: false, email: '', phone: '' },
              venmo: { enabled: false, handle: '' },
              paypal: { enabled: false, email: '' }
            },
            policies: {
              requireDeposit: false,
              depositAmount: 0,
              depositPercentage: 0,
              cancellationPolicy: '',
              refundPolicy: ''
            }
          },
          theme: {
            colors: {
              primary: '',
              secondary: '',
              accent: '',
              background: '',
              text: ''
            },
            fonts: {
              heading: '',
              body: ''
            },
            layout: {
              headerStyle: '',
              footerStyle: '',
              sidebarPosition: ''
            }
          },
          seo: {
            meta: {
              title: '',
              description: '',
              keywords: '',
              author: ''
            },
            social: {
              ogTitle: '',
              ogDescription: '',
              ogImage: '',
              twitterCard: ''
            },
            analytics: {
              googleAnalyticsId: '',
              facebookPixelId: '',
              googleTagManagerId: ''
            }
          }
        });
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      setError('Failed to load settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    if (!settings) return;

    try {
      // Save all settings sections
      await Promise.all([
        settingsAPI.updateSiteSettings(settings.site),
        settingsAPI.updatePaymentSettings(settings.payment),
        settingsAPI.updateThemeSettings(settings.theme),
        settingsAPI.updateSEOSettings(settings.seo)
      ]);

      alert('Settings saved successfully!');
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Failed to save settings. Please try again.');
    }
  };

  const handleSiteSettingChange = (field: string, value: any) => {
    if (!settings) return;
    setSettings(prev => ({
      ...prev!,
      site: {
        ...prev!.site,
        general: {
          ...prev!.site.general,
          [field]: value
        }
      }
    }));
  };

  const handlePaymentSettingChange = (method: string, field: string, value: any) => {
    if (!settings) return;
    setSettings(prev => {
      if (!prev) return prev;
      const currentMethod = prev.payment.methods[method as keyof typeof prev.payment.methods];
      return {
        ...prev,
        payment: {
          ...prev.payment,
          methods: {
            ...prev.payment.methods,
            [method]: {
              ...currentMethod,
              [field]: value
            }
          }
        }
      };
    });
  };

  if (loading) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="loading-container">
            <p>Loading settings...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="error-container">
            <p className="error-message">{error}</p>
            <button
              className="btn-primary"
              onClick={() => {
                setError(null);
                fetchSettings();
              }}
            >
              Try Again
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <Header currentUser={currentUser} onLogout={onLogout} />

      <main className="main-content">
        <div className="page-header">
          <h1>Settings</h1>
          <button
            className="btn-primary"
            onClick={handleSaveSettings}
          >
            Save Settings
          </button>
        </div>

        <div className="settings-tabs">
          <button
            className={`settings-tab ${activeTab === 'general' ? 'active' : ''}`}
            onClick={() => setActiveTab('general')}
          >
            General
          </button>
          <button
            className={`settings-tab ${activeTab === 'hours' ? 'active' : ''}`}
            onClick={() => setActiveTab('hours')}
          >
            Working Hours
          </button>
          <button
            className={`settings-tab ${activeTab === 'payment' ? 'active' : ''}`}
            onClick={() => setActiveTab('payment')}
          >
            Payment Methods
          </button>
        </div>

        {activeTab === 'general' && settings && (
          <div className="settings-section">
            <h2>General Settings</h2>
            <div className="settings-form">
              <div className="form-group">
                <label>Site Name</label>
                <input
                  type="text"
                  value={settings.site.general.siteName}
                  onChange={(e) => handleSiteSettingChange('siteName', e.target.value)}
                />
              </div>

              <div className="form-group">
                <label>Site Description</label>
                <textarea
                  value={settings.site.general.siteDescription}
                  onChange={(e) => handleSiteSettingChange('siteDescription', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="form-group">
                <label>Contact Phone</label>
                <input
                  type="text"
                  value={settings.site.general.contactPhone}
                  onChange={(e) => handleSiteSettingChange('contactPhone', e.target.value)}
                />
              </div>

              <div className="form-group">
                <label>Contact Email</label>
                <input
                  type="email"
                  value={settings.site.general.contactEmail}
                  onChange={(e) => handleSiteSettingChange('contactEmail', e.target.value)}
                />
              </div>

              <div className="form-group">
                <label>Address</label>
                <input
                  type="text"
                  value={settings.site.general.address}
                  onChange={(e) => handleSiteSettingChange('address', e.target.value)}
                />
              </div>

              <div className="form-group">
                <label>Timezone</label>
                <select
                  value={settings.site.general.timezone}
                  onChange={(e) => handleSiteSettingChange('timezone', e.target.value)}
                >
                  <option value="America/New_York">Eastern Time</option>
                  <option value="America/Chicago">Central Time</option>
                  <option value="America/Denver">Mountain Time</option>
                  <option value="America/Los_Angeles">Pacific Time</option>
                </select>
              </div>

              <div className="form-group">
                <label>Currency</label>
                <select
                  value={settings.site.general.currency}
                  onChange={(e) => handleSiteSettingChange('currency', e.target.value)}
                >
                  <option value="USD">USD ($)</option>
                  <option value="EUR">EUR (€)</option>
                  <option value="GBP">GBP (£)</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'hours' && settings && (
          <div className="settings-section">
            <h2>Working Hours</h2>
            <div className="settings-form">
              <p>Working hours management will be available in a future update.</p>
              <p>For now, you can manage business hours through the business profile settings.</p>
            </div>
          </div>
        )}

        {activeTab === 'payment' && settings && (
          <div className="settings-section">
            <h2>Payment Methods</h2>
            <div className="settings-form">
              <div className="payment-method-group">
                <h3>Cash App</h3>
                <div className="form-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.payment.methods.cashApp.enabled}
                      onChange={(e) => handlePaymentSettingChange('cashApp', 'enabled', e.target.checked)}
                    />
                    Enable Cash App
                  </label>
                </div>
                <div className="form-group">
                  <label>Cash App Handle</label>
                  <input
                    type="text"
                    value={settings.payment.methods.cashApp.handle}
                    onChange={(e) => handlePaymentSettingChange('cashApp', 'handle', e.target.value)}
                    placeholder="$username"
                    disabled={!settings.payment.methods.cashApp.enabled}
                  />
                </div>
              </div>

              <div className="payment-method-group">
                <h3>Zelle</h3>
                <div className="form-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={settings.payment.methods.zelle.enabled}
                      onChange={(e) => handlePaymentSettingChange('zelle', 'enabled', e.target.checked)}
                    />
                    Enable Zelle
                  </label>
                </div>
                <div className="form-group">
                  <label>Zelle Email</label>
                  <input
                    type="email"
                    value={settings.payment.methods.zelle.email}
                    onChange={(e) => handlePaymentSettingChange('zelle', 'email', e.target.value)}
                    placeholder="<EMAIL>"
                    disabled={!settings.payment.methods.zelle.enabled}
                  />
                </div>
                <div className="form-group">
                  <label>Zelle Phone</label>
                  <input
                    type="text"
                    value={settings.payment.methods.zelle.phone}
                    onChange={(e) => handlePaymentSettingChange('zelle', 'phone', e.target.value)}
                    placeholder="(*************"
                    disabled={!settings.payment.methods.zelle.enabled}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
