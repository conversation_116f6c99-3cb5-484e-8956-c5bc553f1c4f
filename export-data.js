const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');

// Source database connection
const SOURCE_URI = 'mongodb+srv://inspiredolajosh:<EMAIL>/MicrolocsHq?retryWrites=true&w=majority';
const SOURCE_DB = 'MicrolocsHq';

// Export directory
const EXPORT_DIR = './mongodb-export';

// Collections to export
const COLLECTIONS_TO_EXPORT = [
  'users',
  'services', 
  'appointments',
  'products',
  'categories',
  'orders',
  'carts',
  'reviews',
  'paymentconfirmations',
  'brandings',
  'media',
  'emailtemplates',
  'tokenblacklists'
];

class DataExporter {
  constructor() {
    this.client = null;
    this.exportLog = [];
  }

  async connect() {
    console.log('🔗 Connecting to source database...');
    
    try {
      this.client = new MongoClient(SOURCE_URI);
      await this.client.connect();
      await this.client.db(SOURCE_DB).admin().ping();
      console.log('✅ Connected to source database');
    } catch (error) {
      console.error('❌ Connection failed:', error.message);
      throw error;
    }
  }

  async createExportDirectory() {
    if (!fs.existsSync(EXPORT_DIR)) {
      fs.mkdirSync(EXPORT_DIR, { recursive: true });
      console.log(`📁 Created export directory: ${EXPORT_DIR}`);
    }
  }

  async exportCollection(collectionName) {
    try {
      console.log(`\n📦 Exporting collection: ${collectionName}`);
      
      const db = this.client.db(SOURCE_DB);
      const collection = db.collection(collectionName);

      // Get document count
      const totalDocs = await collection.countDocuments();
      console.log(`   📊 Total documents: ${totalDocs}`);

      if (totalDocs === 0) {
        console.log(`   ⚠️  Collection ${collectionName} is empty, skipping...`);
        this.exportLog.push({
          collection: collectionName,
          status: 'skipped',
          reason: 'empty',
          documents: 0
        });
        return;
      }

      // Export documents
      const documents = await collection.find({}).toArray();
      
      // Save to JSON file
      const filePath = path.join(EXPORT_DIR, `${collectionName}.json`);
      fs.writeFileSync(filePath, JSON.stringify(documents, null, 2));
      
      console.log(`   ✅ Exported ${documents.length} documents to ${filePath}`);
      this.exportLog.push({
        collection: collectionName,
        status: 'success',
        documents: documents.length,
        filePath: filePath
      });

    } catch (error) {
      console.error(`   ❌ Failed to export ${collectionName}:`, error.message);
      this.exportLog.push({
        collection: collectionName,
        status: 'failed',
        error: error.message,
        documents: 0
      });
    }
  }

  async getAvailableCollections() {
    try {
      const db = this.client.db(SOURCE_DB);
      const collections = await db.listCollections().toArray();
      return collections.map(col => col.name);
    } catch (error) {
      console.error('❌ Failed to get collections:', error.message);
      throw error;
    }
  }

  async generateExportReport() {
    console.log('\n📊 Export Report:');
    console.log('='.repeat(50));
    
    let totalSuccess = 0;
    let totalFailed = 0;
    let totalDocuments = 0;

    this.exportLog.forEach(log => {
      const status = log.status === 'success' ? '✅' : 
                    log.status === 'failed' ? '❌' : '⚠️';
      console.log(`${status} ${log.collection}: ${log.documents} documents`);
      
      if (log.status === 'success') {
        totalSuccess++;
        totalDocuments += log.documents;
      } else if (log.status === 'failed') {
        totalFailed++;
        console.log(`   Error: ${log.error}`);
      }
    });

    console.log('='.repeat(50));
    console.log(`📈 Summary:`);
    console.log(`   ✅ Successful collections: ${totalSuccess}`);
    console.log(`   ❌ Failed collections: ${totalFailed}`);
    console.log(`   📄 Total documents exported: ${totalDocuments}`);

    // Save report to file
    const reportPath = path.join(EXPORT_DIR, 'export-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      sourceUri: SOURCE_URI.replace(/\/\/.*@/, '//***:***@'),
      exportDirectory: EXPORT_DIR,
      summary: {
        successfulCollections: totalSuccess,
        failedCollections: totalFailed,
        totalDocuments: totalDocuments
      },
      details: this.exportLog
    }, null, 2));

    console.log(`📋 Export report saved to: ${reportPath}`);
  }

  async disconnect() {
    try {
      if (this.client) {
        await this.client.close();
        console.log('🔌 Disconnected from source database');
      }
    } catch (error) {
      console.error('❌ Error during disconnect:', error.message);
    }
  }

  async export() {
    try {
      console.log('🚀 Starting data export...');
      console.log(`📍 Source: ${SOURCE_URI.replace(/\/\/.*@/, '//***:***@')}`);
      console.log(`📁 Export Directory: ${EXPORT_DIR}`);
      
      await this.connect();
      await this.createExportDirectory();
      
      // Get available collections
      const availableCollections = await this.getAvailableCollections();
      console.log(`📋 Available collections: ${availableCollections.length}`);
      
      // Filter collections to export
      const collectionsToExport = COLLECTIONS_TO_EXPORT.filter(col => 
        availableCollections.includes(col)
      );

      console.log(`\n📦 Collections to export: ${collectionsToExport.length}`);
      collectionsToExport.forEach(name => console.log(`   - ${name}`));

      // Export each collection
      for (const collectionName of collectionsToExport) {
        await this.exportCollection(collectionName);
      }

      // Generate report
      await this.generateExportReport();

      console.log('\n🎉 Data export completed successfully!');
      console.log(`📁 All data exported to: ${path.resolve(EXPORT_DIR)}`);
      console.log('\n📝 Next steps:');
      console.log('   1. Review the exported JSON files');
      console.log('   2. Run the import script to load data into the new database');

    } catch (error) {
      console.error('❌ Export failed:', error.message);
      throw error;
    } finally {
      await this.disconnect();
    }
  }
}

// Main execution
async function main() {
  const exporter = new DataExporter();
  
  try {
    await exporter.export();
  } catch (error) {
    console.error('💥 Export process failed:', error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Export interrupted by user');
  process.exit(0);
});

// Run export if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = DataExporter;
