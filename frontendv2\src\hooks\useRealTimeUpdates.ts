import { useEffect, useRef, useCallback } from 'react';
import { adminAPI } from '../utils/api';

interface UseRealTimeUpdatesOptions {
  enabled?: boolean;
  interval?: number; // in milliseconds
  onUpdate?: (data: any) => void;
  onError?: (error: Error) => void;
}

export function useRealTimeUpdates({
  enabled = true,
  interval = 30000, // 30 seconds default
  onUpdate,
  onError
}: UseRealTimeUpdatesOptions = {}) {
  const intervalRef = useRef<number | null>(null);
  const lastUpdateRef = useRef<string | null>(null);

  const checkForUpdates = useCallback(async () => {
    try {
      // Get dashboard data to check for updates
      const response = await adminAPI.getDashboard();
      
      if (response.success) {
        const currentUpdate = JSON.stringify(response.data);
        
        // Check if data has changed
        if (lastUpdateRef.current && lastUpdateRef.current !== currentUpdate) {
          onUpdate?.(response.data);
        }
        
        lastUpdateRef.current = currentUpdate;
      }
    } catch (error) {
      onError?.(error instanceof Error ? error : new Error('Failed to check for updates'));
    }
  }, [onUpdate, onError]);

  const startPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (enabled) {
      // Initial check
      checkForUpdates();
      
      // Set up polling
      intervalRef.current = setInterval(checkForUpdates, interval) as unknown as number;
    }
  }, [enabled, interval, checkForUpdates]);

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  const forceUpdate = useCallback(() => {
    checkForUpdates();
  }, [checkForUpdates]);

  useEffect(() => {
    startPolling();

    return () => {
      stopPolling();
    };
  }, [startPolling, stopPolling]);

  // Handle visibility change to pause/resume polling
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPolling();
      } else {
        startPolling();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [startPolling, stopPolling]);

  return {
    forceUpdate,
    startPolling,
    stopPolling,
    isPolling: intervalRef.current !== null
  };
}

// Hook for appointment-specific real-time updates
export function useAppointmentUpdates({
  enabled = true,
  interval = 15000, // 15 seconds for appointments
  onAppointmentUpdate,
  onError
}: {
  enabled?: boolean;
  interval?: number;
  onAppointmentUpdate?: (appointments: any[]) => void;
  onError?: (error: Error) => void;
} = {}) {
  const intervalRef = useRef<number | null>(null);
  const lastAppointmentsRef = useRef<string | null>(null);

  const checkAppointmentUpdates = useCallback(async () => {
    try {
      // Get recent appointments
      const response = await adminAPI.getAppointments({
        page: 1,
        limit: 50,
        sortBy: 'updatedAt',
        sortOrder: 'desc'
      });
      
      if (response.success) {
        const currentAppointments = JSON.stringify(response.data.appointments);
        
        // Check if appointments have changed
        if (lastAppointmentsRef.current && lastAppointmentsRef.current !== currentAppointments) {
          onAppointmentUpdate?.(response.data.appointments);
        }
        
        lastAppointmentsRef.current = currentAppointments;
      }
    } catch (error) {
      onError?.(error instanceof Error ? error : new Error('Failed to check appointment updates'));
    }
  }, [onAppointmentUpdate, onError]);

  const startPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (enabled) {
      // Initial check
      checkAppointmentUpdates();
      
      // Set up polling
      intervalRef.current = setInterval(checkAppointmentUpdates, interval) as unknown as number;
    }
  }, [enabled, interval, checkAppointmentUpdates]);

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  const forceUpdate = useCallback(() => {
    checkAppointmentUpdates();
  }, [checkAppointmentUpdates]);

  useEffect(() => {
    startPolling();

    return () => {
      stopPolling();
    };
  }, [startPolling, stopPolling]);

  // Handle visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPolling();
      } else {
        startPolling();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [startPolling, stopPolling]);

  return {
    forceUpdate,
    startPolling,
    stopPolling,
    isPolling: intervalRef.current !== null
  };
}

// Hook for notification system
export function useNotifications() {
  const showNotification = useCallback((message: string, type: 'success' | 'error' | 'info' = 'info') => {
    // Simple notification system - could be enhanced with a proper notification library
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '12px 20px',
      borderRadius: '8px',
      color: 'white',
      fontWeight: '600',
      zIndex: '9999',
      maxWidth: '400px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      backgroundColor: type === 'success' ? '#059669' : type === 'error' ? '#dc2626' : '#3b82f6'
    });

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);

    // Add click to dismiss
    notification.addEventListener('click', () => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    });
  }, []);

  return { showNotification };
}

// Combined hook for comprehensive real-time updates
export function useAdminRealTime({
  enabled = true,
  onDashboardUpdate,
  onAppointmentUpdate,
  onError
}: {
  enabled?: boolean;
  onDashboardUpdate?: (data: any) => void;
  onAppointmentUpdate?: (appointments: any[]) => void;
  onError?: (error: Error) => void;
} = {}) {
  const { showNotification } = useNotifications();

  const dashboardUpdates = useRealTimeUpdates({
    enabled,
    interval: 30000,
    onUpdate: onDashboardUpdate,
    onError
  });

  const appointmentUpdates = useAppointmentUpdates({
    enabled,
    interval: 15000,
    onAppointmentUpdate: (appointments) => {
      onAppointmentUpdate?.(appointments);
      showNotification('Appointments updated', 'info');
    },
    onError
  });

  return {
    dashboard: dashboardUpdates,
    appointments: appointmentUpdates,
    showNotification,
    forceUpdateAll: () => {
      dashboardUpdates.forceUpdate();
      appointmentUpdates.forceUpdate();
    }
  };
}
