import { useState } from 'react';

interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
}

interface BookingState {
  selectedService: any;
  selectedAddOns: AddOnService[];
  selectedDate: string;
  selectedTime: string;
  step: 'services' | 'datetime' | 'details' | 'checkout';
  customerInfo?: any;
}

interface CheckoutProps {
  booking: BookingState;
  onBack: () => void;
  onComplete: () => void;
}

export default function Checkout({ booking, onBack, onComplete }: CheckoutProps) {
  const [couponCode, setCouponCode] = useState('');
  const [showCouponInput, setShowCouponInput] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('total'); // 'total' or 'deposit'
  const [paymentProof, setPaymentProof] = useState<File | null>(null);
  const [paymentSource, setPaymentSource] = useState(''); // 'cashapp' or 'zelle'
  const [isUploading, setIsUploading] = useState(false);

  const calculateSubtotal = () => {
    const servicePrice = booking.selectedService ? parseFloat(booking.selectedService.price) : 0;
    const addOnsPrice = booking.selectedAddOns.reduce((total, addOn) => total + addOn.price, 0);
    return servicePrice + addOnsPrice;
  };

  const subtotal = calculateSubtotal();

  const handlePaymentMethodChange = (method: string) => {
    setPaymentMethod(method);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setPaymentProof(file);
    }
  };

  // const handleContinuePayment = () => {
  //   // Here you would typically integrate with a payment processor
  //   // For now, we'll just complete the booking
  //   onComplete();
  // };

  const handleUploadPayment = async () => {
    if (paymentProof && paymentSource) {
      // Check file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (paymentProof.size > maxSize) {
        alert('File is too large. Please select a file smaller than 10MB.');
        return;
      }

      setIsUploading(true);
      try {
        // Create FormData for file upload
        const formData = new FormData();
        formData.append('file', paymentProof);

        // Add email and appointment data to form
        formData.append('email', booking.customerInfo?.email || '');
        formData.append('appointmentData', JSON.stringify({
          ...booking,
          paymentMethod: paymentSource,
          paymentAmount: subtotal,
          userId: booking.customerInfo?.userId
        }));

        console.log('Uploading payment proof:', paymentProof.name, 'Size:', (paymentProof.size / 1024 / 1024).toFixed(2) + 'MB');

        // Upload to our backend endpoint
        const response = await fetch('http://localhost:3000/api/upload/payment-proof', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (!response.ok) {
          // Handle specific error messages from backend
          const errorMessage = data.message || 'Failed to upload payment proof';
          throw new Error(errorMessage);
        }

        console.log('Payment proof uploaded:', data.data?.url);
        console.log('Appointment data saved:', data.data?.appointmentData);

        // Save payment proof URL to localStorage for appointment creation
        if (data.data?.url) {
          localStorage.setItem('paymentProofUrl', data.data.url);
          localStorage.setItem('paymentProofData', JSON.stringify({
            url: data.data.url,
            amount: subtotal,
            paymentMethod: paymentSource,
            notes: `Payment proof uploaded via ${paymentSource}`,
            uploadedAt: data.data.uploadedAt
          }));
          console.log('Payment proof data saved to localStorage');
        }

        alert('Payment proof uploaded successfully! Your booking will be confirmed once payment is verified.');
        onComplete();
      } catch (error: any) {
        console.error('Error uploading payment proof:', error);

        // Provide user-friendly error messages
        let errorMessage = 'Failed to upload payment proof. Please try again.';

        if (error.message?.includes('timeout') || error.message?.includes('Timeout')) {
          errorMessage = 'Upload timed out. Please check your internet connection and try again with a smaller file.';
        } else if (error.message?.includes('too large')) {
          errorMessage = 'File is too large. Please use a file smaller than 10MB.';
        } else if (error.message?.includes('Invalid file type')) {
          errorMessage = 'Invalid file type. Please use JPEG, PNG, GIF, WebP, or PDF files only.';
        } else if (error.message && error.message !== 'Failed to upload payment proof') {
          errorMessage = error.message;
        }

        alert(errorMessage);
      } finally {
        setIsUploading(false);
      }
    } else {
      alert('Please select a payment proof file and specify payment method.');
    }
  };

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <div className="auth-links">
            <a href="#" className="auth-link">SIGN UP</a>
            <a href="#" className="auth-link">LOG IN</a>
          </div>
        </div>
      </header>

      <main className="main-content">
        <div className="checkout-container">
          <div className="checkout-header">
            <button className="back-button" onClick={onBack}>
              ← YOUR INFORMATION
            </button>
            <h2 className="checkout-title">Checkout</h2>
          </div>

          <div className="checkout-content">
            {/* Payment Section */}
            <div className="payment-section">
              <h3>PAYMENT</h3>
              
              <div className="payment-info">
                <h4>Payment information</h4>

                <div className="payment-options-section">
                  <div className="payment-method-card">
                    <div className="payment-method-info">
                      <span>📄 Upload Payment Proof</span>
                      <p className="payment-note">Upload screenshot or receipt of your payment</p>
                    </div>

                    <div className="payment-source-selection">
                      <h5>Where did you send the payment?</h5>
                      <div className="payment-source-options">
                        <label className="payment-source-option">
                          <input
                            type="radio"
                            name="paymentSource"
                            value="cashapp"
                            checked={paymentSource === 'cashapp'}
                            onChange={(e) => setPaymentSource(e.target.value)}
                          />
                          <span>Cash App</span>
                        </label>
                        <label className="payment-source-option">
                          <input
                            type="radio"
                            name="paymentSource"
                            value="zelle"
                            checked={paymentSource === 'zelle'}
                            onChange={(e) => setPaymentSource(e.target.value)}
                          />
                          <span>Zelle</span>
                        </label>
                      </div>
                    </div>

                    <div className="upload-section">
                      <div className="file-upload-wrapper">
                        <input
                          type="file"
                          id="payment-proof"
                          accept="image/*,.pdf"
                          onChange={handleFileUpload}
                          className="file-input"
                        />
                        <label htmlFor="payment-proof" className="file-upload-label">
                          {paymentProof ? paymentProof.name : 'Choose File'}
                        </label>
                      </div>

                      <button
                        className="payment-button upload-button"
                        onClick={handleUploadPayment}
                        disabled={!paymentProof || !paymentSource || isUploading}
                      >
                        {isUploading ? (
                          <div className="loading-spinner">
                            <div className="spinner"></div>
                            UPLOADING...
                          </div>
                        ) : (
                          'UPLOAD PAYMENT PROOF'
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="order-summary">
              <h4>Order summary</h4>
              
              <div className="order-details">
                <div className="service-line">
                  <span className="service-name">{booking.selectedService?.name}</span>
                  <span className="service-price">${booking.selectedService?.price}</span>
                </div>
                
                <div className="service-date">
                  {booking.selectedDate ? new Date(booking.selectedDate).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'Date not selected'} at {booking.selectedTime} CDT
                </div>

                {booking.selectedAddOns.map(addOn => (
                  <div key={addOn.id} className="addon-line">
                    <span>+ {addOn.name}</span>
                    <span>${addOn.price.toFixed(2)}</span>
                  </div>
                ))}

                <div className="coupon-section">
                  <button 
                    className="coupon-toggle"
                    onClick={() => setShowCouponInput(!showCouponInput)}
                  >
                    Package, gift, or coupon code +
                  </button>
                  
                  {showCouponInput && (
                    <div className="coupon-input">
                      <input
                        type="text"
                        placeholder="Enter code"
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value)}
                      />
                      <button>Apply</button>
                    </div>
                  )}
                </div>

                <div className="pricing-breakdown">
                  <div className="subtotal-line">
                    <span>Subtotal</span>
                    <span>${subtotal.toFixed(2)}</span>
                  </div>

                  <div className="payment-options">
                    <div className="payment-option">
                      <input
                        type="radio"
                        id="total-payment"
                        name="payment-method"
                        value="total"
                        checked={paymentMethod === 'total'}
                        onChange={() => handlePaymentMethodChange('total')}
                      />
                      <label htmlFor="total-payment">
                        <span className="option-label">Total due</span>
                        <span className="option-price">${subtotal.toFixed(2)}</span>
                      </label>
                    </div>

                    <div className="payment-option">
                      <input
                        type="radio"
                        id="deposit-payment"
                        name="payment-method"
                        value="total"
                        checked={paymentMethod === 'total'}
                        onChange={() => handlePaymentMethodChange('total')}
                      />
                      <label htmlFor="deposit-payment">
                        <span className="option-label">Full payment</span>
                        <span className="option-price">${subtotal.toFixed(2)}</span>
                      </label>
                    </div>
                  </div>


                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
