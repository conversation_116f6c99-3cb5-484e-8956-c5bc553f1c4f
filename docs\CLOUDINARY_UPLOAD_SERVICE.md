# Cloudinary Upload Service Documentation

## Overview

This service provides comprehensive file upload functionality using Cloudinary as the cloud storage provider. It supports various upload types, automatic file optimization, and secure file management.

## Features

- ✅ **Cloud Storage**: Files uploaded to Cloudinary for reliable, scalable storage
- ✅ **Multiple Upload Types**: Support for different file categories (profile pictures, products, branding, etc.)
- ✅ **Automatic Optimization**: Built-in image optimization and transformation
- ✅ **Secure Uploads**: Authentication required for all upload operations
- ✅ **File Type Validation**: Supports JPEG, PNG, GIF, WebP, and PDF files
- ✅ **Temporary File Cleanup**: Automatic cleanup of local temporary files
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Legacy Support**: Backward compatibility with existing local upload system

## Setup

### 1. Environment Variables

Add the following to your `.env` file:

```env
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
```

### 2. Cloudinary Account Setup

1. Create a free account at [Cloudinary](https://cloudinary.com/)
2. Get your credentials from the Dashboard
3. Add the credentials to your environment variables

## API Endpoints

### Configuration Check

```http
GET /api/upload/cloudinary/config
```

Checks if Cloudinary is properly configured.

### Unified Upload Routes

#### Single File Upload

```http
POST /api/upload/cloudinary/single/:uploadType
Content-Type: multipart/form-data
Authorization: Bearer <token>

{
  "file": <file>
}
```

**Upload Types:**
- `profilePicture` - User profile images
- `productImage` - Product catalog images
- `serviceImage` - Service images
- `brandingImage` - General branding assets
- `testimonialImage` - Customer testimonial images
- `staffImage` - Staff member photos
- `businessDocs` - Business documents (PDF)
- `logo` - Company logos
- `favicon` - Website favicons
- `heroImage` - Hero/banner images
- `galleryImage` - General gallery images

**Examples:**
```http
POST /api/upload/cloudinary/single/logo
POST /api/upload/cloudinary/single/profilePicture
POST /api/upload/cloudinary/single/heroImage
```

#### Multiple Files Upload

```http
POST /api/upload/cloudinary/multiple/:uploadType
Content-Type: multipart/form-data
Authorization: Bearer <token>

{
  "files": [<file1>, <file2>, ...]
}
```

**Examples:**
```http
POST /api/upload/cloudinary/multiple/productImage
POST /api/upload/cloudinary/multiple/brandingImage
POST /api/upload/cloudinary/multiple/businessDocs
```

### Image Optimization

```http
GET /api/upload/cloudinary/optimize/:imageUrl?width=300&height=200&quality=auto&format=webp&crop=fill
```

Parameters:
- `width`: Target width in pixels
- `height`: Target height in pixels
- `quality`: Image quality (auto, 100, 80, etc.)
- `format`: Output format (webp, jpg, png, etc.)
- `crop`: Crop mode (fill, fit, scale, etc.)

### Delete Image

```http
DELETE /api/upload/cloudinary/image
Content-Type: application/json
Authorization: Bearer <token>

{
  "imageUrl": "https://res.cloudinary.com/..."
}
```

## Upload Types

The service supports the following upload types, each with its own Cloudinary folder:

| Upload Type | Cloudinary Folder | Use Case |
|-------------|-------------------|----------|
| `profilePicture` | `microlocs/profile_pictures` | User profile images |
| `productImage` | `microlocs/products` | Product catalog images |
| `serviceImage` | `microlocs/services` | Service images |
| `brandingImage` | `microlocs/branding` | General branding assets |
| `testimonialImage` | `microlocs/testimonials` | Customer testimonial images |
| `staffImage` | `microlocs/staff` | Staff member photos |
| `businessDocs` | `microlocs/business_documents` | Business documents (PDF) |
| `logo` | `microlocs/branding/logos` | Company logos |
| `favicon` | `microlocs/branding/favicons` | Website favicons |
| `heroImage` | `microlocs/branding/hero` | Hero/banner images |
| `galleryImage` | `microlocs/gallery` | General gallery images |

## File Limits

- **Maximum file size**: 10MB
- **Supported formats**: JPEG, PNG, GIF, WebP, PDF
- **Multiple upload limit**: 10 files per request

## Response Format

### Success Response

```json
{
  "success": true,
  "message": "Image uploaded successfully to Cloudinary",
  "data": {
    "originalName": "example.jpg",
    "size": 1024000,
    "url": "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/microlocs/gallery/abc123.jpg",
    "uploadType": "galleryImage"
  }
}
```

### Error Response

```json
{
  "success": false,
  "message": "Invalid file type. Only JPEG, PNG, GIF, WebP and PDF files are allowed.",
  "error": "INVALID_FILE_TYPE"
}
```

## Usage Examples

### Frontend JavaScript

```javascript
// Single file upload using unified route
const uploadFile = async (file, uploadType = 'galleryImage') => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch(`/api/upload/cloudinary/single/${uploadType}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  return response.json();
};

// Multiple files upload using unified route
const uploadMultipleFiles = async (files, uploadType = 'productImage') => {
  const formData = new FormData();
  files.forEach(file => formData.append('files', file));

  const response = await fetch(`/api/upload/cloudinary/multiple/${uploadType}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  return response.json();
};

// Specific upload examples
const uploadLogo = (file) => uploadFile(file, 'logo');
const uploadProfilePicture = (file) => uploadFile(file, 'profilePicture');
const uploadProductImages = (files) => uploadMultipleFiles(files, 'productImage');
```

### React Component Example

```jsx
import React, { useState } from 'react';
import uploadService from './services/uploadService.js';

const ImageUploader = () => {
  const [uploading, setUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');

  const handleUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setUploading(true);
    try {
      // Using the unified upload service
      const result = await uploadService.uploadFileToCloudinary(file, 'profilePicture');
      if (result.success) {
        setImageUrl(result.data.url);
      }
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <input type="file" onChange={handleUpload} accept="image/*" />
      {uploading && <p>Uploading to cloud storage...</p>}
      {imageUrl && <img src={imageUrl} alt="Uploaded" style={{maxWidth: '200px'}} />}
    </div>
  );
};

// Multiple file upload example
const MultipleFileUploader = ({ uploadType = 'productImage' }) => {
  const [uploading, setUploading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState([]);

  const handleMultipleUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    setUploading(true);
    try {
      const result = await uploadService.uploadFilesToCloudinary(files, uploadType);
      if (result.success) {
        setUploadedFiles(result.data.files);
      }
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <input type="file" multiple onChange={handleMultipleUpload} accept="image/*" />
      {uploading && <p>Uploading {uploadType} files...</p>}
      {uploadedFiles.length > 0 && (
        <div>
          {uploadedFiles.map((file, index) => (
            <img key={index} src={file.url} alt={file.originalName} style={{width: '100px', margin: '5px'}} />
          ))}
        </div>
      )}
    </div>
  );
};
```

## Security

- All upload endpoints require authentication
- File type validation prevents malicious uploads
- Temporary files are automatically cleaned up
- Cloudinary provides additional security features

## Error Handling

The service includes comprehensive error handling for:
- Invalid file types
- File size limits
- Cloudinary upload failures
- Authentication errors
- Missing configuration

## Legacy Support

The service maintains backward compatibility with the existing local upload system. Legacy endpoints remain available:
- `POST /api/upload/image`
- `POST /api/upload/images`
- `GET /api/upload/image/:filename`
- `DELETE /api/upload/image/:filename`
