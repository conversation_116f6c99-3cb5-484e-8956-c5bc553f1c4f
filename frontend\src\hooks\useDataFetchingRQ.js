import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useMemo } from 'react'

/**
 * React Query replacement for useDataFetching
 * Provides the same API but uses React Query under the hood
 */
export function useDataFetching(fetchFunction, dependencies = [], options = {}) {
  const {
    immediate = true,
    queryKey = null,
    enabled = true,
    ...queryOptions
  } = options

  // Generate a query key if not provided
  const generatedKey = useMemo(() => {
    if (queryKey) return queryKey
    // Create a key based on the function name and dependencies
    const functionName = fetchFunction?.name || 'anonymous'
    return [functionName, ...dependencies]
  }, [queryKey, fetchFunction?.name, ...dependencies])

  const query = useQuery({
    queryKey: generatedKey,
    queryFn: fetchFunction,
    enabled: immediate && enabled && !!fetchFunction,
    ...queryOptions,
  })

  // Return the same interface as the old useDataFetching hook
  return {
    data: query.data || null,
    loading: query.isLoading,
    error: query.error?.message || '',
    refetch: query.refetch,
    isLoading: query.isLoading
  }
}

/**
 * React Query replacement for useApiDataFetching
 * Supports both old and new signatures for backward compatibility
 */
export function useApiDataFetching(endpointOrFunction, paramsOrDependencies = {}, fetchFunction, dependencies = []) {
  // Check if this is the old signature (function, dependencies)
  if (typeof endpointOrFunction === 'function') {
    // Old signature: useApiDataFetching(fetchFunction, dependencies)
    const fetchFn = endpointOrFunction
    const deps = Array.isArray(paramsOrDependencies) ? paramsOrDependencies : []

    return useDataFetching(fetchFn, deps, {
      queryKey: [fetchFn?.name || 'api-call', ...deps],
      immediate: true
    })
  } else {
    // New signature: useApiDataFetching(endpoint, params, fetchFunction, dependencies)
    const endpoint = endpointOrFunction
    const params = paramsOrDependencies
    const queryKey = [endpoint, params]

    return useDataFetching(fetchFunction, dependencies, {
      queryKey,
      immediate: true
    })
  }
}

/**
 * React Query replacement for useSimpleDataFetching
 */
export function useSimpleDataFetching(fetchFunction, dependencies = []) {
  return useDataFetching(fetchFunction, dependencies, {
    immediate: true
  })
}

/**
 * React Query replacement for useLazyDataLoader
 */
export function useLazyDataLoader() {
  const queryClient = useQueryClient()

  const loadData = async (key, apiCall, params = {}) => {
    const queryKey = [key, params]
    
    try {
      const data = await queryClient.fetchQuery({
        queryKey,
        queryFn: apiCall,
        staleTime: 5 * 60 * 1000, // 5 minutes
      })
      
      return { success: true, data }
    } catch (error) {
      console.error(`Error loading ${key}:`, error)
      throw error
    }
  }

  const clearData = (key, params = {}) => {
    const queryKey = [key, params]
    queryClient.removeQueries({ queryKey })
  }

  const clearAllData = () => {
    queryClient.clear()
  }

  const hasData = (key, params = {}) => {
    const queryKey = [key, params]
    const data = queryClient.getQueryData(queryKey)
    return !!data
  }

  const getData = (key, params = {}, fallback = null) => {
    const queryKey = [key, params]
    return queryClient.getQueryData(queryKey) || fallback
  }

  const isLoading = (key, params = {}) => {
    const queryKey = [key, params]
    const query = queryClient.getQueryState(queryKey)
    return query?.status === 'loading' || false
  }

  const getError = (key, params = {}) => {
    const queryKey = [key, params]
    const query = queryClient.getQueryState(queryKey)
    return query?.error?.message || null
  }

  return {
    loadData,
    clearData,
    clearAllData,
    hasData,
    getData,
    isLoading,
    getError,
    // Legacy compatibility
    loading: {},
    data: {},
    errors: {}
  }
}

/**
 * React Query replacement for useAdminDataLoader
 */
export function useAdminDataLoader() {
  const queryClient = useQueryClient()

  const loadData = async (key, apiCall, params = {}) => {
    const queryKey = ['admin', key, params]
    
    try {
      const data = await queryClient.fetchQuery({
        queryKey,
        queryFn: apiCall,
        staleTime: 2 * 60 * 1000, // 2 minutes for admin data
      })
      
      return { success: true, data }
    } catch (error) {
      console.error(`Error loading admin ${key}:`, error)
      throw error
    }
  }

  // Specific loaders for admin sections
  const loadDashboardStats = async (adminService) => {
    return loadData('dashboardStats', () => adminService.getDashboardStats())
  }

  const loadAppointments = async (adminService, params = { page: 1, limit: 20 }) => {
    return loadData('appointments', () => adminService.getAppointments(params), params)
  }

  const loadCustomers = async (adminService, params = { page: 1, limit: 20 }) => {
    return loadData('customers', () => adminService.getCustomers(params), params)
  }

  const loadOrders = async (adminService, params = { page: 1, limit: 20 }) => {
    return loadData('orders', () => adminService.getOrders(params), params)
  }

  const loadProducts = async (adminService, params = { page: 1, limit: 20 }) => {
    return loadData('products', () => adminService.getProducts(params), params)
  }

  const loadServices = async (adminService, params = { page: 1, limit: 20 }) => {
    return loadData('services', () => adminService.getServices(params), params)
  }

  // Load data based on tab - each tab only loads its own data
  const loadDataForTab = async (tabId, adminService) => {
    switch (tabId) {
      case 'overview':
        // Overview loads dashboard stats only
        return loadDashboardStats(adminService)
      case 'appointments':
        // Appointments section only loads appointments data
        return loadAppointments(adminService)
      case 'customers':
        return loadCustomers(adminService)
      case 'orders':
        return loadOrders(adminService)
      case 'products':
        return loadProducts(adminService)
      case 'services':
        return loadServices(adminService)
      default:
        return Promise.resolve()
    }
  }

  const clearData = (key, params = {}) => {
    const queryKey = ['admin', key, params]
    queryClient.removeQueries({ queryKey })
  }

  const clearAllData = () => {
    queryClient.removeQueries({ queryKey: ['admin'] })
  }

  const hasData = (key, params = {}) => {
    const queryKey = ['admin', key, params]
    const data = queryClient.getQueryData(queryKey)
    return !!data
  }

  const getData = (key, params = {}, fallback = null) => {
    const queryKey = ['admin', key, params]
    return queryClient.getQueryData(queryKey) || fallback
  }

  const isLoading = (key, params = {}) => {
    const queryKey = ['admin', key, params]
    const query = queryClient.getQueryState(queryKey)
    return query?.status === 'loading' || false
  }

  const getError = (key, params = {}) => {
    const queryKey = ['admin', key, params]
    const query = queryClient.getQueryState(queryKey)
    return query?.error?.message || null
  }

  return {
    loadData,
    clearData,
    clearAllData,
    hasData,
    getData,
    isLoading,
    getError,
    loadDashboardStats,
    loadAppointments,
    loadCustomers,
    loadOrders,
    loadProducts,
    loadServices,
    loadDataForTab,
    // Legacy compatibility
    loading: {},
    data: {},
    errors: {}
  }
}
