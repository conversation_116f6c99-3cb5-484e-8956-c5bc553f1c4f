import { useState, useEffect, useCallback, useRef } from 'react'
import { FiTrash2, FiMinus, FiPlus, FiShoppingBag, FiArrowLeft, FiAlertCircle } from 'react-icons/fi'
import { cartService, productService, authService } from '../../services'

import { useApiDataFetching } from '../../hooks/useDataFetching'
import { useBranding } from '../../contexts/BrandingContext'
import Loading from '../../components/Loading'

const Cart = ({ onNavigate }) => {
  const { branding } = useBranding()

  // Check authentication on component mount
  useEffect(() => {
    if (!authService.isAuthenticated()) {
      // Redirect to login if not authenticated
      onNavigate('login')
      return
    }
  }, [])
  const [cartItems, setCartItems] = useState([])
  const [updatingItems, setUpdatingItems] = useState(new Set())

  // Use ref to prevent loading state from causing infinite loops
  const loadingRef = useRef(false)

  // Memoized function to load cart data - excludes loading from dependencies
  const loadCartData = useCallback(async () => {
    if (loadingRef.current) return

    try {
      loadingRef.current = true

      // Load cart and recommended products in parallel
      const [cartResponse, productsResponse] = await Promise.all([
        cartService.getCart(),
        productService.getProducts({ featured: true, limit: 4 })
      ])

      const result = {
        cartItems: [],
        recommendedProducts: []
      }

      if (cartResponse.success) {
        result.cartItems = cartResponse.data.items || []
      }

      if (productsResponse.success) {
        result.recommendedProducts = productsResponse.data.slice(0, 4)
      }

      return result
    } catch (error) {
      console.error('Error loading cart:', error)
      throw error
    } finally {
      loadingRef.current = false
    }
  }, []) // No dependencies that would cause recreation

  // Use the custom hook for cart data loading
  const {
    data: cartData,
    loading: isLoading,
    error,
    refetch: refetchCartData
  } = useApiDataFetching(
    '/cart',
    {},
    loadCartData,
    []
  )

  // Extract data from the hook result and update local state
  useEffect(() => {
    if (cartData) {
      setCartItems(cartData.cartItems)
    }
  }, [cartData])

  const recommendedProducts = cartData?.recommendedProducts || []

  const updateQuantity = async (itemId, newQuantity) => {
    if (newQuantity === 0) {
      removeItem(itemId)
      return
    }

    try {
      setUpdatingItems(prev => new Set(prev).add(itemId))

      const response = await cartService.updateCartItem(itemId, {
        quantity: newQuantity
      })

      if (response.success) {
        setCartItems(items =>
          items.map(item =>
            item.id === itemId ? { ...item, quantity: newQuantity } : item
          )
        )
      }
    } catch (error) {
      console.error('Error updating quantity:', error)
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: 'Failed to update quantity. Please try again.'
        }
      }))
    } finally {
      setUpdatingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(itemId)
        return newSet
      })
    }
  }

  const removeItem = async (itemId) => {
    try {
      setUpdatingItems(prev => new Set(prev).add(itemId))

      const response = await cartService.removeFromCart(itemId)

      if (response.success) {
        setCartItems(items => items.filter(item => item.id !== itemId))
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'success',
            message: 'Item removed from cart'
          }
        }))
      }
    } catch (error) {
      console.error('Error removing item:', error)
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: 'Failed to remove item. Please try again.'
        }
      }))
    } finally {
      setUpdatingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(itemId)
        return newSet
      })
    }
  }

  const subtotal = cartItems.reduce((sum, item) => {
    const price = item.product?.price || item.price || 0
    return sum + (price * item.quantity)
  }, 0)
  const shipping = subtotal > 50 ? 0 : 8.99
  const tax = subtotal * 0.08
  const total = subtotal + shipping + tax

  // Show loading state
  if (isLoading) {
    return <Loading message="Loading your cart..." />
  }

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {error && (
            <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
              <FiAlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}
          <div className="text-center py-16">
            <FiShoppingBag className="w-24 h-24 text-gray-300 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{branding.content.cartEmptyTitle}</h1>
            <p className="text-gray-600 mb-8">
              {branding.content.cartEmptyMessage}
            </p>
            <button
              onClick={() => onNavigate('shop')}
              className="inline-flex items-center px-6 py-3 text-white font-medium rounded-lg transition-colors duration-200"
              style={{ backgroundColor: branding.colors.secondary }}
              onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
              onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
            >
              <FiArrowLeft className="w-4 h-4 mr-2" />
              {branding.content.continueShoppingButton}
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{branding.content.cartTitle}</h1>
          <button
            onClick={() => onNavigate('shop')}
            className="inline-flex items-center transition-colors duration-200"
            style={{ color: branding.colors.secondary }}
            onMouseEnter={(e) => e.target.style.color = branding.colors.accent}
            onMouseLeave={(e) => e.target.style.color = branding.colors.secondary}
          >
            <FiArrowLeft className="w-4 h-4 mr-2" />
            {branding.content.continueShoppingButton}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">
                  Cart Items ({cartItems.length})
                </h2>
              </div>
              <div className="divide-y divide-gray-200">
                {cartItems.map((item) => {
                  const product = item.product || item
                  const price = product.price || 0
                  const isUpdating = updatingItems.has(item.id)

                  return (
                    <div key={item.id} className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          {(product.image || product.images?.[0]) ? (
                            <img
                              src={product.image || product.images?.[0]}
                              alt={product.name}
                              className="w-20 h-20 object-cover rounded-lg"
                              onError={(e) => {
                                e.target.style.display = 'none'
                              }}
                            />
                          ) : (
                            <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                              <span className="text-gray-400 text-xs">No image</span>
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <button
                            onClick={() => onNavigate('product')}
                            className="text-lg font-medium text-gray-900 hover:text-amber-600 transition-colors duration-200 text-left"
                          >
                            {product.name}
                          </button>
                          <p className="text-gray-600 mt-1">${price.toFixed(2)}</p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center border border-gray-300 rounded-lg">
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              disabled={isUpdating}
                              className="p-2 text-gray-600 hover:text-gray-900 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <FiMinus className="w-4 h-4" />
                            </button>
                            <span className="px-3 py-2 border-x border-gray-300 min-w-[3rem] text-center">
                              {isUpdating ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mx-auto"></div>
                              ) : (
                                item.quantity
                              )}
                            </span>
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              disabled={isUpdating}
                              className="p-2 text-gray-600 hover:text-gray-900 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <FiPlus className="w-4 h-4" />
                            </button>
                          </div>
                          <div className="text-lg font-semibold text-gray-900 min-w-[4rem] text-right">
                            ${(price * item.quantity).toFixed(2)}
                          </div>
                          <button
                            onClick={() => removeItem(item.id)}
                            disabled={isUpdating}
                            className="p-2 text-red-600 hover:text-red-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Order Summary</h2>

              <div className="space-y-4 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">${subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium">
                    {shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax</span>
                  <span className="font-medium">${tax.toFixed(2)}</span>
                </div>
                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between">
                    <span className="text-lg font-semibold text-gray-900">Total</span>
                    <span className="text-lg font-semibold text-gray-900">${total.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {shipping > 0 && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
                  <p className="text-sm text-amber-700">
                    Add ${(50 - subtotal).toFixed(2)} more to get free shipping!
                  </p>
                </div>
              )}

              <button
                onClick={() => onNavigate('checkout')}
                className="w-full bg-amber-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors duration-200 mb-4"
              >
                Proceed to Checkout
              </button>

              <div className="text-center">
                <button
                  onClick={() => onNavigate('shop')}
                  className="text-amber-600 hover:text-amber-700 text-sm transition-colors duration-200"
                >
                  Continue Shopping
                </button>
              </div>

              {/* Security Info */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="text-center">
                  <p className="text-xs text-gray-500 mb-2">Secure Checkout</p>
                  <div className="flex justify-center space-x-2">
                    <div className="w-8 h-5 bg-gray-200 rounded"></div>
                    <div className="w-8 h-5 bg-gray-200 rounded"></div>
                    <div className="w-8 h-5 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recommended Products */}
        {recommendedProducts.length > 0 && (
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">You Might Also Like</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {recommendedProducts.map((product) => (
                <button
                  key={product.id}
                  onClick={() => onNavigate('product')}
                  className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300 text-left"
                >
                  <div className="aspect-square bg-gray-200">
                    {(product.image || product.images?.[0]) ? (
                      <img
                        src={product.image || product.images?.[0]}
                        alt={product.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.style.display = 'none'
                        }}
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-400 text-xs">No image</span>
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium text-gray-900 mb-2 text-sm">{product.name}</h3>
                    <span className="text-lg font-bold text-gray-900">${product.price?.toFixed(2) || '0.00'}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Cart
