import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import { serviceAPI, type Service } from '../utils/serviceAPI'
import { type User } from '../utils/api'

interface ServicesPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function ServicesPage({ currentUser, onLogout }: ServicesPageProps) {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'main' | 'addons'>('main');
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    if (!currentUser || currentUser.role !== 'admin') {
      navigate('/');
      return;
    }

    fetchServices();
    fetchCategories();
  }, [currentUser, navigate, categoryFilter]);

  const fetchServices = async () => {
    try {
      setLoading(true);
      const response = await serviceAPI.getServices({
        page: 1,
        limit: 100, // Get all services for now
        category: categoryFilter !== 'all' ? categoryFilter : undefined,
        sortBy: 'category',
        sortOrder: 'asc'
      });

      if (response.success) {
        setServices(response.data.services);
      } else {
        console.error('Failed to fetch services');
        setServices([]);
      }
    } catch (error) {
      console.error('Error fetching services:', error);
      setServices([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await serviceAPI.getCategories();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const handleServiceEdit = async (serviceId: string) => {
    try {
      const response = await serviceAPI.getService(serviceId);
      if (response.success) {
        const service = response.data;
        const newPrice = prompt('Enter new price:', service.price.toString());
        if (newPrice !== null && !isNaN(parseFloat(newPrice))) {
          const updateResponse = await serviceAPI.updateService(serviceId, {
            price: parseFloat(newPrice)
          });
          if (updateResponse.success) {
            alert('Service updated successfully!');
            fetchServices(); // Refresh the list
          } else {
            alert('Failed to update service');
          }
        }
      }
    } catch (error) {
      console.error('Error updating service:', error);
      alert('Failed to update service');
    }
  };

  const handleServiceDelete = async (serviceId: string) => {
    if (confirm('Are you sure you want to delete this service?')) {
      try {
        const response = await serviceAPI.deleteService(serviceId);
        if (response.success) {
          alert('Service deleted successfully!');
          fetchServices(); // Refresh the list
        } else {
          alert('Failed to delete service');
        }
      } catch (error) {
        console.error('Error deleting service:', error);
        alert('Failed to delete service');
      }
    }
  };

  const handleAddService = () => {
    const name = prompt('Enter service name:');
    if (!name) return;

    const description = prompt('Enter service description:');
    if (!description) return;

    const category = prompt('Enter service category:');
    if (!category) return;

    const duration = prompt('Enter duration (in minutes):');
    if (!duration || isNaN(parseInt(duration))) return;

    const price = prompt('Enter price:');
    if (!price || isNaN(parseFloat(price))) return;

    createService({
      name,
      description,
      category,
      duration: parseInt(duration),
      price: parseFloat(price),
      isActive: true
    });
  };

  const createService = async (serviceData: any) => {
    try {
      const response = await serviceAPI.createService(serviceData);
      if (response.success) {
        alert('Service created successfully!');
        fetchServices(); // Refresh the list
        fetchCategories(); // Refresh categories
      } else {
        alert('Failed to create service');
      }
    } catch (error) {
      console.error('Error creating service:', error);
      alert('Failed to create service');
    }
  };

  // Filter services based on search and category
  const filteredServices = services.filter(service => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || service.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  // Group services by category
  const mainServices = filteredServices.filter(service => service.category !== 'Add-ons');
  const addOnServices = filteredServices.filter(service => service.category === 'Add-ons');

  if (loading) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="loading-container">
            <p>Loading services...</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <Header currentUser={currentUser} onLogout={onLogout} />

      <main className="main-content">
        <div className="page-header">
          <h1>Services Management</h1>
          <div className="header-actions">
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            <button
              className="btn-primary"
              onClick={handleAddService}
            >
              Add New Service
            </button>
          </div>
        </div>

        <div className="filter-tabs">
          <button
            className={`filter-tab ${activeTab === 'main' ? 'active' : ''}`}
            onClick={() => setActiveTab('main')}
          >
            Main Services
          </button>
          <button
            className={`filter-tab ${activeTab === 'addons' ? 'active' : ''}`}
            onClick={() => setActiveTab('addons')}
          >
            Add-on Services
          </button>
        </div>

        {activeTab === 'main' && (
          <div className="services-section">
            <h2>Main Services</h2>
            {mainServices.length === 0 ? (
              <div className="empty-state">
                <p>No main services found.</p>
              </div>
            ) : (
              <div className="appointments-list">
                {mainServices.map(service => (
                  <div key={service.id} className="appointment-card admin">
                    <div className="appointment-header">
                      <h4>{service.name}</h4>
                      <span className="service-price">${service.price}</span>
                    </div>

                    <div className="appointment-details">
                      <p><strong>Description:</strong> {service.description}</p>
                      <p><strong>Category:</strong> {service.category}</p>
                      <p><strong>Duration:</strong> {service.duration} minutes</p>
                      <p><strong>Status:</strong>
                        <span className={`status-badge ${service.isActive ? 'confirmed' : 'cancelled'}`}>
                          {service.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </p>
                    </div>

                    <div className="appointment-actions">
                      <button
                        className="btn-secondary"
                        onClick={() => handleServiceEdit(service.id)}
                      >
                        Edit
                      </button>
                      <button
                        className="btn-danger"
                        onClick={() => handleServiceDelete(service.id)}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'addons' && (
          <div className="services-section">
            <h2>Add-on Services</h2>
            {addOnServices.length === 0 ? (
              <div className="empty-state">
                <p>No add-on services found.</p>
              </div>
            ) : (
              <div className="appointments-list">
                {addOnServices.map(addon => (
                  <div key={addon.id} className="appointment-card admin">
                    <div className="appointment-header">
                      <h4>{addon.name}</h4>
                      <span className="service-price">${addon.price}</span>
                    </div>

                    <div className="appointment-details">
                      <p><strong>Duration:</strong> {addon.duration} minutes</p>
                      <p><strong>Description:</strong> {addon.description}</p>
                      <p><strong>Status:</strong>
                        <span className={`status-badge ${addon.isActive ? 'confirmed' : 'cancelled'}`}>
                          {addon.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </p>
                    </div>

                    <div className="appointment-actions">
                      <button
                        className="btn-secondary"
                        onClick={() => handleServiceEdit(addon.id)}
                      >
                        Edit
                      </button>
                      <button
                        className="btn-danger"
                        onClick={() => handleServiceDelete(addon.id)}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        <div className="dashboard-stats">
          <div className="stat-card">
            <h3>Total Main Services</h3>
            <p className="stat-number">{mainServices.length}</p>
          </div>
          <div className="stat-card">
            <h3>Total Add-ons</h3>
            <p className="stat-number">{addOnServices.length}</p>
          </div>
          <div className="stat-card">
            <h3>Active Services</h3>
            <p className="stat-number">{services.filter(s => s.isActive).length}</p>
          </div>
          <div className="stat-card">
            <h3>Categories</h3>
            <p className="stat-number">{categories.length}</p>
          </div>
        </div>
      </main>
    </div>
  )
}
