import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import { serviceAPI, type Service } from '../utils/serviceAPI'
import { type User } from '../utils/api'

interface ServicesPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function ServicesPage({ currentUser, onLogout }: ServicesPageProps) {
  const navigate = useNavigate();
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'main' | 'addons' | 'active' | 'inactive'>('all');

  useEffect(() => {
    if (!currentUser || currentUser.role !== 'admin') {
      navigate('/');
      return;
    }

    fetchServices();
  }, [currentUser, navigate]);

  const fetchServices = async () => {
    try {
      setLoading(true);
      const response = await serviceAPI.getServices({
        page: 1,
        limit: 100,
        sortBy: 'category',
        sortOrder: 'asc'
      });

      if (response.success) {
        setServices(response.data.services);
      } else {
        console.error('Failed to fetch services');
        setError('Failed to load services. Please try again.');
        setServices([]);
      }
    } catch (error) {
      console.error('Error fetching services:', error);
      setError('Failed to load services. Please try again.');
      setServices([]);
    } finally {
      setLoading(false);
    }
  };

  const handleServiceEdit = async (serviceId: string) => {
    try {
      const response = await serviceAPI.getService(serviceId);
      if (response.success) {
        const service = response.data;
        const newPrice = prompt('Enter new price:', service.price.toString());
        if (newPrice !== null && !isNaN(parseFloat(newPrice))) {
          const updateResponse = await serviceAPI.updateService(serviceId, {
            price: parseFloat(newPrice)
          });
          if (updateResponse.success) {
            alert('Service updated successfully!');
            fetchServices();
          } else {
            alert('Failed to update service');
          }
        }
      }
    } catch (error) {
      console.error('Error updating service:', error);
      alert('Failed to update service');
    }
  };

  const handleServiceDelete = async (serviceId: string) => {
    if (confirm('Are you sure you want to delete this service?')) {
      try {
        const response = await serviceAPI.deleteService(serviceId);
        if (response.success) {
          alert('Service deleted successfully!');
          fetchServices();
        } else {
          alert('Failed to delete service');
        }
      } catch (error) {
        console.error('Error deleting service:', error);
        alert('Failed to delete service');
      }
    }
  };

  const handleAddService = () => {
    const name = prompt('Enter service name:');
    if (!name) return;

    const description = prompt('Enter service description:');
    if (!description) return;

    const category = prompt('Enter service category:');
    if (!category) return;

    const duration = prompt('Enter duration (in minutes):');
    if (!duration || isNaN(parseInt(duration))) return;

    const price = prompt('Enter price:');
    if (!price || isNaN(parseFloat(price))) return;

    createService({
      name,
      description,
      category,
      duration: parseInt(duration),
      price: parseFloat(price),
      isActive: true
    });
  };

  const createService = async (serviceData: any) => {
    try {
      const response = await serviceAPI.createService(serviceData);
      if (response.success) {
        alert('Service created successfully!');
        fetchServices();
      } else {
        alert('Failed to create service');
      }
    } catch (error) {
      console.error('Error creating service:', error);
      alert('Failed to create service');
    }
  };

  const filteredServices = services.filter(service => {
    switch (filter) {
      case 'main':
        return service.category !== 'Add-ons';
      case 'addons':
        return service.category === 'Add-ons';
      case 'active':
        return service.isActive;
      case 'inactive':
        return !service.isActive;
      default:
        return true;
    }
  });

  const getStatusColor = (isActive: boolean) => {
    return isActive ? '#10b981' : '#ef4444';
  };

  if (loading) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="loading-container">
            <p>Loading services...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="error-container">
            <p className="error-message">{error}</p>
            <button
              className="btn-primary"
              onClick={() => {
                setError(null);
                fetchServices();
              }}
            >
              Try Again
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <Header currentUser={currentUser} onLogout={onLogout} />

      <main className="main-content">
        <div className="page-header">
          <h1>Services Management</h1>
          <button
            className="btn-primary"
            onClick={handleAddService}
          >
            Add New Service
          </button>
        </div>

        <div className="filter-tabs">
          {(['all', 'main', 'addons', 'active', 'inactive'] as const).map(filterType => (
            <button
              key={filterType}
              className={`filter-tab ${filter === filterType ? 'active' : ''}`}
              onClick={() => setFilter(filterType)}
            >
              {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
            </button>
          ))}
        </div>

        <div className="appointments-list">
          {filteredServices.length === 0 ? (
            <div className="empty-state">
              <p>No services found for the selected filter.</p>
            </div>
          ) : (
            filteredServices.map(service => (
              <div key={service.id} className="appointment-card">
                <div className="appointment-header">
                  <h3>{service.name}</h3>
                  <span
                    className="status-badge"
                    style={{ backgroundColor: getStatusColor(service.isActive) }}
                  >
                    {service.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>

                <div className="appointment-details">
                  <p><strong>Description:</strong> {service.description}</p>
                  <p><strong>Category:</strong> {service.category}</p>
                  <p><strong>Duration:</strong> {service.duration} minutes</p>
                  <p><strong>Price:</strong> ${service.price}</p>
                </div>

                <div className="appointment-actions">
                  <button
                    className="btn-secondary"
                    onClick={() => handleServiceEdit(service.id)}
                  >
                    Edit
                  </button>
                  <button
                    className="btn-danger"
                    onClick={() => handleServiceDelete(service.id)}
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </main>
    </div>
  )
}
