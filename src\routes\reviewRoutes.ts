import { Router } from 'express';
import { ReviewController } from '../controllers';
import { authenticate } from '../middleware/auth';
import { validate } from '../middleware/validation';
import {
  createReviewValidation,
  updateReviewValidation,
  reviewStatusValidation,
  mongoIdValidation,
  paginationValidation
} from '../utils/validation';

const router = Router();

// Public routes
// GET /api/reviews - Get reviews for product or service
router.get(
  '/',
  validate(paginationValidation),
  ReviewController.getReviews
);

// GET /api/reviews/stats - Get review statistics
router.get(
  '/stats',
  ReviewController.getReviewStats
);

// GET /api/products/:id/reviews (legacy support)
router.get(
  '/:id/reviews',
  validate([...mongoIdValidation(), ...paginationValidation]),
  ReviewController.getProductReviews
);

// User routes (authenticated)
// POST /api/reviews - Create new review
router.post(
  '/',
  authenticate,
  validate(createReviewValidation),
  ReviewController.createReview
);

// GET /api/reviews/my - Get user's own reviews
router.get(
  '/my',
  authenticate,
  validate(paginationValidation),
  ReviewController.getUserReviews
);

// PUT /api/reviews/:id - Update user's own review
router.put(
  '/:id',
  authenticate,
  validate([...mongoIdValidation(), ...updateReviewValidation]),
  ReviewController.updateReview
);

// DELETE /api/reviews/:id - Delete user's own review
router.delete(
  '/:id',
  authenticate,
  validate(mongoIdValidation()),
  ReviewController.deleteReview
);

// Admin routes
// GET /api/reviews/admin/all - Get all reviews for admin
router.get(
  '/admin/all',
  authenticate,
  validate(paginationValidation),
  ReviewController.getAllReviews
);

// PUT /api/reviews/admin/:id/status - Update review status
router.put(
  '/admin/:id/status',
  authenticate,
  validate([...mongoIdValidation(), ...reviewStatusValidation]),
  ReviewController.updateReviewStatus
);

// DELETE /api/reviews/admin/:id - Admin delete any review
router.delete(
  '/admin/:id',
  authenticate,
  validate(mongoIdValidation()),
  ReviewController.adminDeleteReview
);

export default router;
