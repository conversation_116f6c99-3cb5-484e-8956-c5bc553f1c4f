import crypto from 'crypto';
import { User, TokenBlacklist } from '../models';
import { generateToken, generateRefreshToken, decodeToken } from '../utils/jwt';
import { emailService } from './emailService';
import { IUser, JWTPayload } from '../types';

export class AuthService {
  static async register(userData: {
    firstName: string;
    lastName: string;
    name: string;
    email: string;
    phone?: string;
    password: string;
  }): Promise<{ user: IUser; token: string; refreshToken: string }> {
    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { email: userData.email },
        ...(userData.phone ? [{ phone: userData.phone }] : [])
      ]
    });

    if (existingUser) {
      if (existingUser.email === userData.email) {
        throw new Error('User with this email already exists');
      }
      if (userData.phone && existingUser.phone === userData.phone) {
        throw new Error('User with this phone number already exists');
      }
    }

    // Create new user
    const user = await User.create(userData);

    // Generate tokens
    const payload: JWTPayload = {
      userId: user._id,
      email: user.email,
      role: user.role
    };

    const token = generateToken(payload);
    const refreshToken = generateRefreshToken(payload);

    // Send welcome email asynchronously
    try {
      emailService.sendWelcomeEmail(user).catch(err =>
        console.error('Failed to send welcome email:', err)
      );
    } catch (error) {
      console.error('Email service error:', error);
      // Don't fail registration if email fails
    }

    return { user, token, refreshToken };
  }

  static async login(email: string, password: string): Promise<{
    user: IUser;
    token: string;
    refreshToken: string;
  }> {
    // Find user with password field
    const user = await User.findOne({ email }).select('+password');

    if (!user || !(await user.comparePassword(password))) {
      throw new Error('Invalid email or password');
    }

    // Generate tokens
    const payload: JWTPayload = {
      userId: user._id,
      email: user.email,
      role: user.role
    };

    const token = generateToken(payload);
    const refreshToken = generateRefreshToken(payload);

    // Remove password from user object
    user.password = undefined as any;

    return { user, token, refreshToken };
  }

  static async forgotPassword(email: string): Promise<string> {
    const user = await User.findOne({ email });

    if (!user) {
      throw new Error('No user found with this email address');
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');

    // Save hashed token and expiry to user
    user.resetPasswordToken = hashedToken;
    user.resetPasswordExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
    await user.save();

    // TODO: Implement password reset email with new email service
    console.log(`Password reset token generated for ${user.email}: ${resetToken}`);

    return resetToken;
  }

  static async resetPassword(token: string, newPassword: string): Promise<void> {
    // Hash the token to compare with stored hash
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

    const user = await User.findOne({
      resetPasswordToken: hashedToken,
      resetPasswordExpires: { $gt: new Date() }
    });

    if (!user) {
      throw new Error('Invalid or expired reset token');
    }

    // Update password and clear reset fields
    user.password = newPassword;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();
  }

  static async verifyToken(userId: string): Promise<IUser> {
    const user = await User.findById(userId);

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  }

  static async blacklistToken(token: string): Promise<void> {
    try {
      // Decode token to get expiration time
      const decoded = decodeToken(token);
      if (!decoded || !decoded.exp) {
        throw new Error('Invalid token');
      }

      // Convert exp (seconds) to Date
      const expiresAt = new Date(decoded.exp * 1000);

      // Add token to blacklist
      await TokenBlacklist.create({
        token,
        expiresAt
      });
    } catch (error) {
      console.error('Error blacklisting token:', error);
      throw new Error('Failed to blacklist token');
    }
  }

  static async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const blacklistedToken = await TokenBlacklist.findOne({ token });
      return !!blacklistedToken;
    } catch (error) {
      console.error('Error checking token blacklist:', error);
      return false;
    }
  }
}
