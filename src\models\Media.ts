import mongoose, { Document, Schema } from 'mongoose';

export interface IMedia extends Document {
  _id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  cloudinaryId?: string;
  alt?: string;
  caption?: string;
  description?: string;
  uploadedBy: mongoose.Types.ObjectId;
  uploadedAt: Date;
  usedIn: Array<{
    type: 'product' | 'service' | 'branding' | 'other';
    id: string;
    field: string;
  }>;
  tags: string[];
  isActive: boolean;
}

const MediaSchema = new Schema<IMedia>({
  filename: {
    type: String,
    required: true
  },
  originalName: {
    type: String,
    required: true
  },
  mimeType: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true
  },
  url: {
    type: String,
    required: true
  },
  cloudinaryId: {
    type: String
  },
  alt: {
    type: String,
    default: ''
  },
  caption: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  uploadedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  usedIn: [{
    type: {
      type: String,
      enum: ['product', 'service', 'branding', 'other'],
      required: true
    },
    id: {
      type: String,
      required: true
    },
    field: {
      type: String,
      required: true
    }
  }],
  tags: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for better performance
MediaSchema.index({ uploadedBy: 1, uploadedAt: -1 });
MediaSchema.index({ mimeType: 1 });
MediaSchema.index({ tags: 1 });
MediaSchema.index({ 'usedIn.type': 1, 'usedIn.id': 1 });

export const Media = mongoose.model<IMedia>('Media', MediaSchema);
