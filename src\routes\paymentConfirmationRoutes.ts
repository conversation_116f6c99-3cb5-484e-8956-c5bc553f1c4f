import { Router } from 'express';
import { PaymentConfirmationController } from '../controllers/paymentConfirmationController';
import { authenticate } from '../middleware/auth';
import { validate } from '../middleware/validation';
import {
  createPaymentConfirmationValidation,
  updatePaymentConfirmationValidation,
  paymentConfirmationStatusValidation,
  mongoIdValidation,
  paginationValidation
} from '../utils/validation';

const router = Router();

// User routes (authenticated)
// POST /api/payment-confirmations - Create new payment confirmation
router.post(
  '/',
  authenticate,
  validate(createPaymentConfirmationValidation),
  PaymentConfirmationController.createPaymentConfirmation
);

// GET /api/payment-confirmations/my - Get user's own payment confirmations
router.get(
  '/my',
  authenticate,
  validate(paginationValidation),
  PaymentConfirmationController.getUserPaymentConfirmations
);

// GET /api/payment-confirmations/:id - Get payment confirmation by ID
router.get(
  '/:id',
  authenticate,
  validate(mongoIdValidation()),
  PaymentConfirmationController.getPaymentConfirmationById
);

// PUT /api/payment-confirmations/:id - Update user's own payment confirmation
router.put(
  '/:id',
  authenticate,
  validate([...mongoIdValidation(), ...updatePaymentConfirmationValidation]),
  PaymentConfirmationController.updatePaymentConfirmation
);

// DELETE /api/payment-confirmations/:id - Delete user's own payment confirmation
router.delete(
  '/:id',
  authenticate,
  validate(mongoIdValidation()),
  PaymentConfirmationController.deletePaymentConfirmation
);

// Admin routes
// GET /api/payment-confirmations/admin/all - Get all payment confirmations for admin
router.get(
  '/admin/all',
  authenticate,
  validate(paginationValidation),
  PaymentConfirmationController.getAllPaymentConfirmations
);

// GET /api/payment-confirmations/admin/stats - Get payment confirmation statistics
router.get(
  '/admin/stats',
  authenticate,
  PaymentConfirmationController.getPaymentConfirmationStats
);

// PUT /api/payment-confirmations/admin/:id/status - Update payment confirmation status
router.put(
  '/admin/:id/status',
  authenticate,
  validate([...mongoIdValidation(), ...paymentConfirmationStatusValidation]),
  PaymentConfirmationController.updatePaymentConfirmationStatus
);

// DELETE /api/payment-confirmations/admin/:id - Admin delete any payment confirmation
router.delete(
  '/admin/:id',
  authenticate,
  validate(mongoIdValidation()),
  PaymentConfirmationController.adminDeletePaymentConfirmation
);

export default router;
