import { Request, Response } from 'express';
import { Branding } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';

export class BrandingController {
  /**
   * Get complete branding configuration in a single request
   * This returns ONLY database data - no hardcoded values
   */
  static async getCompleteBranding(req: Request, res: Response): Promise<void> {
    try {
      // Get branding content from database
      let branding = await Branding.findOne();

      if (!branding) {
        // Create default branding if none exists
        branding = await Branding.create({});
      }

      // Return ONLY database data - NO hardcoded values anywhere!
      const completeBranding = {
        // Flatten the branding data with all the fields from database ONLY
        branding: {
          // Global/Site information - PURE DATABASE VALUES
          siteName: branding.global?.siteName,
          tagline: branding.global?.tagline,
          phone: branding.global?.phone,
          email: branding.global?.email,
          address: branding.global?.address,

          // Social media - PURE DATABASE VALUES
          instagram: branding.global?.instagram,
          facebook: branding.global?.facebook,
          twitter: branding.global?.twitter,
          youtube: branding.global?.youtube,

          // Core images and branding - PURE DATABASE VALUES
          logo: branding.global?.logo,
          heroImage: branding.home?.heroImage,
          favicon: branding.global?.favicon,

          // Home page content - PURE DATABASE VALUES
          heroTitle: branding.home?.heroTitle,
          heroSubtitle: branding.home?.heroSubtitle,
          aboutTitle: branding.home?.aboutTitle,
          aboutText: branding.home?.aboutText,
          testimonialHeading: branding.home?.testimonialHeading,

          // Services content - PURE DATABASE VALUES
          servicesTitle: branding.services?.pageTitle,
          servicesSubtitle: branding.services?.pageSubtitle,
          servicesDescription: branding.services?.pageDescription,
          serviceLocMaintenance: branding.services?.serviceLocMaintenance,
          serviceLocMaintenanceDesc: branding.services?.serviceLocMaintenanceDesc,
          serviceStarterLocs: branding.services?.serviceStarterLocs,
          serviceStarterLocsDesc: branding.services?.serviceStarterLocsDesc,
          serviceLocStyling: branding.services?.serviceLocStyling,
          serviceLocStylingDesc: branding.services?.serviceLocStylingDesc,
          serviceNaturalHairCare: branding.services?.serviceNaturalHairCare,
          serviceNaturalHairCareDesc: branding.services?.serviceNaturalHairCareDesc,

          // Consultation content - PURE DATABASE VALUES
          consultationTitle: branding.consultation?.pageTitle,
          consultationSubtitle: branding.consultation?.pageSubtitle,
          consultationDescription: branding.consultation?.pageDescription,
          consultationFormTitle: branding.consultation?.formTitle,
          consultationFormSubtitle: branding.consultation?.formSubtitle,

          // Shop content - PURE DATABASE VALUES
          shopTitle: branding.shop?.pageTitle,
          shopSubtitle: branding.shop?.pageSubtitle,
          shopDescription: branding.shop?.pageDescription,
          shopFeaturedTitle: branding.shop?.featuredCollectionTitle,

          // Dashboard content - PURE DATABASE VALUES
          dashboardWelcome: branding.dashboard?.welcomeMessage,
          dashboardOverviewTitle: branding.dashboard?.overviewTitle,
          dashboardAppointmentsTitle: branding.dashboard?.appointmentsTitle,
          dashboardOrdersTitle: branding.dashboard?.ordersTitle,
          dashboardFavoritesTitle: branding.dashboard?.favoritesTitle,
          dashboardProfileTitle: branding.dashboard?.profileTitle,
          dashboardNextAppointment: branding.dashboard?.nextAppointment,
          dashboardRecentOrders: branding.dashboard?.recentOrders,
          dashboardLoyaltyTitle: branding.dashboard?.loyaltyTitle,

          // Authentication content - PURE DATABASE VALUES
          loginTitle: branding.login?.pageTitle,
          loginSubtitle: branding.login?.pageSubtitle,
          signInButton: branding.login?.signInButton,
          signingInText: branding.login?.signingInText,
          noAccountText: branding.login?.noAccountText,
          signUpLink: branding.login?.signUpLink,
          forgotPasswordLink: branding.login?.forgotPasswordLink,
          signupTitle: branding.signup?.pageTitle,
          signupSubtitle: branding.signup?.pageSubtitle,
          createAccountButton: branding.signup?.createAccountButton,
          creatingAccountText: branding.signup?.creatingAccountText,
          haveAccountText: branding.signup?.haveAccountText,
          signInLink: branding.signup?.signInLink,
          agreeTermsText: branding.signup?.agreeTermsText,
          termsLinkText: branding.signup?.termsLinkText,
          andText: branding.signup?.andText,
          privacyLinkText: branding.signup?.privacyLinkText,

          // Cart content - PURE DATABASE VALUES
          cartTitle: branding.cart?.pageTitle,
          cartEmptyMessage: branding.cart?.emptyCartMessage,
          cartShippingMessage: branding.messages?.cartShipping,
          freeShippingThreshold: branding.cart?.freeShippingThreshold,
          shippingCalculated: branding.cart?.shippingCalculated,

          // Product Detail content - PURE DATABASE VALUES
          quantityLabel: branding.productDetail?.quantityLabel,
          overviewTab: branding.productDetail?.overviewTab,
          ingredientsTab: branding.productDetail?.ingredientsTab,
          reviewsTab: branding.productDetail?.reviewsTab,

          // Buttons - PURE DATABASE VALUES
          bookNowButton: branding.buttons?.bookNow,
          shopNowButton: branding.buttons?.shopNow,
          learnMoreButton: branding.buttons?.learnMore,
          viewAllButton: branding.buttons?.viewAll,
          continueShoppingButton: branding.buttons?.continueShopping,
          proceedToCheckoutButton: branding.buttons?.proceedToCheckout,
          addToCartButton: branding.buttons?.addToCart,
          scheduleConsultationButton: branding.buttons?.scheduleConsultation,
          writeReviewButton: branding.buttons?.writeReview,

          // Navigation - PURE DATABASE VALUES
          navHome: branding.navigation?.home,
          navServices: branding.navigation?.services,
          navShop: branding.navigation?.shop,
          navConsultation: branding.navigation?.consultation,
          navLogin: branding.navigation?.login,
          navSignup: branding.navigation?.signup,
          navDashboard: branding.navigation?.dashboard,

          // Footer - PURE DATABASE VALUES
          footerDescription: branding.footer?.description,
          footerQuickLinks: branding.footer?.quickLinks,
          footerContact: branding.footer?.contact,
          footerFollowUs: branding.footer?.followUs,
          footerCopyright: branding.footer?.copyrightText,

          // Testimonials - PURE DATABASE VALUES
          testimonialsTitle: branding.testimonials?.title,
          testimonialsSubtitle: branding.testimonials?.subtitle,
          reviewsTitle: branding.reviews?.sectionTitle,

          // Messages - PURE DATABASE VALUES
          loadingMessage: branding.messages?.loading,
          errorMessage: branding.messages?.error,
          notFoundMessage: branding.messages?.notFound,
          comingSoonMessage: branding.messages?.comingSoon
        },

        // Business profile from database - PURE DATABASE VALUES
        business: branding.business,

        // Theme settings from database - PURE DATABASE VALUES
        theme: branding.theme,

        // Site settings from database - PURE DATABASE VALUES
        site: branding.site
      }

      sendSuccess(res, 'Complete branding configuration retrieved successfully', completeBranding);
    } catch (error) {
      console.error('Get complete branding error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getBrandingContent(req: Request, res: Response): Promise<void> {
    try {
      let branding = await Branding.findOne();

      if (!branding) {
        // Create default branding if none exists
        branding = await Branding.create({});
      }

      sendSuccess(res, 'Branding content retrieved successfully', branding);
    } catch (error) {
      console.error('Get branding content error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getBusinessProfile(req: Request, res: Response): Promise<void> {
    try {
      // Get business profile from database ONLY - NO hardcoded values!
      const branding = await Branding.findOne();

      if (!branding) {
        sendError(res, 'No branding configuration found');
        return;
      }

      // Return ONLY database business data
      const businessProfile = branding.business;

      sendSuccess(res, 'Business profile retrieved successfully', businessProfile);
    } catch (error) {
      console.error('Error getting business profile:', error);
      sendError(res, 'Failed to get business profile');
    }
  }

  static async getThemeSettings(req: Request, res: Response): Promise<void> {
    try {
      // Get theme settings from database ONLY - NO hardcoded values!
      const branding = await Branding.findOne();

      if (!branding) {
        sendError(res, 'No branding configuration found');
        return;
      }

      // Return ONLY database theme data
      const themeSettings = branding.theme;

      sendSuccess(res, 'Theme settings retrieved successfully', themeSettings);
    } catch (error) {
      console.error('Error getting theme settings:', error);
      sendError(res, 'Failed to get theme settings');
    }
  }

  static async getSiteSettings(req: Request, res: Response): Promise<void> {
    try {
      // Get site settings from database ONLY - NO hardcoded values!
      const branding = await Branding.findOne();

      if (!branding) {
        sendError(res, 'No branding configuration found');
        return;
      }

      // Return ONLY database site data
      const siteSettings = branding.site;

      sendSuccess(res, 'Site settings retrieved successfully', siteSettings);
    } catch (error) {
      console.error('Error getting site settings:', error);
      sendError(res, 'Failed to get site settings');
    }
  }

  static async updateBrandingContent(req: Request, res: Response): Promise<void> {
    try {
      const updateData = req.body;

      let branding = await Branding.findOne();

      if (!branding) {
        branding = await Branding.create(updateData);
      } else {
        Object.assign(branding, updateData);

        // Synchronize global fields with business fields to avoid duplicates
        if (updateData.global) {
          if (!branding.business) {
            branding.business = {
              name: '',
              tagline: '',
              description: '',
              phone: '',
              email: '',
              address: {
                street: '',
                city: '',
                state: '',
                zip: '',
                full: ''
              },
              social: {
                instagram: '',
                facebook: '',
                twitter: ''
              },
              hours: {
                monday: '',
                tuesday: '',
                wednesday: '',
                thursday: '',
                friday: '',
                saturday: '',
                sunday: ''
              }
            };
          }

          // Sync key fields between global and business sections
          if (updateData.global.phone) {
            branding.business.phone = updateData.global.phone;
          }
          if (updateData.global.email) {
            branding.business.email = updateData.global.email;
          }
          if (updateData.global.siteName) {
            branding.business.name = updateData.global.siteName;
          }
          if (updateData.global.tagline) {
            branding.business.tagline = updateData.global.tagline;
          }
          if (updateData.global.address) {
            if (!branding.business.address) {
              branding.business.address = {
                street: '',
                city: '',
                state: '',
                zip: '',
                full: ''
              };
            }
            branding.business.address.full = updateData.global.address;
          }

          // Sync social media from global to business
          if (updateData.global.instagram || updateData.global.facebook || updateData.global.twitter || updateData.global.youtube) {
            if (!branding.business.social) {
              branding.business.social = {
                instagram: '',
                facebook: '',
                twitter: ''
              };
            }
            if (updateData.global.instagram) branding.business.social.instagram = updateData.global.instagram;
            if (updateData.global.facebook) branding.business.social.facebook = updateData.global.facebook;
            if (updateData.global.twitter) branding.business.social.twitter = updateData.global.twitter;
          }
        }

        await branding.save();
      }

      sendSuccess(res, 'Branding content updated successfully', branding);
    } catch (error) {
      console.error('Update branding content error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateBrandingSection(req: Request, res: Response): Promise<void> {
    try {
      const { section } = req.params;
      const updateData = req.body;

      const validSections = [
        'global', 'home', 'services', 'shop', 'consultation',
        'login', 'signup', 'cart', 'productDetail', 'footer',
        'dashboard', 'buttons', 'navigation', 'testimonials',
        'reviews', 'contact', 'paymentConfirmation', 'messages',
        'business', 'theme', 'site'
      ];

      if (!validSections.includes(section)) {
        sendError(res, 'Invalid section specified');
        return;
      }

      let branding = await Branding.findOne();
      
      if (!branding) {
        branding = await Branding.create({});
      }

      // Update specific section
      (branding as any)[section] = { ...(branding as any)[section], ...updateData };
      await branding.save();

      sendSuccess(res, `${section} section updated successfully`, branding);
    } catch (error) {
      console.error('Update branding section error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
