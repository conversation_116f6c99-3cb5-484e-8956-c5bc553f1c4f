import { Request, Response } from 'express';
import { AuthService } from '../services';
import { sendSuccess, sendError, sendCreated } from '../utils/response';
import { AuthenticatedRequest } from '../types';
import { User } from '../models';

export class AuthController {
  static async register(req: Request, res: Response): Promise<void> {
    try {
      const { firstName, lastName, email, phone, password } = req.body;

      const result = await AuthService.register({
        firstName,
        lastName,
        name: `${firstName} ${lastName}`.trim(),
        email,
        phone,
        password
      });

      sendCreated(res, 'User registered successfully', {
        user: result.user,
        token: result.token,
        refreshToken: result.refreshToken
      });
    } catch (error) {
      console.error('Registration error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async login(req: Request, res: Response): Promise<void> {
    try {
      const { email, password, rememberMe } = req.body;

      const result = await AuthService.login(email, password);

      sendSuccess(res, 'Login successful', {
        user: result.user,
        token: result.token,
        refreshToken: result.refreshToken,
        rememberMe: rememberMe || false
      });
    } catch (error) {
      console.error('Login error:', error);
      sendError(res, (error as Error).message, undefined, 401);
    }
  }

  static async forgotPassword(req: Request, res: Response): Promise<void> {
    try {
      const { email } = req.body;

      await AuthService.forgotPassword(email);

      sendSuccess(res, 'Password reset instructions sent to your email');
    } catch (error) {
      console.error('Forgot password error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async resetPassword(req: Request, res: Response): Promise<void> {
    try {
      const { token, password } = req.body;

      await AuthService.resetPassword(token, password);

      sendSuccess(res, 'Password reset successful');
    } catch (error) {
      console.error('Reset password error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async verify(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'User not found', undefined, 401);
        return;
      }

      console.log('Auth verify endpoint - User from middleware:', req.user._id);
      console.log('Auth verify endpoint - User ID type:', typeof req.user._id);

      const user = await AuthService.verifyToken(req.user._id);

      console.log('Auth verify endpoint - User returned:', user._id);
      console.log('Auth verify endpoint - Sending user to frontend');

      sendSuccess(res, 'Token verified successfully', { user });
    } catch (error) {
      console.error('Token verification error:', error);
      sendError(res, (error as Error).message, undefined, 401);
    }
  }

  static async logout(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      // Extract token from Authorization header
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);

        // Add token to blacklist (implement token blacklist service)
        await AuthService.blacklistToken(token);
      }

      sendSuccess(res, 'Logout successful');
    } catch (error) {
      console.error('Logout error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async checkEmail(req: Request, res: Response): Promise<void> {
    try {
      const { email } = req.body;

      if (!email) {
        sendError(res, 'Email is required', undefined, 400);
        return;
      }

      const user = await User.findOne({ email: email.toLowerCase() });

      if (user) {
        // Determine if user is admin
        const isAdmin = user.role === 'admin';

        sendSuccess(res, 'Email found', {
          exists: true,
          isAdmin: isAdmin,
          requiresPassword: isAdmin, // Admins require password, regular users can use email-only login
          user: {
            _id: user._id.toString(),
            id: user._id.toString(), // Include both for compatibility
            name: user.name,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            email: user.email,
            role: user.role,
            isVerified: user.isVerified,
            notificationPreferences: user.notificationPreferences || {
              email: true,
              sms: false,
              push: true
            },
            favorites: user.favorites || [],
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
          },
          loginMethod: isAdmin ? 'password' : 'email-only',
          message: isAdmin
            ? 'Admin account found. Password required for login.'
            : 'User account found. You can login with email only.',
          authFlow: {
            nextStep: isAdmin ? 'password-login' : 'email-only-login',
            endpoint: isAdmin ? '/api/v2/auth/login' : '/api/v2/auth/login-email-only',
            description: isAdmin
              ? 'Use email and password to login'
              : 'Use email-only login for quick access'
          }
        });
      } else {
        sendSuccess(res, 'Email not found', {
          exists: false,
          isAdmin: false,
          requiresPassword: false,
          user: null,
          loginMethod: 'contact-admin',
          message: 'Email not found. Please contact support to get access to your account.'
        });
      }
    } catch (error) {
      console.error('Check email error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
