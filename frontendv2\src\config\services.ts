/**
 * Service Type Definitions
 * All service data is now fetched from the API
 */

export interface AddOnService {
  id: string;
  name: string;
  duration: number;
  price: number;
  description?: string;
}

export interface Service {
  id: string;
  name: string;
  price: string;
  description: string;
  category?: string;
}

// All services are now fetched from the API
// These arrays are kept for backward compatibility but should not be used
export const addOnServices: AddOnService[] = [];
export const retieServices: Service[] = [];
export const otherServices: Service[] = [];

// Helper function to filter add-ons for services
// All data now comes from the API
export const getAddOnsForService = (_serviceId: string): AddOnService[] => {
  // This function should now use API data instead of static arrays
  return [];
};
