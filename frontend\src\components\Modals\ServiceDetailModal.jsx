import React, { useState } from 'react'
import { FiX, FiEdit3, FiTrash2, FiScissors, FiDollarSign, FiClock, FiTag, FiCalendar, FiUsers } from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'
import { useToast } from '../../contexts/ToastContext'

const ServiceDetailModal = ({
  service,
  isOpen,
  onClose,
  onEdit,
  onDelete
}) => {
  const { branding } = useBranding()
  const { showSuccess, showError } = useToast()
  const [isDeleting, setIsDeleting] = useState(false)

  if (!isOpen || !service) return null

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this service?')) return
    
    setIsDeleting(true)
    try {
      await onDelete(service._id)
      showSuccess('Service deleted successfully')
      onClose()
    } catch (error) {
      showError('Failed to delete service')
    } finally {
      setIsDeleting(false)
    }
  }

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive': return 'bg-red-100 text-red-800 border-red-200'
      case 'draft': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'archived': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0)
  }

  const formatDuration = (hours) => {
    if (!hours) return 'N/A'

    // Convert hours to a readable format
    if (hours === 1) {
      return '1 hour'
    } else if (hours < 1) {
      const minutes = Math.round(hours * 60)
      return `${minutes} minutes`
    } else if (hours % 1 === 0) {
      return `${hours} hours`
    } else {
      return `${hours} hours`
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div 
              className="w-10 h-10 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: branding?.colors?.primary + '20' || '#3B82F620' }}
            >
              <FiScissors className="w-5 h-5" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Service Details</h2>
              <p className="text-sm text-gray-500">
                ID: {service._id?.slice(-8) || 'N/A'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <FiX className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status and Actions */}
          <div className="flex justify-between items-start">
            <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full border ${getStatusColor(service.status)}`}>
              {service.status || 'Draft'}
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => onEdit(service)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
              >
                <FiEdit3 className="w-4 h-4" />
                <span>Edit</span>
              </button>
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50"
              >
                <FiTrash2 className="w-4 h-4" />
                <span>{isDeleting ? 'Deleting...' : 'Delete'}</span>
              </button>
            </div>
          </div>

          {/* Service Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiScissors className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Service Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-600 mb-1">Service Name</label>
                <div className="text-gray-900 font-medium text-lg">
                  {service.name || 'N/A'}
                </div>
              </div>
              {service.description && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-600 mb-1">Description</label>
                  <div className="text-gray-900 bg-white p-3 rounded border">
                    {service.description}
                  </div>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Category</label>
                <div className="text-gray-900">
                  {service.category || 'Uncategorized'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Service Type</label>
                <div className="text-gray-900">
                  {service.type || 'Standard'}
                </div>
              </div>
            </div>
          </div>

          {/* Pricing Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiDollarSign className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Pricing Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Base Price</label>
                <div className="text-gray-900 font-bold text-lg">
                  {formatCurrency(service.price)}
                </div>
              </div>
              {service.discountPrice && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Discount Price</label>
                  <div className="text-green-600 font-bold text-lg">
                    {formatCurrency(service.discountPrice)}
                  </div>
                </div>
              )}
              {service.memberPrice && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Member Price</label>
                  <div className="text-blue-600 font-bold">
                    {formatCurrency(service.memberPrice)}
                  </div>
                </div>
              )}
              {service.minPrice && service.maxPrice && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Price Range</label>
                  <div className="text-gray-900">
                    {formatCurrency(service.minPrice)} - {formatCurrency(service.maxPrice)}
                  </div>
                </div>
              )}
            </div>
          </div>



          {/* Availability */}
          {service.availability && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
                <FiCalendar className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
                Availability
              </h3>
              <div className="text-gray-900 bg-white p-3 rounded border">
                {typeof service.availability === 'string' 
                  ? service.availability 
                  : JSON.stringify(service.availability, null, 2)
                }
              </div>
            </div>
          )}

          {/* Staff and Requirements */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiUsers className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Staff & Requirements
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {service.staffRequired && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Staff Required</label>
                  <div className="text-gray-900">
                    {service.staffRequired} person(s)
                  </div>
                </div>
              )}
              {service.skillLevel && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Skill Level Required</label>
                  <div className="text-gray-900 capitalize">
                    {service.skillLevel}
                  </div>
                </div>
              )}
              {service.equipmentRequired && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-600 mb-1">Equipment Required</label>
                  <div className="text-gray-900">
                    {Array.isArray(service.equipmentRequired) 
                      ? service.equipmentRequired.join(', ')
                      : service.equipmentRequired
                    }
                  </div>
                </div>
              )}
              {service.preparationTime && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Preparation Time</label>
                  <div className="text-gray-900">
                    {formatDuration(service.preparationTime)}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Tags and Categories */}
          {(service.tags && service.tags.length > 0) && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
                <FiTag className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {service.tags.map((tag, index) => (
                  <span 
                    key={index}
                    className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Service Timeline */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiCalendar className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Service Timeline
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Created Date</label>
                <div className="text-gray-900">
                  {service.createdAt ? new Date(service.createdAt).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Last Updated</label>
                <div className="text-gray-900">
                  {service.updatedAt ? new Date(service.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  }) : 'N/A'}
                </div>
              </div>
              {service.totalBookings !== undefined && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Total Bookings</label>
                  <div className="text-gray-900 font-medium">
                    {service.totalBookings}
                  </div>
                </div>
              )}
              {service.averageRating && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Average Rating</label>
                  <div className="text-gray-900 font-medium">
                    ⭐ {service.averageRating}/5
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Additional Notes */}
          {service.notes && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Service Notes
              </h3>
              <div className="text-gray-900 bg-white p-3 rounded border">
                {service.notes}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-4 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

export default ServiceDetailModal
