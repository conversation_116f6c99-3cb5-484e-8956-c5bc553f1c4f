import { apiService } from './index.js'

class MediaService {
  /**
   * Get media library with pagination and filters
   */
  async getMediaLibrary(params = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        search = '',
        mimeType = '',
        tags = [],
        uploadedBy = ''
      } = params

      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(mimeType && { mimeType }),
        ...(uploadedBy && { uploadedBy })
      })

      // Add tags as separate parameters
      if (tags && tags.length > 0) {
        tags.forEach(tag => queryParams.append('tags', tag))
      }

      const response = await apiService.get(`/media/library?${queryParams}`)
      return response
    } catch (error) {
      console.error('Error fetching media library:', error)
      throw error
    }
  }

  /**
   * Upload media file
   */
  async uploadMedia(file, metadata = {}) {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('alt', metadata.alt || '')
      formData.append('caption', metadata.caption || '')
      formData.append('description', metadata.description || '')
      formData.append('tags', JSON.stringify(metadata.tags || []))

      const response = await apiService.post('/media/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      return response
    } catch (error) {
      console.error('Error uploading media:', error)
      throw error
    }
  }

  /**
   * Upload multiple media files
   */
  async uploadMultipleMedia(files, metadata = {}) {
    try {
      const uploadPromises = Array.from(files).map(file => 
        this.uploadMedia(file, metadata)
      )

      const results = await Promise.all(uploadPromises)
      return results
    } catch (error) {
      console.error('Error uploading multiple media:', error)
      throw error
    }
  }

  /**
   * Get media by ID
   */
  async getMediaById(id) {
    try {
      const response = await apiService.get(`/media/${id}`)
      return response
    } catch (error) {
      console.error('Error fetching media by ID:', error)
      throw error
    }
  }

  /**
   * Update media metadata
   */
  async updateMedia(id, metadata) {
    try {
      const response = await apiService.put(`/media/${id}`, metadata)
      return response
    } catch (error) {
      console.error('Error updating media:', error)
      throw error
    }
  }

  /**
   * Delete media
   */
  async deleteMedia(id) {
    try {
      const response = await apiService.delete(`/media/${id}`)
      return response
    } catch (error) {
      console.error('Error deleting media:', error)
      throw error
    }
  }

  /**
   * Bulk delete media
   */
  async bulkDeleteMedia(ids) {
    try {
      const response = await apiService.post('/media/bulk-delete', { ids })
      return response
    } catch (error) {
      console.error('Error bulk deleting media:', error)
      throw error
    }
  }

  /**
   * Get media stats
   */
  async getMediaStats() {
    try {
      const response = await apiService.get('/media/stats')
      return response
    } catch (error) {
      console.error('Error fetching media stats:', error)
      throw error
    }
  }

  /**
   * Get all tags
   */
  async getAllTags() {
    try {
      const response = await apiService.get('/media/tags')
      return response
    } catch (error) {
      console.error('Error fetching media tags:', error)
      throw error
    }
  }

  /**
   * Track media usage
   */
  async trackMediaUsage(mediaId, usage) {
    try {
      const response = await apiService.post(`/media/${mediaId}/track-usage`, usage)
      return response
    } catch (error) {
      console.error('Error tracking media usage:', error)
      throw error
    }
  }

  /**
   * Remove media usage
   */
  async removeMediaUsage(mediaId, usage) {
    try {
      const response = await apiService.delete(`/media/${mediaId}/remove-usage`, { data: usage })
      return response
    } catch (error) {
      console.error('Error removing media usage:', error)
      throw error
    }
  }

  /**
   * Search media with advanced filters
   */
  async searchMedia(query, filters = {}) {
    try {
      const params = {
        search: query,
        ...filters
      }

      return await this.getMediaLibrary(params)
    } catch (error) {
      console.error('Error searching media:', error)
      throw error
    }
  }

  /**
   * Get media by type
   */
  async getMediaByType(type, params = {}) {
    try {
      const mimeTypeMap = {
        images: 'image/',
        videos: 'video/',
        documents: 'application/',
        audio: 'audio/'
      }

      const mimeType = mimeTypeMap[type] || ''
      
      return await this.getMediaLibrary({
        ...params,
        mimeType
      })
    } catch (error) {
      console.error('Error fetching media by type:', error)
      throw error
    }
  }

  /**
   * Get recently uploaded media
   */
  async getRecentMedia(limit = 10) {
    try {
      return await this.getMediaLibrary({
        limit,
        page: 1
      })
    } catch (error) {
      console.error('Error fetching recent media:', error)
      throw error
    }
  }

  /**
   * Get media usage information
   */
  async getMediaUsage(mediaId) {
    try {
      const response = await apiService.get(`/media/${mediaId}/usage`)
      return response
    } catch (error) {
      console.error('Error fetching media usage:', error)
      throw error
    }
  }

  /**
   * Optimize image URL with Cloudinary transformations
   */
  getOptimizedImageUrl(imageUrl, options = {}) {
    try {
      const {
        width,
        height,
        quality = 'auto',
        format = 'auto',
        crop = 'fill'
      } = options

      // If it's not a Cloudinary URL, return as is
      if (!imageUrl || !imageUrl.includes('cloudinary.com')) {
        return imageUrl
      }

      // Extract the public ID and build optimized URL
      const parts = imageUrl.split('/')
      const uploadIndex = parts.findIndex(part => part === 'upload')
      
      if (uploadIndex === -1) return imageUrl

      const transformations = []
      if (width) transformations.push(`w_${width}`)
      if (height) transformations.push(`h_${height}`)
      if (quality) transformations.push(`q_${quality}`)
      if (format) transformations.push(`f_${format}`)
      if (crop) transformations.push(`c_${crop}`)

      if (transformations.length === 0) return imageUrl

      const transformationString = transformations.join(',')
      parts.splice(uploadIndex + 1, 0, transformationString)

      return parts.join('/')
    } catch (error) {
      console.error('Error optimizing image URL:', error)
      return imageUrl
    }
  }

  /**
   * Generate thumbnail URL
   */
  getThumbnailUrl(imageUrl, size = 150) {
    return this.getOptimizedImageUrl(imageUrl, {
      width: size,
      height: size,
      crop: 'fill',
      quality: 'auto',
      format: 'auto'
    })
  }

  /**
   * Generate responsive image URLs
   */
  getResponsiveImageUrls(imageUrl) {
    const sizes = [320, 640, 768, 1024, 1280, 1920]
    
    return sizes.reduce((acc, size) => {
      acc[`w${size}`] = this.getOptimizedImageUrl(imageUrl, {
        width: size,
        quality: 'auto',
        format: 'auto'
      })
      return acc
    }, {})
  }
}

export const mediaService = new MediaService()
export default mediaService
