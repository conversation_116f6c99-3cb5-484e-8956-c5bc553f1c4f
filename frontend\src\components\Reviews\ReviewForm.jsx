import { useState } from 'react';
import { FiStar } from 'react-icons/fi';
import { useToast } from '../../contexts/ToastContext';
import apiService from '../../services/api';

const ReviewForm = ({
  onSubmit,
  onSuccess,
  onCancel,
  branding,
  productId,
  serviceId,
  initialData = null,
  isEditing = false
}) => {
  const { showSuccess, showError } = useToast();
  const [formData, setFormData] = useState({
    rating: initialData?.rating || 0,
    title: initialData?.title || '',
    comment: initialData?.comment || ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (formData.rating === 0) {
      newErrors.rating = 'Please select a rating';
    }

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters';
    }

    if (!formData.comment.trim()) {
      newErrors.comment = 'Comment is required';
    } else if (formData.comment.length > 1000) {
      newErrors.comment = 'Comment must be less than 1000 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const reviewData = {
        ...formData
      };

      // Only include the relevant ID (product OR service, not both)
      if (productId) {
        reviewData.product = productId;
      } else if (serviceId) {
        reviewData.service = serviceId;
      }

      if (onSubmit) {
        // Use custom submit handler if provided
        await onSubmit(reviewData);
      } else {
        // Submit directly to API
        const response = await apiService.post('/reviews', reviewData);
        if (!response.success) {
          throw new Error(response.message || 'Failed to submit review');
        }
      }

      if (isEditing) {
        showSuccess(branding?.reviews?.successMessage || 'Review updated successfully!');
      } else {
        showSuccess(branding?.reviews?.pendingApprovalMessage || 'Review submitted successfully and is pending approval!');
      }

      // Reset form if not editing
      if (!isEditing) {
        setFormData({ rating: 0, title: '', comment: '' });
      }

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      showError(`Failed to ${isEditing ? 'update' : 'submit'} review: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRatingClick = (rating) => {
    setFormData(prev => ({ ...prev, rating }));
    if (errors.rating) {
      setErrors(prev => ({ ...prev, rating: null }));
    }
  };

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
      <h3 className="text-xl font-semibold text-gray-900 mb-4">
        {isEditing 
          ? (branding?.reviews?.editReviewTitle || 'Edit Your Review')
          : (branding?.reviews?.formTitle || 'Write Your Review')
        }
      </h3>
      
      {!isEditing && (
        <p className="text-gray-600 mb-6">
          {branding?.reviews?.formSubtitle || 'Share your experience with others'}
        </p>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Rating */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {branding?.reviews?.ratingLabel || 'Rating'} *
          </label>
          <div className="flex items-center space-x-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                type="button"
                onClick={() => handleRatingClick(star)}
                className={`p-1 transition-colors duration-200 ${
                  star <= formData.rating
                    ? 'text-yellow-400'
                    : 'text-gray-300 hover:text-yellow-300'
                }`}
              >
                <FiStar 
                  className="w-8 h-8" 
                  fill={star <= formData.rating ? 'currentColor' : 'none'}
                />
              </button>
            ))}
          </div>
          {errors.rating && (
            <p className="mt-1 text-sm text-red-600">{errors.rating}</p>
          )}
        </div>

        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {branding?.reviews?.titleLabel || 'Review Title'} *
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            placeholder={branding?.reviews?.titlePlaceholder || 'Summarize your experience'}
            className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
              errors.title ? 'border-red-300' : 'border-gray-300'
            }`}
            maxLength={100}
          />
          <div className="flex justify-between mt-1">
            {errors.title && (
              <p className="text-sm text-red-600">{errors.title}</p>
            )}
            <p className="text-xs text-gray-500 ml-auto">
              {formData.title.length}/100
            </p>
          </div>
        </div>

        {/* Comment */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {branding?.reviews?.commentLabel || 'Your Review'} *
          </label>
          <textarea
            value={formData.comment}
            onChange={(e) => setFormData(prev => ({ ...prev, comment: e.target.value }))}
            placeholder={branding?.reviews?.commentPlaceholder || 'Tell us about your experience with this product or service...'}
            rows={5}
            className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none ${
              errors.comment ? 'border-red-300' : 'border-gray-300'
            }`}
            maxLength={1000}
          />
          <div className="flex justify-between mt-1">
            {errors.comment && (
              <p className="text-sm text-red-600">{errors.comment}</p>
            )}
            <p className="text-xs text-gray-500 ml-auto">
              {formData.comment.length}/1000
            </p>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors duration-200"
              disabled={isSubmitting}
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-xl hover:from-blue-700 hover:to-purple-700 focus:ring-4 focus:ring-blue-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting 
              ? (isEditing 
                  ? (branding?.reviews?.updatingText || 'Updating...')
                  : (branding?.reviews?.submittingText || 'Submitting...')
                )
              : (isEditing 
                  ? (branding?.reviews?.updateButton || 'Update Review')
                  : (branding?.reviews?.submitButton || 'Submit Review')
                )
            }
          </button>
        </div>
      </form>
    </div>
  );
};

export default ReviewForm;
